# ===========================================
# VoiceHub 项目 .gitignore 配置
# ===========================================

# ===========================================
# Java / Maven 相关
# ===========================================
# Maven 构建目录
/**/target/**
**/target/

# Maven 包装器
.mvn/wrapper/maven-wrapper.jar
.mvn/wrapper/maven-wrapper.properties
.mvn/wrapper/MavenWrapperDownloader.java

# IDE 相关文件
/**/.project
/**/.classpath
/**/.settings/**
*.iml
*.ipr
*.iws
.idea/
.vscode/
/**/.cursor/**

# Eclipse 相关
.metadata
bin/
tmp/
*.tmp
*.bak
*.swp
*~.nib
local.properties
.loadpath
.recommenders

# ===========================================
# Node.js / React 相关
# ===========================================
# 依赖目录
node_modules/
/.pnp
.pnp.js

# 构建产物
/build
/dist
/coverage

# 缓存
.npm
.eslintcache
.stylelintcache

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# ===========================================
# 环境配置文件
# ===========================================
# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.*.local

# 配置文件
config/local.json
config/production.json

# ===========================================
# 日志文件
# ===========================================
*.log
logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# ===========================================
# 操作系统相关
# ===========================================
# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ===========================================
# 开发工具相关
# ===========================================
# JetBrains IDEs
out/

# Visual Studio Code
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# Sublime Text
*.tmlanguage.cache
*.tmPreferences.cache
*.stTheme.cache
*.sublime-workspace
*.sublime-project

# ===========================================
# 数据库相关
# ===========================================
# 数据库文件
*.db
*.sqlite
*.sqlite3

# ===========================================
# Docker 相关
# ===========================================
# Docker 临时文件
.dockerignore

# ===========================================
# 安全相关
# ===========================================
# 密钥文件
*.key
*.pem
*.p12
*.jks
*.keystore

# 证书文件
*.crt
*.cer

# ===========================================
# 临时文件和缓存
# ===========================================
# 通用临时文件
*.temp
*.cache

# 自定义临时文件
*._gs
*.gsl
*_gs

# ===========================================
# 测试相关
# ===========================================
# 测试覆盖率报告
coverage/
*.lcov

# 测试结果
test-results/
junit.xml

# ===========================================
# 部署相关
# ===========================================
# 部署脚本生成的文件
deploy/
*.war
*.jar
!**/src/main/**/target/
!**/src/test/**/target/

# ===========================================
# 其他
# ===========================================
# 备份文件
*.backup
*.old

# 压缩文件
*.7z
*.dmg
*.gz
*.iso
*.rar
*.tar
*.zip

# 编辑器临时文件
.#*
*#
