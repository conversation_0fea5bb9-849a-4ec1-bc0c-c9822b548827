# VoiceHub 智能语音助手平台 - 部署指南

## 📋 目录

- [系统要求](#系统要求)
- [快速开始](#快速开始)
- [环境配置](#环境配置)
- [本地开发部署](#本地开发部署)
- [生产环境部署](#生产环境部署)
- [云平台部署](#云平台部署)
- [监控和维护](#监控和维护)
- [故障排除](#故障排除)
- [性能优化](#性能优化)

## 🔧 系统要求

### 最低配置要求
- **CPU**: 2核心 2.0GHz
- **内存**: 4GB RAM
- **存储**: 20GB 可用空间
- **网络**: 稳定的互联网连接

### 推荐配置要求
- **CPU**: 4核心 2.5GHz+
- **内存**: 8GB+ RAM
- **存储**: 50GB+ SSD
- **网络**: 100Mbps+ 带宽

### 软件依赖
- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **Java**: 17+ (开发环境)
- **Node.js**: 18+ (开发环境)
- **PostgreSQL**: 14+ (生产环境)
- **Redis**: 6.0+ (生产环境)

## 🚀 快速开始

### 1. 克隆项目
```bash
git clone https://github.com/your-org/voicehub.git
cd voicehub
```

### 2. 环境配置
```bash
# 复制环境变量模板
cp docker/.env.example docker/.env

# 编辑环境变量
vim docker/.env
```

### 3. 一键部署
```bash
# 开发环境
./scripts/deploy.sh dev

# 生产环境
./scripts/deploy.sh prod
```

### 4. 访问应用
- **前端应用**: http://localhost:3000
- **后端API**: http://localhost:8080
- **API文档**: http://localhost:8080/swagger-ui.html
- **监控面板**: http://localhost:3001 (生产环境)

## ⚙️ 环境配置

### 环境变量说明

#### 数据库配置
```bash
# PostgreSQL 数据库
DB_HOST=postgres
DB_PORT=5432
DB_NAME=voicehub
DB_USERNAME=voicehub_user
DB_PASSWORD=your_secure_password

# Redis 缓存
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password
```

#### 应用配置
```bash
# 应用基础配置
APP_NAME=VoiceHub
APP_VERSION=1.0.0
APP_ENV=production
APP_DEBUG=false

# 服务端口
BACKEND_PORT=8080
FRONTEND_PORT=3000
NGINX_PORT=80
NGINX_SSL_PORT=443
```

#### AI服务配置
```bash
# OpenAI API
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-3.5-turbo
OPENAI_MAX_TOKENS=1000

# 语音服务
SPEECH_TO_TEXT_API_KEY=your_stt_api_key
TEXT_TO_SPEECH_API_KEY=your_tts_api_key
SPEECH_SERVICE_PROVIDER=azure  # azure, google, aws
```

#### 安全配置
```bash
# JWT 配置
JWT_SECRET=your_jwt_secret_key_minimum_32_characters
JWT_EXPIRATION=86400  # 24小时

# SSL 证书 (生产环境)
SSL_CERT_PATH=/etc/ssl/certs/voicehub.crt
SSL_KEY_PATH=/etc/ssl/private/voicehub.key
```

#### 监控配置
```bash
# Prometheus 监控
PROMETHEUS_PORT=9090
GRAFANA_PORT=3001
GRAFANA_ADMIN_PASSWORD=your_grafana_password

# 日志配置
LOG_LEVEL=INFO
LOG_MAX_SIZE=100MB
LOG_MAX_FILES=10
```

## 🏠 本地开发部署

### 方式一：Docker Compose (推荐)

```bash
# 启动开发环境
docker-compose -f docker/docker-compose.yml up -d

# 查看服务状态
docker-compose -f docker/docker-compose.yml ps

# 查看日志
docker-compose -f docker/docker-compose.yml logs -f

# 停止服务
docker-compose -f docker/docker-compose.yml down
```

### 方式二：本地运行

#### 后端服务
```bash
cd backend/voicehub-backend

# 安装依赖
./mvnw clean install

# 启动数据库 (Docker)
docker run -d --name postgres \
  -e POSTGRES_DB=voicehub \
  -e POSTGRES_USER=voicehub_user \
  -e POSTGRES_PASSWORD=password \
  -p 5432:5432 postgres:14

# 启动Redis (Docker)
docker run -d --name redis \
  -p 6379:6379 redis:6-alpine

# 启动后端服务
./mvnw spring-boot:run
```

#### 前端服务
```bash
cd frontend/voicehub-ui

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

## 🏭 生产环境部署

### 使用部署脚本 (推荐)

```bash
# 生产环境一键部署
./scripts/deploy.sh prod

# 查看部署状态
./scripts/deploy.sh status

# 查看服务日志
./scripts/deploy.sh logs

# 备份数据
./scripts/deploy.sh backup

# 停止服务
./scripts/deploy.sh stop
```

### 手动部署步骤

#### 1. 准备环境
```bash
# 创建生产环境目录
mkdir -p /opt/voicehub
cd /opt/voicehub

# 复制项目文件
cp -r /path/to/voicehub/* .

# 设置环境变量
cp docker/.env.example docker/.env
# 编辑 docker/.env 文件，设置生产环境配置
```

#### 2. 构建镜像
```bash
# 构建后端镜像
docker build -t voicehub-backend:latest backend/voicehub-backend/

# 构建前端镜像
docker build -t voicehub-frontend:latest frontend/voicehub-ui/
```

#### 3. 启动服务
```bash
# 启动生产环境
docker-compose -f docker/docker-compose.prod.yml up -d

# 等待服务启动
sleep 30

# 检查服务健康状态
curl http://localhost/api/health
```

#### 4. 配置反向代理 (Nginx)
```nginx
# /etc/nginx/sites-available/voicehub
server {
    listen 80;
    server_name your-domain.com;
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    # SSL 配置
    ssl_certificate /path/to/ssl/cert.pem;
    ssl_certificate_key /path/to/ssl/private.key;
    
    # 前端静态文件
    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 后端API
    location /api/ {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # WebSocket 支持
    location /ws/ {
        proxy_pass http://localhost:8080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## ☁️ 云平台部署

### AWS 部署

#### 1. ECS Fargate 部署
```bash
# 登录 AWS ECR
aws ecr get-login-password --region us-west-2 | docker login --username AWS --password-stdin 123456789012.dkr.ecr.us-west-2.amazonaws.com

# 构建并推送镜像
docker build -t voicehub-backend backend/voicehub-backend/
docker tag voicehub-backend:latest 123456789012.dkr.ecr.us-west-2.amazonaws.com/voicehub-backend:latest
docker push 123456789012.dkr.ecr.us-west-2.amazonaws.com/voicehub-backend:latest

# 部署 ECS 服务
aws ecs create-service --cli-input-json file://cloud/aws/ecs-service.json
```

#### 2. RDS 数据库设置
```bash
# 创建 RDS PostgreSQL 实例
aws rds create-db-instance \
    --db-instance-identifier voicehub-db \
    --db-instance-class db.t3.micro \
    --engine postgres \
    --master-username voicehub_user \
    --master-user-password your_secure_password \
    --allocated-storage 20 \
    --vpc-security-group-ids sg-12345678
```

#### 3. ElastiCache Redis 设置
```bash
# 创建 Redis 集群
aws elasticache create-cache-cluster \
    --cache-cluster-id voicehub-redis \
    --cache-node-type cache.t3.micro \
    --engine redis \
    --num-cache-nodes 1
```

### Google Cloud Platform 部署

#### 1. GKE 部署
```bash
# 创建 GKE 集群
gcloud container clusters create voicehub-cluster \
    --zone us-central1-a \
    --num-nodes 3 \
    --machine-type e2-medium

# 部署应用
kubectl apply -f cloud/gcp/k8s-deployment.yaml
```

#### 2. Cloud SQL 设置
```bash
# 创建 Cloud SQL PostgreSQL 实例
gcloud sql instances create voicehub-db \
    --database-version POSTGRES_14 \
    --tier db-f1-micro \
    --region us-central1
```

### Azure 部署

#### 1. Container Instances 部署
```bash
# 创建资源组
az group create --name voicehub-rg --location eastus

# 部署容器实例
az container create \
    --resource-group voicehub-rg \
    --name voicehub-app \
    --image voicehub-backend:latest \
    --ports 8080 \
    --environment-variables \
        DB_HOST=voicehub-db.postgres.database.azure.com \
        DB_USERNAME=voicehub_user \
        DB_PASSWORD=your_password
```

## 📊 监控和维护

### Prometheus 监控配置

```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'voicehub-backend'
    static_configs:
      - targets: ['backend:8080']
    metrics_path: '/actuator/prometheus'
    
  - job_name: 'voicehub-frontend'
    static_configs:
      - targets: ['frontend:3000']
```

### Grafana 仪表板

主要监控指标：
- **应用性能**: 响应时间、吞吐量、错误率
- **系统资源**: CPU、内存、磁盘、网络使用率
- **数据库性能**: 连接数、查询时间、锁等待
- **语音服务**: 识别准确率、处理延迟、API调用量

### 日志管理

```bash
# 查看应用日志
docker-compose logs -f backend

# 查看特定时间段日志
docker-compose logs --since="2024-01-01T00:00:00" backend

# 导出日志到文件
docker-compose logs backend > voicehub-backend.log
```

### 数据备份

#### 自动备份脚本
```bash
#!/bin/bash
# backup.sh

BACKUP_DIR="/opt/backups/voicehub"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
docker exec postgres pg_dump -U voicehub_user voicehub > $BACKUP_DIR/db_backup_$DATE.sql

# 备份文件存储
tar -czf $BACKUP_DIR/files_backup_$DATE.tar.gz /opt/voicehub/data

# 清理旧备份 (保留7天)
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete

echo "备份完成: $DATE"
```

#### 设置定时备份
```bash
# 添加到 crontab
crontab -e

# 每天凌晨2点执行备份
0 2 * * * /opt/voicehub/scripts/backup.sh
```

## 🔧 故障排除

### 常见问题及解决方案

#### 1. 服务启动失败
```bash
# 检查容器状态
docker-compose ps

# 查看错误日志
docker-compose logs backend

# 重启服务
docker-compose restart backend
```

#### 2. 数据库连接问题
```bash
# 检查数据库连接
docker exec -it postgres psql -U voicehub_user -d voicehub

# 检查网络连接
docker network ls
docker network inspect voicehub_default
```

#### 3. 语音服务异常
```bash
# 检查API密钥配置
echo $OPENAI_API_KEY

# 测试API连接
curl -H "Authorization: Bearer $OPENAI_API_KEY" \
     https://api.openai.com/v1/models
```

#### 4. 前端页面无法访问
```bash
# 检查Nginx配置
docker exec nginx nginx -t

# 重新加载Nginx配置
docker exec nginx nginx -s reload

# 检查端口占用
netstat -tulpn | grep :80
```

### 性能问题诊断

#### 1. 应用性能分析
```bash
# 查看JVM内存使用
docker exec backend jstat -gc 1

# 查看线程状态
docker exec backend jstack 1

# 查看系统资源使用
docker stats
```

#### 2. 数据库性能优化
```sql
-- 查看慢查询
SELECT query, mean_time, calls 
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;

-- 查看数据库连接
SELECT * FROM pg_stat_activity;

-- 分析表统计信息
ANALYZE;
```

## ⚡ 性能优化

### 应用层优化

#### 1. JVM 参数调优
```bash
# 在 docker-compose.yml 中设置
environment:
  - JAVA_OPTS=-Xms2g -Xmx4g -XX:+UseG1GC -XX:MaxGCPauseMillis=200
```

#### 2. 连接池优化
```yaml
# application.yml
spring:
  datasource:
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
```

#### 3. Redis 缓存优化
```yaml
spring:
  redis:
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5
```

### 数据库优化

#### 1. 索引优化
```sql
-- 创建复合索引
CREATE INDEX idx_user_created_at ON users(user_id, created_at);

-- 创建部分索引
CREATE INDEX idx_active_users ON users(email) WHERE active = true;
```

#### 2. 查询优化
```sql
-- 使用 EXPLAIN ANALYZE 分析查询
EXPLAIN ANALYZE SELECT * FROM conversations WHERE user_id = 1;

-- 优化分页查询
SELECT * FROM voice_notes 
WHERE user_id = 1 
ORDER BY created_at DESC 
LIMIT 20 OFFSET 0;
```

### 网络优化

#### 1. CDN 配置
```nginx
# 静态资源缓存
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

#### 2. Gzip 压缩
```nginx
# 启用 Gzip 压缩
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css application/json application/javascript text/xml application/xml;
```

## 🔒 安全配置

### SSL/TLS 配置
```bash
# 生成自签名证书 (开发环境)
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout ssl/private.key -out ssl/cert.crt

# 使用 Let's Encrypt (生产环境)
certbot --nginx -d your-domain.com
```

### 防火墙配置
```bash
# UFW 防火墙规则
ufw allow 22/tcp    # SSH
ufw allow 80/tcp    # HTTP
ufw allow 443/tcp   # HTTPS
ufw enable
```

### 安全头配置
```nginx
# 安全头设置
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header X-Content-Type-Options "nosniff" always;
add_header Referrer-Policy "no-referrer-when-downgrade" always;
add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
```

## 📞 技术支持

如果在部署过程中遇到问题，请：

1. **查看日志**: 首先检查应用和系统日志
2. **检查配置**: 确认环境变量和配置文件正确
3. **网络连接**: 验证服务间网络连通性
4. **资源使用**: 检查系统资源是否充足
5. **版本兼容**: 确认软件版本兼容性

### 联系方式
- **技术文档**: [项目Wiki](https://github.com/your-org/voicehub/wiki)
- **问题反馈**: [GitHub Issues](https://github.com/your-org/voicehub/issues)
- **技术支持**: <EMAIL>

---

**祝您部署顺利！** 🚀