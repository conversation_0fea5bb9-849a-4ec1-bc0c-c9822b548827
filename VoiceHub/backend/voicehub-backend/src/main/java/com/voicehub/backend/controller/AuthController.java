package com.voicehub.backend.controller;

import com.voicehub.backend.dto.AuthResponse;
import com.voicehub.backend.dto.LoginRequest;
import com.voicehub.backend.dto.RegisterRequest;
import com.voicehub.backend.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import javax.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 认证控制器
 * 处理用户登录、注册等认证相关请求
 */
@RestController
@RequestMapping("/auth")
@Tag(name = "认证管理", description = "用户认证相关接口")
public class AuthController {

    private static final Logger logger = LoggerFactory.getLogger(AuthController.class);

    @Autowired
    private UserService userService;

    /**
     * 用户注册
     */
    @PostMapping("/register")
    @Operation(summary = "用户注册", description = "创建新用户账户")
    public ResponseEntity<?> register(@Valid @RequestBody RegisterRequest request) {
        try {
            logger.info("用户注册请求: {}", request.getUsername());
            
            AuthResponse response = userService.register(request);
            
            logger.info("用户注册成功: {}", request.getUsername());
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("用户注册失败: {}", e.getMessage());
            
            Map<String, String> error = new HashMap<>();
            error.put("error", "注册失败");
            error.put("message", e.getMessage());
            
            return ResponseEntity.badRequest().body(error);
        }
    }

    /**
     * 用户登录
     */
    @PostMapping("/login")
    @Operation(summary = "用户登录", description = "用户身份验证")
    public ResponseEntity<?> login(@Valid @RequestBody LoginRequest request) {
        try {
            logger.info("用户登录请求: {}", request.getUsernameOrEmail());
            
            AuthResponse response = userService.login(request);
            
            logger.info("用户登录成功: {}", request.getUsernameOrEmail());
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("用户登录失败: {}", e.getMessage());
            
            Map<String, String> error = new HashMap<>();
            error.put("error", "登录失败");
            error.put("message", e.getMessage());
            
            return ResponseEntity.badRequest().body(error);
        }
    }

    /**
     * 检查用户名可用性
     */
    @GetMapping("/check-username")
    @Operation(summary = "检查用户名", description = "检查用户名是否可用")
    public ResponseEntity<Map<String, Boolean>> checkUsername(@RequestParam String username) {
        boolean available = userService.isUsernameAvailable(username);
        
        Map<String, Boolean> response = new HashMap<>();
        response.put("available", available);
        
        return ResponseEntity.ok(response);
    }

    /**
     * 检查邮箱可用性
     */
    @GetMapping("/check-email")
    @Operation(summary = "检查邮箱", description = "检查邮箱是否可用")
    public ResponseEntity<Map<String, Boolean>> checkEmail(@RequestParam String email) {
        boolean available = userService.isEmailAvailable(email);
        
        Map<String, Boolean> response = new HashMap<>();
        response.put("available", available);
        
        return ResponseEntity.ok(response);
    }

    /**
     * 获取当前用户信息
     */
    @GetMapping("/me")
    @Operation(summary = "获取当前用户", description = "获取当前登录用户的信息")
    public ResponseEntity<?> getCurrentUser() {
        // 这个方法将在后续实现，需要从SecurityContext中获取当前用户
        Map<String, String> response = new HashMap<>();
        response.put("message", "功能开发中");
        return ResponseEntity.ok(response);
    }
}