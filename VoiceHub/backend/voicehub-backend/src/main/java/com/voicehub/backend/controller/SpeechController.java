package com.voicehub.backend.controller;

import com.voicehub.backend.service.SpeechToTextService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;

/**
 * 语音处理控制器
 * 处理语音识别和转换相关请求
 */
@RestController
@RequestMapping("/speech")
@Tag(name = "语音处理", description = "语音识别和转换相关接口")
@PreAuthorize("hasRole('USER')")
public class SpeechController {

    private static final Logger logger = LoggerFactory.getLogger(SpeechController.class);

    @Autowired
    private SpeechToTextService speechToTextService;

    /**
     * 语音转文本接口
     */
    @PostMapping("/recognize")
    @Operation(summary = "语音识别", description = "将语音文件转换为文本")
    public ResponseEntity<?> recognizeSpeech(
            @RequestParam("audio") MultipartFile audioFile,
            @RequestParam(value = "format", defaultValue = "wav") String format,
            @RequestParam(value = "rate", defaultValue = "16000") int sampleRate) {
        
        try {
            logger.info("Received speech recognition request: {} bytes, format: {}, rate: {}", 
                       audioFile.getSize(), format, sampleRate);

            // 验证文件
            if (audioFile.isEmpty()) {
                return ResponseEntity.badRequest().body(createErrorResponse("音频文件不能为空"));
            }

            // 验证文件大小 (最大10MB)
            if (audioFile.getSize() > 10 * 1024 * 1024) {
                return ResponseEntity.badRequest().body(createErrorResponse("音频文件大小不能超过10MB"));
            }

            // 验证音频格式
            if (!speechToTextService.isSupportedFormat(format)) {
                return ResponseEntity.badRequest().body(createErrorResponse("不支持的音频格式: " + format));
            }

            // 获取音频数据
            byte[] audioData = audioFile.getBytes();

            // 执行语音识别
            String recognizedText = speechToTextService.convertSpeechToText(audioData, format, sampleRate);

            if (recognizedText != null && !recognizedText.trim().isEmpty()) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", true);
                response.put("text", recognizedText.trim());
                response.put("confidence", 0.95); // 模拟置信度
                response.put("language", "zh-CN");
                response.put("duration", estimateAudioDuration(audioData.length, sampleRate));

                logger.info("Speech recognition successful: {}", recognizedText);
                return ResponseEntity.ok(response);
            } else {
                return ResponseEntity.ok(createErrorResponse("未识别到语音内容"));
            }

        } catch (Exception e) {
            logger.error("Speech recognition failed: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("语音识别失败: " + e.getMessage()));
        }
    }

    /**
     * 获取支持的音频格式
     */
    @GetMapping("/formats")
    @Operation(summary = "支持的格式", description = "获取支持的音频格式列表")
    public ResponseEntity<Map<String, Object>> getSupportedFormats() {
        Map<String, Object> response = new HashMap<>();
        
        String[] formats = {"wav", "pcm", "opus", "amr", "m4a"};
        Map<String, Integer> formatRates = new HashMap<>();
        
        for (String format : formats) {
            formatRates.put(format, speechToTextService.getRecommendedSampleRate(format));
        }
        
        response.put("supportedFormats", formats);
        response.put("recommendedSampleRates", formatRates);
        response.put("maxFileSize", "10MB");
        
        return ResponseEntity.ok(response);
    }

    /**
     * 语音识别状态检查
     */
    @GetMapping("/status")
    @Operation(summary = "服务状态", description = "检查语音识别服务状态")
    public ResponseEntity<Map<String, Object>> getServiceStatus() {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "active");
        response.put("service", "Baidu Speech Recognition");
        response.put("version", "1.0");
        response.put("supportedLanguages", new String[]{"zh-CN", "en-US"});
        
        return ResponseEntity.ok(response);
    }

    /**
     * 实时语音识别WebSocket端点信息
     */
    @GetMapping("/websocket-info")
    @Operation(summary = "WebSocket信息", description = "获取实时语音识别WebSocket连接信息")
    public ResponseEntity<Map<String, Object>> getWebSocketInfo() {
        Map<String, Object> response = new HashMap<>();
        response.put("endpoint", "/ws");
        response.put("destination", "/app/speech/recognize");
        response.put("subscribe", "/user/queue/speech/result");
        response.put("protocols", new String[]{"STOMP", "SockJS"});
        
        return ResponseEntity.ok(response);
    }

    /**
     * 创建错误响应
     */
    private Map<String, Object> createErrorResponse(String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("error", message);
        response.put("text", "");
        return response;
    }

    /**
     * 估算音频时长（秒）
     */
    private double estimateAudioDuration(int audioDataLength, int sampleRate) {
        // 简单估算：假设16位单声道
        int bytesPerSecond = sampleRate * 2; // 16位 = 2字节
        return (double) audioDataLength / bytesPerSecond;
    }
}