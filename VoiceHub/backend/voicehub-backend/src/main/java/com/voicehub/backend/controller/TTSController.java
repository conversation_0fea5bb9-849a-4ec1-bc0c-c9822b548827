package com.voicehub.backend.controller;

import com.voicehub.backend.entity.User;
import com.voicehub.backend.service.TextToSpeechService;
import com.voicehub.backend.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * Text-to-Speech Controller
 * REST API endpoints for TTS functionality
 */
@RestController
@RequestMapping("/api/tts")
@Tag(name = "Text-to-Speech", description = "Text-to-Speech synthesis APIs")
@PreAuthorize("hasRole('USER')")
public class TTSController {

    private static final Logger logger = LoggerFactory.getLogger(TTSController.class);

    @Autowired
    private TextToSpeechService ttsService;

    @Autowired
    private UserService userService;

    /**
     * Synthesize speech from text
     */
    @PostMapping("/synthesize")
    @Operation(summary = "Synthesize Speech", description = "Convert text to speech audio")
    public ResponseEntity<?> synthesizeSpeech(
            @RequestBody Map<String, Object> request,
            Authentication authentication) {
        
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            
            String text = (String) request.get("text");
            if (text == null || text.trim().isEmpty()) {
                return ResponseEntity.badRequest().body(createErrorResponse("Text is required"));
            }

            // Build TTS request
            TextToSpeechService.TTSRequest ttsRequest = new TextToSpeechService.TTSRequest(text);
            
            // Optional parameters
            if (request.containsKey("voice")) {
                String voiceStr = (String) request.get("voice");
                try {
                    TextToSpeechService.Voice voice = TextToSpeechService.Voice.valueOf(voiceStr.toUpperCase());
                    ttsRequest.setVoice(voice);
                } catch (IllegalArgumentException e) {
                    return ResponseEntity.badRequest().body(createErrorResponse("Invalid voice: " + voiceStr));
                }
            }

            if (request.containsKey("format")) {
                String formatStr = (String) request.get("format");
                try {
                    TextToSpeechService.AudioFormat format = TextToSpeechService.AudioFormat.valueOf(formatStr.toUpperCase());
                    ttsRequest.setFormat(format);
                } catch (IllegalArgumentException e) {
                    return ResponseEntity.badRequest().body(createErrorResponse("Invalid format: " + formatStr));
                }
            }

            if (request.containsKey("speed")) {
                Integer speed = (Integer) request.get("speed");
                if (speed != null) ttsRequest.setSpeed(speed);
            }

            if (request.containsKey("pitch")) {
                Integer pitch = (Integer) request.get("pitch");
                if (pitch != null) ttsRequest.setPitch(pitch);
            }

            if (request.containsKey("volume")) {
                Integer volume = (Integer) request.get("volume");
                if (volume != null) ttsRequest.setVolume(volume);
            }

            if (request.containsKey("language")) {
                String language = (String) request.get("language");
                if (language != null) ttsRequest.setLanguage(language);
            }

            // Synthesize speech
            TextToSpeechService.TTSResult result = ttsService.synthesizeSpeech(ttsRequest);

            // Return audio as response
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType(result.getMimeType()));
            headers.setContentLength(result.getFileSize());
            headers.set("Content-Disposition", "inline; filename=\"speech." + result.getAudioFormat() + "\"");

            Resource resource = new ByteArrayResource(result.getAudioData());

            logger.info("Successfully synthesized speech for user: {}, text length: {}", 
                user.getUsername(), text.length());

            return ResponseEntity.ok()
                .headers(headers)
                .body(resource);

        } catch (IOException e) {
            logger.error("TTS synthesis failed: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Speech synthesis failed: " + e.getMessage()));
        } catch (Exception e) {
            logger.error("TTS request failed: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Request failed: " + e.getMessage()));
        }
    }

    /**
     * Synthesize speech and return as Base64
     */
    @PostMapping("/synthesize-base64")
    @Operation(summary = "Synthesize Speech as Base64", description = "Convert text to speech and return as Base64 encoded audio")
    public ResponseEntity<?> synthesizeSpeechBase64(
            @RequestBody Map<String, Object> request,
            Authentication authentication) {
        
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            
            String text = (String) request.get("text");
            if (text == null || text.trim().isEmpty()) {
                return ResponseEntity.badRequest().body(createErrorResponse("Text is required"));
            }

            String voiceStr = (String) request.getOrDefault("voice", "FEMALE_STANDARD");
            TextToSpeechService.Voice voice;
            try {
                voice = TextToSpeechService.Voice.valueOf(voiceStr.toUpperCase());
            } catch (IllegalArgumentException e) {
                voice = TextToSpeechService.Voice.FEMALE_STANDARD;
            }

            // Synthesize and encode
            String base64Audio = ttsService.synthesizeToBase64(text, voice);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Speech synthesized successfully");
            response.put("audioData", base64Audio);
            response.put("voice", voice.getName());
            response.put("textLength", text.length());

            logger.info("Successfully synthesized speech as Base64 for user: {}", user.getUsername());
            return ResponseEntity.ok(response);

        } catch (IOException e) {
            logger.error("TTS synthesis failed: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Speech synthesis failed: " + e.getMessage()));
        } catch (Exception e) {
            logger.error("TTS request failed: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Request failed: " + e.getMessage()));
        }
    }

    /**
     * Synthesize speech and save to file
     */
    @PostMapping("/synthesize-file")
    @Operation(summary = "Synthesize and Save Speech", description = "Convert text to speech and save as file")
    public ResponseEntity<?> synthesizeAndSaveFile(
            @RequestBody Map<String, Object> request,
            Authentication authentication) {
        
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            
            String text = (String) request.get("text");
            if (text == null || text.trim().isEmpty()) {
                return ResponseEntity.badRequest().body(createErrorResponse("Text is required"));
            }

            // Build TTS request
            TextToSpeechService.TTSRequest ttsRequest = new TextToSpeechService.TTSRequest(text);
            
            // Set optional parameters (same as synthesize endpoint)
            if (request.containsKey("voice")) {
                String voiceStr = (String) request.get("voice");
                try {
                    TextToSpeechService.Voice voice = TextToSpeechService.Voice.valueOf(voiceStr.toUpperCase());
                    ttsRequest.setVoice(voice);
                } catch (IllegalArgumentException e) {
                    // Use default voice
                }
            }

            if (request.containsKey("format")) {
                String formatStr = (String) request.get("format");
                try {
                    TextToSpeechService.AudioFormat format = TextToSpeechService.AudioFormat.valueOf(formatStr.toUpperCase());
                    ttsRequest.setFormat(format);
                } catch (IllegalArgumentException e) {
                    // Use default format
                }
            }

            // Synthesize and save
            TextToSpeechService.TTSResult result = ttsService.synthesizeAndSave(ttsRequest, user.getId().toString());

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Speech synthesized and saved successfully");
            response.put("filePath", result.getFilePath());
            response.put("fileSize", result.getFileSize());
            response.put("duration", result.getDurationSeconds());
            response.put("format", result.getAudioFormat());

            logger.info("Successfully synthesized and saved speech file for user: {}", user.getUsername());
            return ResponseEntity.ok(response);

        } catch (IOException e) {
            logger.error("TTS synthesis and save failed: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Speech synthesis failed: " + e.getMessage()));
        } catch (Exception e) {
            logger.error("TTS request failed: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Request failed: " + e.getMessage()));
        }
    }

    /**
     * Get available voices
     */
    @GetMapping("/voices")
    @Operation(summary = "Get Available Voices", description = "Get list of available TTS voices")
    public ResponseEntity<?> getAvailableVoices() {
        try {
            TextToSpeechService.Voice[] voices = ttsService.getAvailableVoices();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("voices", voices);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("Failed to get available voices: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to get voices: " + e.getMessage()));
        }
    }

    /**
     * Get available audio formats
     */
    @GetMapping("/formats")
    @Operation(summary = "Get Available Formats", description = "Get list of available audio formats")
    public ResponseEntity<?> getAvailableFormats() {
        try {
            TextToSpeechService.AudioFormat[] formats = ttsService.getAvailableFormats();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("formats", formats);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("Failed to get available formats: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to get formats: " + e.getMessage()));
        }
    }

    /**
     * Test TTS service
     */
    @GetMapping("/test")
    @Operation(summary = "Test TTS Service", description = "Test TTS service connectivity")
    public ResponseEntity<?> testTTSService() {
        try {
            boolean isConnected = ttsService.testConnection();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", isConnected);
            response.put("message", isConnected ? "TTS service is working" : "TTS service is not available");
            response.put("status", isConnected ? "connected" : "disconnected");

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("TTS service test failed: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Service test failed: " + e.getMessage()));
        }
    }

    /**
     * Create error response
     */
    private Map<String, Object> createErrorResponse(String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("error", message);
        return response;
    }
}