package com.voicehub.backend.controller;

import com.voicehub.backend.entity.User;
import com.voicehub.backend.entity.VoiceNote;
import com.voicehub.backend.service.UserService;
import com.voicehub.backend.service.VoiceNoteService;
import com.voicehub.backend.util.PageConverter;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.MalformedURLException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

/**
 * Voice Note Controller
 * REST API endpoints for voice note management
 */
@RestController
@RequestMapping("/api/voice-notes")
@Tag(name = "Voice Notes", description = "Voice note management APIs")
@PreAuthorize("hasRole('USER')")
public class VoiceNoteController {

    private static final Logger logger = LoggerFactory.getLogger(VoiceNoteController.class);

    @Autowired
    private VoiceNoteService voiceNoteService;

    @Autowired
    private UserService userService;

    /**
     * Create voice note with audio file upload
     */
    @PostMapping(consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "Create Voice Note", description = "Create a new voice note with audio file")
    public ResponseEntity<?> createVoiceNote(
            @RequestParam("audio") MultipartFile audioFile,
            @RequestParam(value = "title", required = false) String title,
            @RequestParam(value = "description", required = false) String description,
            @RequestParam(value = "category", defaultValue = "GENERAL") VoiceNote.Category category,
            @RequestParam(value = "priority", defaultValue = "MEDIUM") VoiceNote.Priority priority,
            @RequestParam(value = "tags", required = false) String[] tags,
            Authentication authentication) {
        
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            
            Set<String> tagSet = new HashSet<>();
            if (tags != null) {
                tagSet.addAll(Arrays.asList(tags));
            }

            VoiceNote voiceNote = voiceNoteService.createVoiceNote(
                title, description, audioFile, category, priority, tagSet, user);

            logger.info("Successfully created voice note: {} for user: {}", voiceNote.getId(), user.getUsername());
            return ResponseEntity.ok(createSuccessResponse("Voice note created successfully", voiceNote));

        } catch (Exception e) {
            logger.error("Failed to create voice note: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to create voice note: " + e.getMessage()));
        }
    }

    /**
     * Create voice note from audio data (for WebRTC integration)
     */
    @PostMapping("/from-audio")
    @Operation(summary = "Create Voice Note from Audio Data", description = "Create voice note from raw audio data")
    public ResponseEntity<?> createVoiceNoteFromAudio(
            @RequestBody Map<String, Object> request,
            Authentication authentication) {
        
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            
            String title = (String) request.get("title");
            String audioDataBase64 = (String) request.get("audioData");
            String audioFormat = (String) request.getOrDefault("audioFormat", "wav");
            String categoryStr = (String) request.getOrDefault("category", "GENERAL");
            
            VoiceNote.Category category = VoiceNote.Category.valueOf(categoryStr.toUpperCase());
            
            // Decode base64 audio data
            byte[] audioData = Base64.getDecoder().decode(audioDataBase64);
            
            VoiceNote voiceNote = voiceNoteService.createVoiceNoteFromAudio(title, audioData, audioFormat, category, user);

            logger.info("Successfully created voice note from audio data: {} for user: {}", voiceNote.getId(), user.getUsername());
            return ResponseEntity.ok(createSuccessResponse("Voice note created successfully", voiceNote));

        } catch (Exception e) {
            logger.error("Failed to create voice note from audio data: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to create voice note: " + e.getMessage()));
        }
    }

    /**
     * Get all voice notes for user
     */
    @GetMapping
    @Operation(summary = "Get Voice Notes", description = "Get user's voice notes with pagination")
    public ResponseEntity<?> getVoiceNotes(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            Authentication authentication) {
        
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            Pageable pageable = PageRequest.of(page, size);
            com.baomidou.mybatisplus.core.metadata.IPage<VoiceNote> iPageVoiceNotes = voiceNoteService.getUserVoiceNotes(user, pageable);
            Page<VoiceNote> voiceNotes = PageConverter.convertToSpringPage(iPageVoiceNotes);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", voiceNotes.getContent());
            response.put("totalElements", voiceNotes.getTotalElements());
            response.put("totalPages", voiceNotes.getTotalPages());
            response.put("currentPage", page);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("Failed to get voice notes: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to get voice notes: " + e.getMessage()));
        }
    }

    /**
     * Get voice note by ID
     */
    @GetMapping("/{id}")
    @Operation(summary = "Get Voice Note", description = "Get voice note by ID")
    public ResponseEntity<?> getVoiceNote(
            @PathVariable Long id,
            Authentication authentication) {
        
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            VoiceNote voiceNote = voiceNoteService.getVoiceNote(id, user);
            return ResponseEntity.ok(createSuccessResponse("Voice note retrieved successfully", voiceNote));

        } catch (Exception e) {
            logger.error("Failed to get voice note: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to get voice note: " + e.getMessage()));
        }
    }

    /**
     * Update voice note
     */
    @PutMapping("/{id}")
    @Operation(summary = "Update Voice Note", description = "Update voice note details")
    public ResponseEntity<?> updateVoiceNote(
            @PathVariable Long id,
            @RequestBody Map<String, Object> request,
            Authentication authentication) {
        
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            
            String title = (String) request.get("title");
            String description = (String) request.get("description");
            String categoryStr = (String) request.get("category");
            String priorityStr = (String) request.get("priority");
            
            VoiceNote.Category category = categoryStr != null ? VoiceNote.Category.valueOf(categoryStr.toUpperCase()) : null;
            VoiceNote.Priority priority = priorityStr != null ? VoiceNote.Priority.valueOf(priorityStr.toUpperCase()) : null;
            
            @SuppressWarnings("unchecked")
            List<String> tagsList = (List<String>) request.get("tags");
            Set<String> tags = tagsList != null ? new HashSet<>(tagsList) : null;

            VoiceNote updatedVoiceNote = voiceNoteService.updateVoiceNote(id, user, title, description, category, priority, tags);

            logger.info("Successfully updated voice note: {} for user: {}", id, user.getUsername());
            return ResponseEntity.ok(createSuccessResponse("Voice note updated successfully", updatedVoiceNote));

        } catch (Exception e) {
            logger.error("Failed to update voice note: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to update voice note: " + e.getMessage()));
        }
    }

    /**
     * Delete voice note
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "Delete Voice Note", description = "Delete voice note by ID")
    public ResponseEntity<?> deleteVoiceNote(
            @PathVariable Long id,
            Authentication authentication) {
        
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            voiceNoteService.deleteVoiceNote(id, user);

            logger.info("Successfully deleted voice note: {} for user: {}", id, user.getUsername());
            return ResponseEntity.ok(createSuccessResponse("Voice note deleted successfully", null));

        } catch (Exception e) {
            logger.error("Failed to delete voice note: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to delete voice note: " + e.getMessage()));
        }
    }

    /**
     * Search voice notes
     */
    @GetMapping("/search")
    @Operation(summary = "Search Voice Notes", description = "Search voice notes by keyword")
    public ResponseEntity<?> searchVoiceNotes(
            @RequestParam String keyword,
            Authentication authentication) {
        
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            List<VoiceNote> voiceNotes = voiceNoteService.searchVoiceNotes(user, keyword);

            return ResponseEntity.ok(createSuccessResponse("Search completed successfully", voiceNotes));

        } catch (Exception e) {
            logger.error("Failed to search voice notes: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to search voice notes: " + e.getMessage()));
        }
    }

    /**
     * Get voice notes by category
     */
    @GetMapping("/category/{category}")
    @Operation(summary = "Get Voice Notes by Category", description = "Get voice notes filtered by category")
    public ResponseEntity<?> getVoiceNotesByCategory(
            @PathVariable VoiceNote.Category category,
            Authentication authentication) {
        
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            List<VoiceNote> voiceNotes = voiceNoteService.getVoiceNotesByCategory(user, category);

            return ResponseEntity.ok(createSuccessResponse("Voice notes retrieved successfully", voiceNotes));

        } catch (Exception e) {
            logger.error("Failed to get voice notes by category: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to get voice notes by category: " + e.getMessage()));
        }
    }

    /**
     * Get favorite voice notes
     */
    @GetMapping("/favorites")
    @Operation(summary = "Get Favorite Voice Notes", description = "Get user's favorite voice notes")
    public ResponseEntity<?> getFavoriteVoiceNotes(Authentication authentication) {
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            List<VoiceNote> voiceNotes = voiceNoteService.getFavoriteVoiceNotes(user);

            return ResponseEntity.ok(createSuccessResponse("Favorite voice notes retrieved successfully", voiceNotes));

        } catch (Exception e) {
            logger.error("Failed to get favorite voice notes: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to get favorite voice notes: " + e.getMessage()));
        }
    }

    /**
     * Get archived voice notes
     */
    @GetMapping("/archived")
    @Operation(summary = "Get Archived Voice Notes", description = "Get user's archived voice notes")
    public ResponseEntity<?> getArchivedVoiceNotes(Authentication authentication) {
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            List<VoiceNote> voiceNotes = voiceNoteService.getArchivedVoiceNotes(user);

            return ResponseEntity.ok(createSuccessResponse("Archived voice notes retrieved successfully", voiceNotes));

        } catch (Exception e) {
            logger.error("Failed to get archived voice notes: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to get archived voice notes: " + e.getMessage()));
        }
    }

    /**
     * Get voice notes by tag
     */
    @GetMapping("/tag/{tag}")
    @Operation(summary = "Get Voice Notes by Tag", description = "Get voice notes filtered by tag")
    public ResponseEntity<?> getVoiceNotesByTag(
            @PathVariable String tag,
            Authentication authentication) {
        
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            List<VoiceNote> voiceNotes = voiceNoteService.getVoiceNotesByTag(user, tag);

            return ResponseEntity.ok(createSuccessResponse("Voice notes retrieved successfully", voiceNotes));

        } catch (Exception e) {
            logger.error("Failed to get voice notes by tag: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to get voice notes by tag: " + e.getMessage()));
        }
    }

    /**
     * Get recent voice notes
     */
    @GetMapping("/recent")
    @Operation(summary = "Get Recent Voice Notes", description = "Get recent voice notes")
    public ResponseEntity<?> getRecentVoiceNotes(
            @RequestParam(defaultValue = "7") int days,
            Authentication authentication) {
        
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            List<VoiceNote> voiceNotes = voiceNoteService.getRecentVoiceNotes(user, days);

            return ResponseEntity.ok(createSuccessResponse("Recent voice notes retrieved successfully", voiceNotes));

        } catch (Exception e) {
            logger.error("Failed to get recent voice notes: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to get recent voice notes: " + e.getMessage()));
        }
    }

    /**
     * Toggle favorite status
     */
    @PutMapping("/{id}/favorite")
    @Operation(summary = "Toggle Favorite", description = "Toggle favorite status of voice note")
    public ResponseEntity<?> toggleFavorite(
            @PathVariable Long id,
            Authentication authentication) {
        
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            VoiceNote voiceNote = voiceNoteService.toggleFavorite(id, user);

            logger.info("Successfully toggled favorite for voice note: {} for user: {}", id, user.getUsername());
            return ResponseEntity.ok(createSuccessResponse("Favorite status updated successfully", voiceNote));

        } catch (Exception e) {
            logger.error("Failed to toggle favorite: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to toggle favorite: " + e.getMessage()));
        }
    }

    /**
     * Toggle archive status
     */
    @PutMapping("/{id}/archive")
    @Operation(summary = "Toggle Archive", description = "Toggle archive status of voice note")
    public ResponseEntity<?> toggleArchive(
            @PathVariable Long id,
            Authentication authentication) {
        
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            VoiceNote voiceNote = voiceNoteService.toggleArchive(id, user);

            logger.info("Successfully toggled archive for voice note: {} for user: {}", id, user.getUsername());
            return ResponseEntity.ok(createSuccessResponse("Archive status updated successfully", voiceNote));

        } catch (Exception e) {
            logger.error("Failed to toggle archive: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to toggle archive: " + e.getMessage()));
        }
    }

    /**
     * Get voice note statistics
     */
    @GetMapping("/stats")
    @Operation(summary = "Get Voice Note Statistics", description = "Get user's voice note statistics")
    public ResponseEntity<?> getVoiceNoteStats(Authentication authentication) {
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            VoiceNoteService.VoiceNoteStats stats = voiceNoteService.getUserVoiceNoteStats(user);

            return ResponseEntity.ok(createSuccessResponse("Statistics retrieved successfully", stats));

        } catch (Exception e) {
            logger.error("Failed to get voice note stats: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to get statistics: " + e.getMessage()));
        }
    }

    /**
     * Get user tags
     */
    @GetMapping("/tags")
    @Operation(summary = "Get User Tags", description = "Get all tags used by user")
    public ResponseEntity<?> getUserTags(Authentication authentication) {
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            Set<String> tagsSet = voiceNoteService.getUserTags(user);
            List<String> tags = PageConverter.setToList(tagsSet);

            return ResponseEntity.ok(createSuccessResponse("Tags retrieved successfully", tags));

        } catch (Exception e) {
            logger.error("Failed to get user tags: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to get tags: " + e.getMessage()));
        }
    }

    /**
     * Download audio file
     */
    @GetMapping("/{id}/audio")
    @Operation(summary = "Download Audio File", description = "Download voice note audio file")
    public ResponseEntity<Resource> downloadAudioFile(
            @PathVariable Long id,
            Authentication authentication) {
        
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            VoiceNote voiceNote = voiceNoteService.getVoiceNote(id, user);
            Path filePath = Paths.get(voiceNote.getAudioFilePath());
            Resource resource = new UrlResource(filePath.toUri());

            if (resource.exists() && resource.isReadable()) {
                String contentType = "audio/" + voiceNote.getAudioFormat();
                
                return ResponseEntity.ok()
                        .contentType(MediaType.parseMediaType(contentType))
                        .header(HttpHeaders.CONTENT_DISPOSITION, 
                               "attachment; filename=\"" + voiceNote.getAudioFileName() + "\"")
                        .body(resource);
            } else {
                return ResponseEntity.notFound().build();
            }

        } catch (MalformedURLException e) {
            logger.error("Failed to download audio file: {}", e.getMessage());
            return ResponseEntity.internalServerError().build();
        } catch (Exception e) {
            logger.error("Failed to download audio file: {}", e.getMessage());
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Create success response
     */
    private Map<String, Object> createSuccessResponse(String message, Object data) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", message);
        response.put("data", data);
        return response;
    }

    /**
     * Create error response
     */
    private Map<String, Object> createErrorResponse(String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("error", message);
        return response;
    }
}