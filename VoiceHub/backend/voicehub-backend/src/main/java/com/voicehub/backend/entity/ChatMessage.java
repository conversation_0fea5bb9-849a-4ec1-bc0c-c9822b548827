package com.voicehub.backend.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;

/**
 * Chat Message Entity
 * Represents individual messages in a conversation
 */
@TableName("chat_messages")
public class ChatMessage extends BaseEntity {

    @TableField("content")
    private String content;

    @TableField("type")
    private MessageType type;

    @TableField("role")
    private MessageRole role;

    @TableField("conversation_id")
    private Long conversationId;

    @TableField(exist = false)
    private Conversation conversation;

    @TableField("emotion_detected")
    private String emotionDetected;

    @TableField("emotion_confidence")
    private Double emotionConfidence;

    @TableField("response_time_ms")
    private Long responseTimeMs;

    @TableField("token_count")
    private Integer tokenCount;

    @TableField("is_voice_input")
    private Boolean isVoiceInput = false;

    @TableField("voice_file_path")
    private String voiceFilePath;

    @TableField("transcription_confidence")
    private Double transcriptionConfidence;

    // 新增的getter/setter方法（只添加缺失的）
    public Long getConversationId() { return conversationId; }
    public void setConversationId(Long conversationId) { this.conversationId = conversationId; }

    // Enums
    public enum MessageType {
        TEXT,
        VOICE,
        SYSTEM,
        ERROR
    }

    public enum MessageRole {
        USER,
        ASSISTANT,
        SYSTEM
    }

    // Constructors
    public ChatMessage() {}

    public ChatMessage(String content, MessageType type, MessageRole role, Conversation conversation) {
        this.content = content;
        this.type = type;
        this.role = role;
        this.conversation = conversation;
    }

    // Static factory methods
    public static ChatMessage createUserMessage(String content, Conversation conversation) {
        return new ChatMessage(content, MessageType.TEXT, MessageRole.USER, conversation);
    }

    public static ChatMessage createAssistantMessage(String content, Conversation conversation) {
        return new ChatMessage(content, MessageType.TEXT, MessageRole.ASSISTANT, conversation);
    }

    public static ChatMessage createVoiceMessage(String content, String voiceFilePath, Conversation conversation) {
        ChatMessage message = new ChatMessage(content, MessageType.VOICE, MessageRole.USER, conversation);
        message.setIsVoiceInput(true);
        message.setVoiceFilePath(voiceFilePath);
        return message;
    }

    public static ChatMessage createSystemMessage(String content, Conversation conversation) {
        return new ChatMessage(content, MessageType.SYSTEM, MessageRole.SYSTEM, conversation);
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public MessageType getType() {
        return type;
    }

    public void setType(MessageType type) {
        this.type = type;
    }

    public MessageRole getRole() {
        return role;
    }

    public void setRole(MessageRole role) {
        this.role = role;
    }

    public Conversation getConversation() {
        return conversation;
    }

    public void setConversation(Conversation conversation) {
        this.conversation = conversation;
    }

    public String getEmotionDetected() {
        return emotionDetected;
    }

    public void setEmotionDetected(String emotionDetected) {
        this.emotionDetected = emotionDetected;
    }

    public Double getEmotionConfidence() {
        return emotionConfidence;
    }

    public void setEmotionConfidence(Double emotionConfidence) {
        this.emotionConfidence = emotionConfidence;
    }

    public Long getResponseTimeMs() {
        return responseTimeMs;
    }

    public void setResponseTimeMs(Long responseTimeMs) {
        this.responseTimeMs = responseTimeMs;
    }

    public Integer getTokenCount() {
        return tokenCount;
    }

    public void setTokenCount(Integer tokenCount) {
        this.tokenCount = tokenCount;
    }

    public Boolean getIsVoiceInput() {
        return isVoiceInput;
    }

    public void setIsVoiceInput(Boolean isVoiceInput) {
        this.isVoiceInput = isVoiceInput;
    }

    public String getVoiceFilePath() {
        return voiceFilePath;
    }

    public void setVoiceFilePath(String voiceFilePath) {
        this.voiceFilePath = voiceFilePath;
    }

    public Double getTranscriptionConfidence() {
        return transcriptionConfidence;
    }

    public void setTranscriptionConfidence(Double transcriptionConfidence) {
        this.transcriptionConfidence = transcriptionConfidence;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    // Utility methods
    public boolean isFromUser() {
        return MessageRole.USER.equals(this.role);
    }

    public boolean isFromAssistant() {
        return MessageRole.ASSISTANT.equals(this.role);
    }

    public boolean isVoiceMessage() {
        return MessageType.VOICE.equals(this.type) || Boolean.TRUE.equals(this.isVoiceInput);
    }

    public void setEmotionAnalysis(String emotion, Double confidence) {
        this.emotionDetected = emotion;
        this.emotionConfidence = confidence;
    }

    @Override
    public String toString() {
        return "ChatMessage{" +
                "id=" + id +
                ", role=" + role +
                ", type=" + type +
                ", content='" + (content != null ? content.substring(0, Math.min(50, content.length())) : "") + "..." + '\'' +
                ", createdAt=" + createdAt +
                '}';
    }
}