package com.voicehub.backend.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.UUID;

/**
 * Voice Analytics Entity
 * Stores detailed voice analysis data for quality assessment and improvement tracking
 */
@TableName("voice_analytics")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = false)
public class VoiceAnalytics extends BaseEntity {

    @TableField("user_id")
    private Long userId;

    @TableField(exist = false)
    private User user;

    @TableField("session_id")
    private UUID sessionId;

    @TableField("recording_type")
    private String recordingType; // NOTE, CONVERSATION, SCHEDULE

    @TableField("recording_id")
    private Long recordingId;

    @TableField("duration_seconds")
    private Integer durationSeconds;

    @TableField("audio_quality_score")
    private BigDecimal audioQualityScore;

    @TableField("speech_rate_wpm")
    private Integer speechRateWpm;

    @TableField("pause_frequency")
    private BigDecimal pauseFrequency;

    @TableField("volume_consistency")
    private BigDecimal volumeConsistency;

    @TableField("clarity_score")
    private BigDecimal clarityScore;

    @TableField("pitch_analysis")
    private String pitchAnalysis;

    @TableField("frequency_analysis")
    private String frequencyAnalysis;

    @TableField("noise_level")
    private BigDecimal noiseLevel;

    @TableField("voice_activity_ratio")
    private BigDecimal voiceActivityRatio;

    @TableField("emotion_detected")
    private String emotionDetected;

    @TableField("emotion_confidence")
    private BigDecimal emotionConfidence;

    @TableField("language_detected")
    private String languageDetected;

    @TableField("language_confidence")
    private BigDecimal languageConfidence;

    // Enums for recording types
    public enum RecordingType {
        NOTE, CONVERSATION, SCHEDULE
    }

    // Enums for emotions
    public enum EmotionType {
        HAPPY, SAD, ANGRY, SURPRISED, NEUTRAL, EXCITED, CALM, FRUSTRATED
    }

    // Helper methods
    public RecordingType getRecordingTypeEnum() {
        try {
            return RecordingType.valueOf(recordingType.toUpperCase());
        } catch (Exception e) {
            return RecordingType.NOTE;
        }
    }

    public void setRecordingTypeEnum(RecordingType recordingType) {
        this.recordingType = recordingType.name();
    }

    public EmotionType getEmotionDetectedEnum() {
        try {
            return EmotionType.valueOf(emotionDetected.toUpperCase());
        } catch (Exception e) {
            return EmotionType.NEUTRAL;
        }
    }

    public void setEmotionDetectedEnum(EmotionType emotion) {
        this.emotionDetected = emotion.name();
    }

    // Calculated properties
    public boolean isHighQuality() {
        return audioQualityScore != null && audioQualityScore.compareTo(new BigDecimal("8.0")) >= 0;
    }

    public boolean isOptimalSpeechRate() {
        return speechRateWpm != null && speechRateWpm >= 120 && speechRateWpm <= 160;
    }

    public boolean isGoodVoiceActivity() {
        return voiceActivityRatio != null && voiceActivityRatio.compareTo(new BigDecimal("0.7")) >= 0;
    }

    public String getQualityLevel() {
        if (audioQualityScore == null) return "UNKNOWN";
        
        if (audioQualityScore.compareTo(new BigDecimal("9.0")) >= 0) return "EXCELLENT";
        if (audioQualityScore.compareTo(new BigDecimal("8.0")) >= 0) return "GOOD";
        if (audioQualityScore.compareTo(new BigDecimal("6.0")) >= 0) return "FAIR";
        return "POOR";
    }

    public void onCreate() {
        if (sessionId == null) {
            sessionId = UUID.randomUUID();
        }
    }
}