package com.voicehub.backend.entity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

/**
 * Voice Note Entity
 * Stores voice recordings with transcriptions and metadata
 */
@TableName("voice_notes")
public class VoiceNote extends BaseEntity {

    @NotBlank(message = "Title cannot be blank")
    @TableField("title")
    private String title;

    @TableField("description")
    private String description;

    @TableField("transcription")
    private String transcription;

    @TableField("file_path")
    private String audioFilePath;

    @TableField("audio_file_name")
    private String audioFileName;

    @TableField("audio_file_size")
    private Long audioFileSize;

    @TableField("audio_duration_seconds")
    private Integer audioDurationSeconds;

    @TableField("audio_format")
    private String audioFormat;

    @TableField("category")
    private Category category = Category.GENERAL;

    @TableField("priority")
    private Priority priority = Priority.MEDIUM;

    @TableField("is_favorite")
    private Boolean isFavorite = false;

    @TableField("is_archived")
    private Boolean isArchived = false;

    @TableField("transcription_confidence")
    private Double transcriptionConfidence;

    @TableField("language_detected")
    private String languageDetected;

    @TableField("tags")
    private Set<String> tags = new HashSet<>();

    @TableField("user_id")
    private Long userId;

    @TableField(exist = false)
    private User user;

    // 新增的getter/setter方法（只添加缺失的）
    public Long getUserId() { return userId; }
    public void setUserId(Long userId) { this.userId = userId; }

    // Enums
    public enum Category {
        GENERAL, MEETING, IDEA, REMINDER, PERSONAL, WORK, STUDY, TRAVEL, HEALTH, OTHER
    }

    public enum Priority {
        LOW, MEDIUM, HIGH, URGENT
    }

    // Constructors
    public VoiceNote() {}

    public VoiceNote(String title, User user) {
        this.title = title;
        this.user = user;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getTranscription() {
        return transcription;
    }

    public void setTranscription(String transcription) {
        this.transcription = transcription;
    }

    public String getAudioFilePath() {
        return audioFilePath;
    }

    public void setAudioFilePath(String audioFilePath) {
        this.audioFilePath = audioFilePath;
    }

    public String getAudioFileName() {
        return audioFileName;
    }

    public void setAudioFileName(String audioFileName) {
        this.audioFileName = audioFileName;
    }

    public Long getAudioFileSize() {
        return audioFileSize;
    }

    public void setAudioFileSize(Long audioFileSize) {
        this.audioFileSize = audioFileSize;
    }

    public Integer getAudioDurationSeconds() {
        return audioDurationSeconds;
    }

    public void setAudioDurationSeconds(Integer audioDurationSeconds) {
        this.audioDurationSeconds = audioDurationSeconds;
    }

    public String getAudioFormat() {
        return audioFormat;
    }

    public void setAudioFormat(String audioFormat) {
        this.audioFormat = audioFormat;
    }

    public Category getCategory() {
        return category;
    }

    public void setCategory(Category category) {
        this.category = category;
    }

    public Priority getPriority() {
        return priority;
    }

    public void setPriority(Priority priority) {
        this.priority = priority;
    }

    public Boolean getIsFavorite() {
        return isFavorite;
    }

    public void setIsFavorite(Boolean isFavorite) {
        this.isFavorite = isFavorite;
    }

    public Boolean getIsArchived() {
        return isArchived;
    }

    public void setIsArchived(Boolean isArchived) {
        this.isArchived = isArchived;
    }

    public Double getTranscriptionConfidence() {
        return transcriptionConfidence;
    }

    public void setTranscriptionConfidence(Double transcriptionConfidence) {
        this.transcriptionConfidence = transcriptionConfidence;
    }

    public String getLanguageDetected() {
        return languageDetected;
    }

    public void setLanguageDetected(String languageDetected) {
        this.languageDetected = languageDetected;
    }

    public Set<String> getTags() {
        return tags;
    }

    public void setTags(Set<String> tags) {
        this.tags = tags;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    // Utility methods
    public void addTag(String tag) {
        if (tag != null && !tag.trim().isEmpty()) {
            this.tags.add(tag.trim().toLowerCase());
        }
    }

    public void removeTag(String tag) {
        if (tag != null) {
            this.tags.remove(tag.trim().toLowerCase());
        }
    }

    public boolean hasTag(String tag) {
        return tag != null && this.tags.contains(tag.trim().toLowerCase());
    }

    public String getFormattedDuration() {
        if (audioDurationSeconds == null || audioDurationSeconds == 0) {
            return "0:00";
        }
        
        int minutes = audioDurationSeconds / 60;
        int seconds = audioDurationSeconds % 60;
        return String.format("%d:%02d", minutes, seconds);
    }

    public String getFormattedFileSize() {
        if (audioFileSize == null || audioFileSize == 0) {
            return "0 B";
        }
        
        if (audioFileSize < 1024) {
            return audioFileSize + " B";
        } else if (audioFileSize < 1024 * 1024) {
            return String.format("%.1f KB", audioFileSize / 1024.0);
        } else {
            return String.format("%.1f MB", audioFileSize / (1024.0 * 1024.0));
        }
    }

    public boolean isRecent() {
        if (createdAt == null) return false;
        return createdAt.isAfter(LocalDateTime.now().minusDays(7));
    }

    @Override
    public String toString() {
        return "VoiceNote{" +
                "id=" + id +
                ", title='" + title + '\'' +
                ", category=" + category +
                ", priority=" + priority +
                ", duration=" + getFormattedDuration() +
                ", createdAt=" + createdAt +
                '}';
    }
}