package com.voicehub.backend.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.voicehub.backend.entity.MoodEntry;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 情绪记录Mapper接口
 * 继承MyBatis-Plus的BaseMapper，提供基础CRUD功能
 */
@Mapper
public interface MoodEntryMapper extends BaseMapper<MoodEntry> {

    /**
     * 查找用户的所有情绪记录，按创建时间降序
     */
    @Select("SELECT * FROM mood_entries WHERE user_id = #{userId} AND deleted = 0 ORDER BY created_at DESC")
    List<MoodEntry> findByUserIdOrderByCreatedAtDesc(@Param("userId") Long userId);

    /**
     * 分页查找用户的情绪记录
     */
    @Select("SELECT * FROM mood_entries WHERE user_id = #{userId} AND deleted = 0 ORDER BY created_at DESC")
    IPage<MoodEntry> findByUserIdOrderByCreatedAtDesc(@Param("userId") Long userId, Page<MoodEntry> page);

    /**
     * 根据用户和情绪类型查找记录
     */
    @Select("SELECT * FROM mood_entries WHERE user_id = #{userId} AND mood = #{mood} AND deleted = 0 ORDER BY created_at DESC")
    List<MoodEntry> findByUserIdAndMoodOrderByCreatedAtDesc(@Param("userId") Long userId, @Param("mood") String mood);

    /**
     * 根据用户和强度范围查找记录
     */
    @Select("SELECT * FROM mood_entries WHERE user_id = #{userId} AND intensity >= #{minIntensity} AND intensity <= #{maxIntensity} AND deleted = 0 ORDER BY created_at DESC")
    List<MoodEntry> findByUserIdAndIntensityBetweenOrderByCreatedAtDesc(@Param("userId") Long userId, 
                                                                        @Param("minIntensity") Integer minIntensity, 
                                                                        @Param("maxIntensity") Integer maxIntensity);

    /**
     * 根据用户和上下文查找记录
     */
    @Select("SELECT * FROM mood_entries WHERE user_id = #{userId} AND context = #{context} AND deleted = 0 ORDER BY created_at DESC")
    List<MoodEntry> findByUserIdAndContextOrderByCreatedAtDesc(@Param("userId") Long userId, @Param("context") String context);

    /**
     * 查找用户今天的情绪记录
     */
    @Select("SELECT * FROM mood_entries WHERE user_id = #{userId} AND DATE(created_at) = CURRENT_DATE AND deleted = 0 ORDER BY created_at DESC")
    List<MoodEntry> findTodayMoodEntries(@Param("userId") Long userId);

    /**
     * 查找用户本周的情绪记录
     */
    @Select("SELECT * FROM mood_entries WHERE user_id = #{userId} AND created_at >= #{weekStart} AND deleted = 0 ORDER BY created_at DESC")
    List<MoodEntry> findThisWeeksMoodEntries(@Param("userId") Long userId, @Param("weekStart") LocalDateTime weekStart);

    /**
     * 查找用户本月的情绪记录
     */
    @Select("SELECT * FROM mood_entries WHERE user_id = #{userId} AND created_at >= #{monthStart} AND deleted = 0 ORDER BY created_at DESC")
    List<MoodEntry> findThisMonthsMoodEntries(@Param("userId") Long userId, @Param("monthStart") LocalDateTime monthStart);

    /**
     * 查找用户指定时间范围内的情绪记录
     */
    @Select("SELECT * FROM mood_entries WHERE user_id = #{userId} AND created_at >= #{startDate} AND created_at <= #{endDate} AND deleted = 0 ORDER BY created_at DESC")
    List<MoodEntry> findByUserIdAndDateRange(@Param("userId") Long userId, 
                                            @Param("startDate") LocalDateTime startDate, 
                                            @Param("endDate") LocalDateTime endDate);

    /**
     * 计算用户的平均情绪强度
     */
    @Select("SELECT AVG(intensity) FROM mood_entries WHERE user_id = #{userId} AND deleted = 0")
    Double findAverageIntensityByUserId(@Param("userId") Long userId);

    /**
     * 计算用户指定时间以来的平均情绪强度
     */
    @Select("SELECT AVG(intensity) FROM mood_entries WHERE user_id = #{userId} AND created_at >= #{startDate} AND deleted = 0")
    Double findAverageIntensityByUserIdSince(@Param("userId") Long userId, @Param("startDate") LocalDateTime startDate);

    /**
     * 查找用户最高强度的情绪记录
     */
    @Select("SELECT * FROM mood_entries WHERE user_id = #{userId} AND intensity = (SELECT MAX(intensity) FROM mood_entries WHERE user_id = #{userId} AND deleted = 0) AND deleted = 0 ORDER BY created_at DESC")
    List<MoodEntry> findHighestIntensityMoodEntries(@Param("userId") Long userId);

    /**
     * 查找用户最低强度的情绪记录
     */
    @Select("SELECT * FROM mood_entries WHERE user_id = #{userId} AND intensity = (SELECT MIN(intensity) FROM mood_entries WHERE user_id = #{userId} AND deleted = 0) AND deleted = 0 ORDER BY created_at DESC")
    List<MoodEntry> findLowestIntensityMoodEntries(@Param("userId") Long userId);

    /**
     * 统计用户情绪记录总数
     */
    @Select("SELECT COUNT(*) FROM mood_entries WHERE user_id = #{userId} AND deleted = 0")
    long countByUserId(@Param("userId") Long userId);

    /**
     * 统计用户指定情绪类型的记录数
     */
    @Select("SELECT COUNT(*) FROM mood_entries WHERE user_id = #{userId} AND mood = #{mood} AND deleted = 0")
    long countByUserIdAndMood(@Param("userId") Long userId, @Param("mood") String mood);

    /**
     * 统计用户今天的情绪记录数
     */
    @Select("SELECT COUNT(*) FROM mood_entries WHERE user_id = #{userId} AND DATE(created_at) = CURRENT_DATE AND deleted = 0")
    long countTodayMoodEntriesByUserId(@Param("userId") Long userId);

    /**
     * 统计用户本周的情绪记录数
     */
    @Select("SELECT COUNT(*) FROM mood_entries WHERE user_id = #{userId} AND created_at >= #{weekStart} AND deleted = 0")
    long countThisWeekMoodEntriesByUserId(@Param("userId") Long userId, @Param("weekStart") LocalDateTime weekStart);

    /**
     * 根据ID和用户查找情绪记录（安全检查）
     */
    @Select("SELECT * FROM mood_entries WHERE id = #{id} AND user_id = #{userId} AND deleted = 0")
    MoodEntry findByIdAndUserId(@Param("id") Long id, @Param("userId") Long userId);
}
