package com.voicehub.backend.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.voicehub.backend.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 用户Mapper接口
 * 继承MyBatis-Plus的BaseMapper，提供基础CRUD功能
 */
@Mapper
public interface UserMapper extends BaseMapper<User> {

    /**
     * 根据用户名查找用户
     */
    @Select("SELECT * FROM users WHERE username = #{username} AND deleted = 0")
    User findByUsername(@Param("username") String username);

    /**
     * 根据邮箱查找用户
     */
    @Select("SELECT * FROM users WHERE email = #{email} AND deleted = 0")
    User findByEmail(@Param("email") String email);

    /**
     * 根据用户名或邮箱查找用户
     */
    @Select("SELECT * FROM users WHERE (username = #{username} OR email = #{email}) AND deleted = 0")
    User findByUsernameOrEmail(@Param("username") String username, @Param("email") String email);

    /**
     * 检查用户名是否存在
     */
    @Select("SELECT COUNT(*) > 0 FROM users WHERE username = #{username} AND deleted = 0")
    boolean existsByUsername(@Param("username") String username);

    /**
     * 检查邮箱是否存在
     */
    @Select("SELECT COUNT(*) > 0 FROM users WHERE email = #{email} AND deleted = 0")
    boolean existsByEmail(@Param("email") String email);

    /**
     * 更新用户最后登录时间
     */
    @Update("UPDATE users SET last_login_at = #{lastLoginAt}, updated_at = NOW() WHERE id = #{userId}")
    void updateLastLoginTime(@Param("userId") Long userId, @Param("lastLoginAt") LocalDateTime lastLoginAt);

    /**
     * 根据角色查找用户
     */
    @Select("SELECT * FROM users WHERE role = #{role} AND deleted = 0")
    List<User> findByRole(@Param("role") String role);

    /**
     * 查找启用状态的用户
     */
    @Select("SELECT * FROM users WHERE is_enabled = #{enabled} AND deleted = 0")
    List<User> findByEnabled(@Param("enabled") Boolean enabled);

    /**
     * 根据创建时间范围查找用户
     */
    @Select("SELECT * FROM users WHERE created_at BETWEEN #{startDate} AND #{endDate} AND deleted = 0")
    List<User> findByCreatedAtBetween(@Param("startDate") LocalDateTime startDate, 
                                     @Param("endDate") LocalDateTime endDate);

    /**
     * 统计用户总数
     */
    @Select("SELECT COUNT(*) FROM users WHERE deleted = 0")
    long countAllUsers();

    /**
     * 统计启用用户数
     */
    @Select("SELECT COUNT(*) FROM users WHERE is_enabled = true AND deleted = 0")
    long countEnabledUsers();
}
