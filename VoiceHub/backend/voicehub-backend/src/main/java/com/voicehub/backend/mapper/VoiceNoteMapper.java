package com.voicehub.backend.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.voicehub.backend.entity.VoiceNote;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * 语音笔记Mapper接口
 * 继承MyBatis-Plus的BaseMapper，提供基础CRUD功能
 */
@Mapper
public interface VoiceNoteMapper extends BaseMapper<VoiceNote> {

    /**
     * 查找用户的所有语音笔记，按创建时间降序
     */
    @Select("SELECT * FROM voice_notes WHERE user_id = #{userId} AND deleted = 0 ORDER BY created_at DESC")
    List<VoiceNote> findByUserIdOrderByCreatedAtDesc(@Param("userId") Long userId);

    /**
     * 分页查找用户的语音笔记
     */
    @Select("SELECT * FROM voice_notes WHERE user_id = #{userId} AND deleted = 0 ORDER BY created_at DESC")
    IPage<VoiceNote> findByUserIdOrderByCreatedAtDesc(@Param("userId") Long userId, Page<VoiceNote> page);

    /**
     * 根据用户和分类查找语音笔记
     */
    @Select("SELECT * FROM voice_notes WHERE user_id = #{userId} AND category = #{category} AND deleted = 0 ORDER BY created_at DESC")
    List<VoiceNote> findByUserIdAndCategoryOrderByCreatedAtDesc(@Param("userId") Long userId, @Param("category") String category);

    /**
     * 根据用户和优先级查找语音笔记
     */
    @Select("SELECT * FROM voice_notes WHERE user_id = #{userId} AND priority = #{priority} AND deleted = 0 ORDER BY created_at DESC")
    List<VoiceNote> findByUserIdAndPriorityOrderByCreatedAtDesc(@Param("userId") Long userId, @Param("priority") String priority);

    /**
     * 查找收藏的语音笔记
     */
    @Select("SELECT * FROM voice_notes WHERE user_id = #{userId} AND is_favorite = true AND deleted = 0 ORDER BY created_at DESC")
    List<VoiceNote> findByUserIdAndIsFavoriteTrueOrderByCreatedAtDesc(@Param("userId") Long userId);

    /**
     * 查找已归档的语音笔记
     */
    @Select("SELECT * FROM voice_notes WHERE user_id = #{userId} AND is_archived = true AND deleted = 0 ORDER BY created_at DESC")
    List<VoiceNote> findByUserIdAndIsArchivedTrueOrderByCreatedAtDesc(@Param("userId") Long userId);

    /**
     * 查找未归档的语音笔记
     */
    @Select("SELECT * FROM voice_notes WHERE user_id = #{userId} AND is_archived = false AND deleted = 0 ORDER BY created_at DESC")
    List<VoiceNote> findByUserIdAndIsArchivedFalseOrderByCreatedAtDesc(@Param("userId") Long userId);

    /**
     * 搜索语音笔记（标题和转写内容）
     */
    @Select("SELECT * FROM voice_notes WHERE user_id = #{userId} AND (title ILIKE CONCAT('%', #{keyword}, '%') OR transcription ILIKE CONCAT('%', #{keyword}, '%')) AND deleted = 0 ORDER BY created_at DESC")
    List<VoiceNote> searchByKeyword(@Param("userId") Long userId, @Param("keyword") String keyword);

    /**
     * 根据标签查找语音笔记
     */
    @Select("SELECT vn.* FROM voice_notes vn JOIN voice_note_tags vnt ON vn.id = vnt.voice_note_id WHERE vn.user_id = #{userId} AND vnt.tag = #{tag} AND vn.deleted = 0 ORDER BY vn.created_at DESC")
    List<VoiceNote> findByUserIdAndTag(@Param("userId") Long userId, @Param("tag") String tag);

    /**
     * 查找最近的语音笔记
     */
    @Select("SELECT * FROM voice_notes WHERE user_id = #{userId} AND created_at >= #{since} AND deleted = 0 ORDER BY created_at DESC")
    List<VoiceNote> findRecentVoiceNotes(@Param("userId") Long userId, @Param("since") LocalDateTime since);

    /**
     * 根据ID和用户查找语音笔记（安全检查）
     */
    @Select("SELECT * FROM voice_notes WHERE id = #{id} AND user_id = #{userId} AND deleted = 0")
    VoiceNote findByIdAndUserId(@Param("id") Long id, @Param("userId") Long userId);

    /**
     * 统计用户语音笔记总数
     */
    @Select("SELECT COUNT(*) FROM voice_notes WHERE user_id = #{userId} AND deleted = 0")
    long countByUserId(@Param("userId") Long userId);

    /**
     * 统计用户指定分类的语音笔记数
     */
    @Select("SELECT COUNT(*) FROM voice_notes WHERE user_id = #{userId} AND category = #{category} AND deleted = 0")
    long countByUserIdAndCategory(@Param("userId") Long userId, @Param("category") String category);

    /**
     * 统计用户收藏的语音笔记数
     */
    @Select("SELECT COUNT(*) FROM voice_notes WHERE user_id = #{userId} AND is_favorite = true AND deleted = 0")
    long countByUserIdAndIsFavoriteTrue(@Param("userId") Long userId);

    /**
     * 统计用户已归档的语音笔记数
     */
    @Select("SELECT COUNT(*) FROM voice_notes WHERE user_id = #{userId} AND is_archived = true AND deleted = 0")
    long countByUserIdAndIsArchivedTrue(@Param("userId") Long userId);

    /**
     * 查找转写置信度低的语音笔记
     */
    @Select("SELECT * FROM voice_notes WHERE user_id = #{userId} AND transcription_confidence < #{threshold} AND deleted = 0 ORDER BY created_at DESC")
    List<VoiceNote> findLowConfidenceTranscriptions(@Param("userId") Long userId, @Param("threshold") Double threshold);

    /**
     * 根据语言查找语音笔记
     */
    @Select("SELECT * FROM voice_notes WHERE user_id = #{userId} AND language_detected = #{language} AND deleted = 0 ORDER BY created_at DESC")
    List<VoiceNote> findByUserIdAndLanguageDetectedOrderByCreatedAtDesc(@Param("userId") Long userId, @Param("language") String language);

    /**
     * 查找需要转写的语音笔记
     */
    @Select("SELECT * FROM voice_notes WHERE user_id = #{userId} AND (transcription IS NULL OR transcription = '') AND deleted = 0 ORDER BY created_at DESC")
    List<VoiceNote> findVoiceNotesNeedingTranscription(@Param("userId") Long userId);

    /**
     * 根据音频格式查找语音笔记
     */
    @Select("SELECT * FROM voice_notes WHERE user_id = #{userId} AND audio_format = #{audioFormat} AND deleted = 0 ORDER BY created_at DESC")
    List<VoiceNote> findByUserIdAndAudioFormatOrderByCreatedAtDesc(@Param("userId") Long userId, @Param("audioFormat") String audioFormat);

    // ========== Service层需要的额外方法 ==========

    /**
     * 分页查询用户语音笔记
     */
    @Select("SELECT * FROM voice_notes WHERE user_id = #{userId} AND deleted = 0 ORDER BY created_at DESC")
    IPage<VoiceNote> findByUserId(@Param("userId") Long userId, Page<VoiceNote> page);

    /**
     * 获取用户总时长
     */
    @Select("SELECT COALESCE(SUM(audio_duration_seconds), 0) FROM voice_notes WHERE user_id = #{userId} AND deleted = 0")
    long getTotalDurationByUserId(@Param("userId") Long userId);

    /**
     * 获取用户所有标签
     */
    @Select("SELECT DISTINCT tags FROM voice_notes WHERE user_id = #{userId} AND tags IS NOT NULL AND deleted = 0")
    Set<String> findAllTagsByUserId(@Param("userId") Long userId);
}
