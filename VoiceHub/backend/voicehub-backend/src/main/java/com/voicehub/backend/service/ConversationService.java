package com.voicehub.backend.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.voicehub.backend.entity.Conversation;
import com.voicehub.backend.entity.ChatMessage;
import com.voicehub.backend.entity.User;
import com.voicehub.backend.mapper.ConversationMapper;
import com.voicehub.backend.mapper.ChatMessageMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Optional;
import java.util.Map;
import java.util.HashMap;

/**
 * 对话服务实现
 * 提供对话相关的业务逻辑处理
 */
@Service
@Transactional
public class ConversationService extends BaseServiceImpl<ConversationMapper, Conversation> {

    @Autowired
    private ConversationMapper conversationMapper;

    @Autowired
    private ChatMessageMapper chatMessageMapper;

    /**
     * 创建新对话
     */
    public Conversation createConversation(User user, String title, Conversation.ConversationType type) {
        Conversation conversation = new Conversation();
        conversation.setUser(user);
        conversation.setTitle(title);
        conversation.setType(type);
        conversation.setStatus(Conversation.ConversationStatus.ACTIVE);
        conversation.setMessageCount(0);
        conversation.setIsFavorite(false);
        conversation.setIsArchived(false);
        conversation.setCreatedAt(LocalDateTime.now());
        conversation.setUpdatedAt(LocalDateTime.now());
        conversation.setLastMessageAt(LocalDateTime.now());
        
        conversationMapper.insert(conversation);
        return conversation;
    }

    /**
     * 根据ID和用户查找对话
     */
    public Optional<Conversation> findByIdAndUser(Long id, User user) {
        Conversation conversation = conversationMapper.findByIdAndUserId(id, user.getId());
        return Optional.ofNullable(conversation);
    }

    /**
     * 查找用户的所有对话
     */
    public List<Conversation> findByUser(User user) {
        return conversationMapper.findByUserIdOrderByLastActivityAtDesc(user.getId());
    }

    /**
     * 分页查找用户的对话
     */
    public IPage<Conversation> findByUser(User user, int pageNum, int pageSize) {
        Page<Conversation> page = new Page<>(pageNum, pageSize);
        return conversationMapper.findByUserIdOrderByLastActivityAtDesc(user.getId(), page);
    }

    /**
     * 根据用户和状态查找对话
     */
    public List<Conversation> findByUserAndStatus(User user, Conversation.ConversationStatus status) {
        return conversationMapper.findByUserIdAndStatusOrderByLastActivityAtDesc(user.getId(), status.name());
    }

    /**
     * 根据用户和类型查找对话
     */
    public List<Conversation> findByUserAndType(User user, Conversation.ConversationType type) {
        return conversationMapper.findByUserIdAndTypeOrderByLastActivityAtDesc(user.getId(), type.name());
    }

    /**
     * 查找收藏的对话
     */
    public List<Conversation> findFavoriteConversations(User user) {
        return conversationMapper.findFavoriteConversations(user.getId());
    }

    /**
     * 查找已归档的对话
     */
    public List<Conversation> findArchivedConversations(User user) {
        return conversationMapper.findArchivedConversations(user.getId());
    }

    /**
     * 搜索对话标题
     */
    public List<Conversation> searchByTitle(User user, String keyword) {
        return conversationMapper.searchByTitle(user.getId(), keyword);
    }

    /**
     * 查找今天的对话
     */
    public List<Conversation> findTodayConversations(User user) {
        return conversationMapper.findTodayConversations(user.getId());
    }

    /**
     * 查找本周的对话
     */
    public List<Conversation> findThisWeekConversations(User user) {
        LocalDateTime weekStart = LocalDateTime.now().truncatedTo(ChronoUnit.DAYS).minusDays(7);
        return conversationMapper.findThisWeekConversations(user.getId(), weekStart);
    }

    /**
     * 查找本月的对话
     */
    public List<Conversation> findThisMonthConversations(User user) {
        LocalDateTime monthStart = LocalDateTime.now().withDayOfMonth(1).truncatedTo(ChronoUnit.DAYS);
        return conversationMapper.findThisMonthConversations(user.getId(), monthStart);
    }

    /**
     * 查找最近的对话
     */
    public List<Conversation> findRecentConversations(User user, int days) {
        LocalDateTime since = LocalDateTime.now().minusDays(days);
        return conversationMapper.findRecentConversations(user.getId(), since);
    }

    /**
     * 查找高情绪分数的对话
     */
    public List<Conversation> findPositiveConversations(User user, Double minScore) {
        return conversationMapper.findPositiveConversations(user.getId(), minScore);
    }

    /**
     * 更新对话标题
     */
    public Conversation updateTitle(Long conversationId, User user, String newTitle) {
        Conversation conversation = conversationMapper.findByIdAndUserId(conversationId, user.getId());
        if (conversation != null) {
            conversation.setTitle(newTitle);
            conversation.setUpdatedAt(LocalDateTime.now());
            conversationMapper.updateById(conversation);
        }
        return conversation;
    }

    /**
     * 切换收藏状态
     */
    public Conversation toggleFavorite(Long conversationId, User user) {
        Conversation conversation = conversationMapper.findByIdAndUserId(conversationId, user.getId());
        if (conversation != null) {
            conversation.setIsFavorite(!conversation.getIsFavorite());
            conversation.setUpdatedAt(LocalDateTime.now());
            conversationMapper.updateById(conversation);
        }
        return conversation;
    }

    /**
     * 切换归档状态
     */
    public Conversation toggleArchive(Long conversationId, User user) {
        Conversation conversation = conversationMapper.findByIdAndUserId(conversationId, user.getId());
        if (conversation != null) {
            conversation.setIsArchived(!conversation.getIsArchived());
            conversation.setUpdatedAt(LocalDateTime.now());
            conversationMapper.updateById(conversation);
        }
        return conversation;
    }

    /**
     * 更新对话状态
     */
    public Conversation updateStatus(Long conversationId, User user, Conversation.ConversationStatus status) {
        Conversation conversation = conversationMapper.findByIdAndUserId(conversationId, user.getId());
        if (conversation != null) {
            conversation.setStatus(status);
            conversation.setUpdatedAt(LocalDateTime.now());
            conversationMapper.updateById(conversation);
        }
        return conversation;
    }

    /**
     * 更新情绪分析
     */
    public void updateMoodAnalysis(Long conversationId, String emotionAnalysis, Double moodScore) {
        Conversation conversation = conversationMapper.selectById(conversationId);
        if (conversation != null) {
            conversation.setEmotionAnalysis(emotionAnalysis);
            conversation.setMoodScore(moodScore);
            conversation.setUpdatedAt(LocalDateTime.now());
            conversationMapper.updateById(conversation);
        }
    }

    /**
     * 统计用户对话总数
     */
    public long countByUser(User user) {
        return conversationMapper.countByUserId(user.getId());
    }

    /**
     * 统计用户活跃对话数
     */
    public long countActiveByUser(User user) {
        return conversationMapper.countActiveByUserId(user.getId());
    }

    /**
     * 统计用户收藏对话数
     */
    public long countFavoriteByUser(User user) {
        return conversationMapper.countFavoriteByUserId(user.getId());
    }

    // ========== Controller需要的方法 ==========

    /**
     * 发送语音消息
     */
    public ChatMessage sendVoiceMessage(Long conversationId, String content, String voiceFilePath, User user) {
        Conversation conversation = getConversation(conversationId, user);

        ChatMessage message = new ChatMessage();
        message.setConversationId(conversationId);
        message.setContent(content);
        message.setType(ChatMessage.MessageType.VOICE);
        message.setRole(ChatMessage.MessageRole.USER);
        message.setIsVoiceInput(true);
        message.setVoiceFilePath(voiceFilePath);
        message.setCreatedAt(LocalDateTime.now());

        chatMessageMapper.insert(message);

        // 更新对话信息
        conversation.setMessageCount(conversation.getMessageCount() + 1);
        conversation.setLastMessageAt(LocalDateTime.now());
        conversationMapper.updateById(conversation);

        return message;
    }

    /**
     * 发送文本消息
     */
    public ChatMessage sendMessage(Long conversationId, String content, User user) {
        Conversation conversation = getConversation(conversationId, user);

        ChatMessage message = new ChatMessage();
        message.setConversationId(conversationId);
        message.setContent(content);
        message.setType(ChatMessage.MessageType.TEXT);
        message.setRole(ChatMessage.MessageRole.USER);
        message.setIsVoiceInput(false);
        message.setCreatedAt(LocalDateTime.now());

        chatMessageMapper.insert(message);

        // 更新对话信息
        conversation.setMessageCount(conversation.getMessageCount() + 1);
        conversation.setLastMessageAt(LocalDateTime.now());
        conversationMapper.updateById(conversation);

        return message;
    }

    /**
     * 获取用户对话列表（分页）
     */
    public IPage<Conversation> getUserConversations(User user, Pageable pageable) {
        Page<Conversation> page = new Page<>(pageable.getPageNumber() + 1, pageable.getPageSize());
        return conversationMapper.findByUserId(user.getId(), page);
    }

    /**
     * 获取对话详情
     */
    public Conversation getConversation(Long conversationId, User user) {
        Conversation conversation = conversationMapper.selectById(conversationId);
        if (conversation == null || !conversation.getUserId().equals(user.getId())) {
            throw new RuntimeException("对话不存在或无权限访问");
        }
        return conversation;
    }

    /**
     * 获取对话消息列表
     */
    public List<ChatMessage> getConversationMessages(Long conversationId, User user) {
        // 验证权限
        getConversation(conversationId, user);
        return chatMessageMapper.findByConversationId(conversationId);
    }

    /**
     * 获取对话消息列表（分页）
     */
    public IPage<ChatMessage> getConversationMessages(Long conversationId, User user, Pageable pageable) {
        // 验证权限
        getConversation(conversationId, user);
        Page<ChatMessage> page = new Page<>(pageable.getPageNumber() + 1, pageable.getPageSize());
        return chatMessageMapper.findByConversationIdWithPage(conversationId, page);
    }

    /**
     * 更新对话
     */
    public Conversation updateConversation(Long conversationId, String title, Conversation.ConversationType type, User user) {
        Conversation conversation = getConversation(conversationId, user);
        conversation.setTitle(title);
        conversation.setType(type);
        conversation.setUpdatedAt(LocalDateTime.now());
        conversationMapper.updateById(conversation);
        return conversation;
    }

    /**
     * 删除对话
     */
    public void deleteConversation(Long conversationId, User user) {
        Conversation conversation = getConversation(conversationId, user);
        conversationMapper.deleteById(conversationId);
        // 同时删除相关消息
        chatMessageMapper.deleteByConversationId(conversationId);
    }

    /**
     * 搜索对话
     */
    public List<Conversation> searchConversations(User user, String keyword) {
        return conversationMapper.searchByUserIdAndKeyword(user.getId(), keyword);
    }

    /**
     * 根据类型获取对话
     */
    public List<Conversation> getConversationsByType(User user, Conversation.ConversationType type) {
        return conversationMapper.findByUserIdAndType(user.getId(), type.name());
    }

    /**
     * 获取收藏对话
     */
    public List<Conversation> getFavoriteConversations(User user) {
        return conversationMapper.findFavoriteByUserId(user.getId());
    }

    /**
     * 获取归档对话
     */
    public List<Conversation> getArchivedConversations(User user) {
        return conversationMapper.findArchivedByUserId(user.getId());
    }

    /**
     * 获取最近对话
     */
    public List<Conversation> getRecentConversations(User user, int limit) {
        return conversationMapper.findRecentByUserId(user.getId(), limit);
    }

    /**
     * 更新对话状态
     */
    public Conversation updateConversationStatus(Long conversationId, Conversation.ConversationStatus status, User user) {
        Conversation conversation = getConversation(conversationId, user);
        conversation.setStatus(status);
        conversation.setUpdatedAt(LocalDateTime.now());
        conversationMapper.updateById(conversation);
        return conversation;
    }

    /**
     * 生成对话摘要
     */
    public String generateSummary(Long conversationId, User user) {
        Conversation conversation = getConversation(conversationId, user);
        List<ChatMessage> messages = chatMessageMapper.findByConversationId(conversationId);

        // 简单的摘要生成逻辑
        StringBuilder summary = new StringBuilder();
        summary.append("对话包含 ").append(messages.size()).append(" 条消息");
        if (!messages.isEmpty()) {
            summary.append("，最新消息时间：").append(messages.get(messages.size() - 1).getCreatedAt());
        }

        conversation.setSummary(summary.toString());
        conversation.setUpdatedAt(LocalDateTime.now());
        conversationMapper.updateById(conversation);

        return summary.toString();
    }

    /**
     * 对话统计信息内部类
     */
    public static class ConversationStats {
        private long totalConversations;
        private long activeConversations;
        private long favoriteConversations;
        private long archivedConversations;

        public ConversationStats(long total, long active, long favorite, long archived) {
            this.totalConversations = total;
            this.activeConversations = active;
            this.favoriteConversations = favorite;
            this.archivedConversations = archived;
        }

        // Getters
        public long getTotalConversations() { return totalConversations; }
        public long getActiveConversations() { return activeConversations; }
        public long getFavoriteConversations() { return favoriteConversations; }
        public long getArchivedConversations() { return archivedConversations; }
    }

    /**
     * 获取用户对话统计
     */
    public ConversationStats getUserConversationStats(User user) {
        long total = countByUser(user);
        long active = countActiveByUser(user);
        long favorite = countFavoriteByUser(user);
        long archived = conversationMapper.countArchivedByUserId(user.getId());

        return new ConversationStats(total, active, favorite, archived);
    }

    /**
     * 搜索消息
     */
    public List<ChatMessage> searchMessages(User user, String keyword) {
        return chatMessageMapper.searchByUserIdAndKeyword(user.getId(), keyword);
    }

    /**
     * 根据情感获取消息
     */
    public List<ChatMessage> getMessagesByEmotion(User user, String emotion) {
        return chatMessageMapper.findByUserIdAndEmotion(user.getId(), emotion);
    }

    /**
     * 获取情感统计
     */
    public Map<String, Long> getEmotionStatistics(User user) {
        List<Map<String, Object>> stats = chatMessageMapper.getEmotionStatsByUserId(user.getId());
        Map<String, Long> result = new HashMap<>();

        for (Map<String, Object> stat : stats) {
            String emotion = (String) stat.get("emotion");
            Long count = (Long) stat.get("count");
            result.put(emotion, count);
        }

        return result;
    }
}
