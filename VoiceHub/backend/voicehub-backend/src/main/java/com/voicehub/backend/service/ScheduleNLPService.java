package com.voicehub.backend.service;

import com.voicehub.backend.entity.Schedule;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 日程自然语言处理服务
 * 解析语音指令并提取日程信息
 */
@Service
public class ScheduleNLPService {

    private static final Logger logger = LoggerFactory.getLogger(ScheduleNLPService.class);

    // 时间相关的正则表达式模式
    private static final Pattern TIME_PATTERN = Pattern.compile(
        "(?i)(\\d{1,2})(?:[:：](\\d{2}))?" +
        "\\s*(?:(am|pm|上午|下午|早上|晚上|中午))?", 
        Pattern.CASE_INSENSITIVE
    );

    private static final Pattern DATE_PATTERN = Pattern.compile(
        "(?i)(今天|明天|后天|今日|明日|" +
        "下周|下个月|" +
        "周一|周二|周三|周四|周五|周六|周日|" +
        "星期一|星期二|星期三|星期四|星期五|星期六|星期日|" +
        "\\d{1,2}月\\d{1,2}日?|" +
        "\\d{4}年\\d{1,2}月\\d{1,2}日?)",
        Pattern.CASE_INSENSITIVE
    );

    private static final Pattern DURATION_PATTERN = Pattern.compile(
        "(?i)(\\d+)\\s*(小时|分钟|hour|hours|minute|minutes|h|m)",
        Pattern.CASE_INSENSITIVE
    );

    // 优先级关键词
    private static final Map<String, Schedule.Priority> PRIORITY_KEYWORDS = new HashMap<>();
    static {
        PRIORITY_KEYWORDS.put("重要", Schedule.Priority.HIGH);
        PRIORITY_KEYWORDS.put("紧急", Schedule.Priority.URGENT);
        PRIORITY_KEYWORDS.put("高优先级", Schedule.Priority.HIGH);
        PRIORITY_KEYWORDS.put("低优先级", Schedule.Priority.LOW);
        PRIORITY_KEYWORDS.put("important", Schedule.Priority.HIGH);
        PRIORITY_KEYWORDS.put("urgent", Schedule.Priority.URGENT);
        PRIORITY_KEYWORDS.put("high", Schedule.Priority.HIGH);
        PRIORITY_KEYWORDS.put("low", Schedule.Priority.LOW);
    }

    /**
     * 解析语音指令并提取日程信息
     */
    public ScheduleParseResult parseVoiceCommand(String voiceCommand) {
        logger.info("Parsing voice command: {}", voiceCommand);
        
        ScheduleParseResult result = new ScheduleParseResult();
        result.setOriginalCommand(voiceCommand);
        result.setSuccess(false);

        try {
            // 提取标题
            String title = extractTitle(voiceCommand);
            if (title.isEmpty()) {
                result.setErrorMessage("无法识别日程标题");
                return result;
            }
            result.setTitle(title);

            // 提取时间信息
            LocalDateTime startTime = extractDateTime(voiceCommand);
            if (startTime == null) {
                result.setErrorMessage("无法识别时间信息");
                return result;
            }
            result.setStartTime(startTime);

            // 提取持续时间
            Integer duration = extractDuration(voiceCommand);
            if (duration != null) {
                result.setEndTime(startTime.plusMinutes(duration));
                result.setDurationMinutes(duration);
            }

            // 提取地点
            String location = extractLocation(voiceCommand);
            result.setLocation(location);

            // 提取优先级
            Schedule.Priority priority = extractPriority(voiceCommand);
            result.setPriority(priority);

            // 提取描述
            String description = extractDescription(voiceCommand);
            result.setDescription(description);

            result.setSuccess(true);
            logger.info("Successfully parsed schedule: {}", result);

        } catch (Exception e) {
            logger.error("Error parsing voice command: {}", e.getMessage());
            result.setErrorMessage("解析语音指令时发生错误: " + e.getMessage());
        }

        return result;
    }

    /**
     * 提取日程标题
     */
    private String extractTitle(String command) {
        // 移除常见的指令词
        String cleanCommand = command.replaceAll("(?i)(安排|创建|添加|新建|schedule|create|add)", "");
        cleanCommand = cleanCommand.replaceAll("(?i)(会议|meeting|约会|appointment)", "会议");
        
        // 提取主要内容作为标题
        String[] parts = cleanCommand.split("(?i)(在|at|时间|time|地点|location)");
        if (parts.length > 0) {
            String title = parts[0].trim();
            // 清理标题
            title = title.replaceAll("(?i)(今天|明天|后天|\\d{1,2}[:：]\\d{2})", "").trim();
            return title.isEmpty() ? "新日程" : title;
        }
        
        return "新日程";
    }

    /**
     * 提取日期时间
     */
    private LocalDateTime extractDateTime(String command) {
        LocalDateTime baseTime = LocalDateTime.now();
        
        // 提取日期
        Matcher dateMatcher = DATE_PATTERN.matcher(command);
        if (dateMatcher.find()) {
            String dateStr = dateMatcher.group(1);
            baseTime = parseDateString(dateStr, baseTime);
        }

        // 提取时间
        Matcher timeMatcher = TIME_PATTERN.matcher(command);
        if (timeMatcher.find()) {
            int hour = Integer.parseInt(timeMatcher.group(1));
            int minute = timeMatcher.group(2) != null ? Integer.parseInt(timeMatcher.group(2)) : 0;
            String period = timeMatcher.group(3);

            // 处理AM/PM
            if (period != null) {
                if (period.matches("(?i)(pm|下午|晚上)") && hour < 12) {
                    hour += 12;
                } else if (period.matches("(?i)(am|上午|早上)") && hour == 12) {
                    hour = 0;
                } else if (period.equals("中午") && hour == 12) {
                    // 中午12点保持不变
                }
            }

            baseTime = baseTime.withHour(hour).withMinute(minute).withSecond(0).withNano(0);
        } else {
            // 如果没有指定时间，默认设置为下一个整点
            baseTime = baseTime.plusHours(1).withMinute(0).withSecond(0).withNano(0);
        }

        return baseTime;
    }

    /**
     * 解析日期字符串
     */
    private LocalDateTime parseDateString(String dateStr, LocalDateTime baseTime) {
        switch (dateStr.toLowerCase()) {
            case "今天":
            case "今日":
                return baseTime;
            case "明天":
            case "明日":
                return baseTime.plusDays(1);
            case "后天":
                return baseTime.plusDays(2);
            case "下周":
                return baseTime.plusWeeks(1);
            case "下个月":
                return baseTime.plusMonths(1);
            default:
                // 处理星期
                if (dateStr.matches("(?i)(周|星期)[一二三四五六日1-7]")) {
                    int targetDay = parseWeekDay(dateStr);
                    int currentDay = baseTime.getDayOfWeek().getValue();
                    int daysToAdd = (targetDay - currentDay + 7) % 7;
                    if (daysToAdd == 0) daysToAdd = 7; // 下周同一天
                    return baseTime.plusDays(daysToAdd);
                }
                // 处理具体日期 (简化处理)
                return baseTime;
        }
    }

    /**
     * 解析星期
     */
    private int parseWeekDay(String weekStr) {
        if (weekStr.contains("一") || weekStr.contains("1")) return 1;
        if (weekStr.contains("二") || weekStr.contains("2")) return 2;
        if (weekStr.contains("三") || weekStr.contains("3")) return 3;
        if (weekStr.contains("四") || weekStr.contains("4")) return 4;
        if (weekStr.contains("五") || weekStr.contains("5")) return 5;
        if (weekStr.contains("六") || weekStr.contains("6")) return 6;
        if (weekStr.contains("日") || weekStr.contains("7")) return 7;
        return 1; // 默认周一
    }

    /**
     * 提取持续时间
     */
    private Integer extractDuration(String command) {
        Matcher matcher = DURATION_PATTERN.matcher(command);
        if (matcher.find()) {
            int value = Integer.parseInt(matcher.group(1));
            String unit = matcher.group(2).toLowerCase();
            
            if (unit.contains("小时") || unit.contains("hour") || unit.equals("h")) {
                return value * 60; // 转换为分钟
            } else if (unit.contains("分钟") || unit.contains("minute") || unit.equals("m")) {
                return value;
            }
        }
        
        // 默认持续时间1小时
        return 60;
    }

    /**
     * 提取地点
     */
    private String extractLocation(String command) {
        Pattern locationPattern = Pattern.compile("(?i)(在|at|地点|location)\\s*([^时间]+?)(?=\\s|$|时间|time)");
        Matcher matcher = locationPattern.matcher(command);
        if (matcher.find()) {
            return matcher.group(2).trim();
        }
        return null;
    }

    /**
     * 提取优先级
     */
    private Schedule.Priority extractPriority(String command) {
        for (Map.Entry<String, Schedule.Priority> entry : PRIORITY_KEYWORDS.entrySet()) {
            if (command.toLowerCase().contains(entry.getKey().toLowerCase())) {
                return entry.getValue();
            }
        }
        return Schedule.Priority.MEDIUM; // 默认中等优先级
    }

    /**
     * 提取描述
     */
    private String extractDescription(String command) {
        // 简化处理，将整个指令作为描述的一部分
        return "通过语音创建: " + command;
    }

    /**
     * 日程解析结果类
     */
    public static class ScheduleParseResult {
        private boolean success;
        private String errorMessage;
        private String originalCommand;
        private String title;
        private String description;
        private LocalDateTime startTime;
        private LocalDateTime endTime;
        private Integer durationMinutes;
        private String location;
        private Schedule.Priority priority = Schedule.Priority.MEDIUM;

        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }

        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }

        public String getOriginalCommand() { return originalCommand; }
        public void setOriginalCommand(String originalCommand) { this.originalCommand = originalCommand; }

        public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }

        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }

        public LocalDateTime getStartTime() { return startTime; }
        public void setStartTime(LocalDateTime startTime) { this.startTime = startTime; }

        public LocalDateTime getEndTime() { return endTime; }
        public void setEndTime(LocalDateTime endTime) { this.endTime = endTime; }

        public Integer getDurationMinutes() { return durationMinutes; }
        public void setDurationMinutes(Integer durationMinutes) { this.durationMinutes = durationMinutes; }

        public String getLocation() { return location; }
        public void setLocation(String location) { this.location = location; }

        public Schedule.Priority getPriority() { return priority; }
        public void setPriority(Schedule.Priority priority) { this.priority = priority; }

        @Override
        public String toString() {
            return "ScheduleParseResult{" +
                    "success=" + success +
                    ", title='" + title + '\'' +
                    ", startTime=" + startTime +
                    ", endTime=" + endTime +
                    ", location='" + location + '\'' +
                    ", priority=" + priority +
                    '}';
        }
    }
}