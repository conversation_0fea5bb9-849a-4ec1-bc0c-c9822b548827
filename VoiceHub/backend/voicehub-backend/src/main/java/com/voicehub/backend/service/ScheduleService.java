package com.voicehub.backend.service;

import com.voicehub.backend.entity.Schedule;
import com.voicehub.backend.entity.User;
import com.voicehub.backend.mapper.ScheduleMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;


/**
 * 日程管理服务
 * 提供日程的CRUD操作和业务逻辑
 */
@Service
@Transactional
public class ScheduleService {

    private static final Logger logger = LoggerFactory.getLogger(ScheduleService.class);

    @Autowired
    private ScheduleMapper scheduleMapper;

    @Autowired
    private ScheduleNLPService scheduleNLPService;

    /**
     * 通过语音指令创建日程
     */
    public Schedule createScheduleFromVoice(String voiceCommand, User user) {
        logger.info("Creating schedule from voice command for user: {}", user.getUsername());
        
        // 解析语音指令
        ScheduleNLPService.ScheduleParseResult parseResult = scheduleNLPService.parseVoiceCommand(voiceCommand);
        
        if (!parseResult.isSuccess()) {
            throw new RuntimeException("Failed to parse voice command: " + parseResult.getErrorMessage());
        }

        // 检查时间冲突
        if (parseResult.getEndTime() != null) {
            List<Schedule> conflicts = scheduleMapper.findConflictingSchedules(
                user.getId(), parseResult.getStartTime(), parseResult.getEndTime());
            
            if (!conflicts.isEmpty()) {
                logger.warn("Schedule conflict detected for user: {}", user.getUsername());
                // 可以选择抛出异常或者调整时间
                // throw new RuntimeException("Schedule conflict detected");
            }
        }

        // 创建日程对象
        Schedule schedule = new Schedule();
        schedule.setTitle(parseResult.getTitle());
        schedule.setDescription(parseResult.getDescription());
        schedule.setStartTime(parseResult.getStartTime());
        schedule.setEndTime(parseResult.getEndTime());
        schedule.setLocation(parseResult.getLocation());
        schedule.setPriority(parseResult.getPriority());
        schedule.setUser(user);
        schedule.setVoiceCommand(voiceCommand);
        schedule.setCreatedByVoice(true);
        schedule.setStatus(Schedule.Status.SCHEDULED);

        // 设置默认提醒时间（提前15分钟）
        schedule.setReminderMinutes(15);

        scheduleMapper.insert(schedule);
        logger.info("Successfully created schedule: {}", schedule.getId());

        return schedule;
    }

    /**
     * 创建日程
     */
    public Schedule createSchedule(Schedule schedule, User user) {
        schedule.setUser(user);
        
        // 检查时间冲突
        if (schedule.getEndTime() != null) {
            List<Schedule> conflicts = scheduleMapper.findConflictingSchedules(
                user.getId(), schedule.getStartTime(), schedule.getEndTime());

            if (!conflicts.isEmpty()) {
                logger.warn("Schedule conflict detected for user: {}", user.getUsername());
            }
        }

        schedule.setUserId(user.getId());
        scheduleMapper.insert(schedule);
        return schedule;
    }

    /**
     * 更新日程
     */
    public Schedule updateSchedule(Long scheduleId, Schedule updatedSchedule, User user) {
        Schedule existingSchedule = scheduleMapper.findByIdAndUserId(scheduleId, user.getId());
        
        if (existingSchedule == null) {
            throw new RuntimeException("Schedule not found or access denied");
        }

        Schedule schedule = existingSchedule;
        
        // 更新字段
        if (updatedSchedule.getTitle() != null) {
            schedule.setTitle(updatedSchedule.getTitle());
        }
        if (updatedSchedule.getDescription() != null) {
            schedule.setDescription(updatedSchedule.getDescription());
        }
        if (updatedSchedule.getStartTime() != null) {
            schedule.setStartTime(updatedSchedule.getStartTime());
        }
        if (updatedSchedule.getEndTime() != null) {
            schedule.setEndTime(updatedSchedule.getEndTime());
        }
        if (updatedSchedule.getLocation() != null) {
            schedule.setLocation(updatedSchedule.getLocation());
        }
        if (updatedSchedule.getPriority() != null) {
            schedule.setPriority(updatedSchedule.getPriority());
        }
        if (updatedSchedule.getStatus() != null) {
            schedule.setStatus(updatedSchedule.getStatus());
        }
        if (updatedSchedule.getReminderMinutes() != null) {
            schedule.setReminderMinutes(updatedSchedule.getReminderMinutes());
        }

        scheduleMapper.updateById(schedule);
        return schedule;
    }

    /**
     * 删除日程
     */
    public void deleteSchedule(Long scheduleId, User user) {
        Schedule schedule = scheduleMapper.findByIdAndUserId(scheduleId, user.getId());

        if (schedule == null) {
            throw new RuntimeException("Schedule not found or access denied");
        }

        scheduleMapper.deleteById(scheduleId);
        logger.info("Deleted schedule: {} for user: {}", scheduleId, user.getUsername());
    }

    /**
     * 获取用户的所有日程
     */
    public List<Schedule> getUserSchedules(User user) {
        return scheduleMapper.findByUserOrderByStartTimeAsc(user.getId());
    }

    /**
     * 分页获取用户日程
     */
    public com.baomidou.mybatisplus.core.metadata.IPage<Schedule> getUserSchedules(User user, com.baomidou.mybatisplus.extension.plugins.pagination.Page<Schedule> page) {
        return scheduleMapper.findByUserOrderByStartTimeDesc(user.getId(), page);
    }

    /**
     * 获取用户今天的日程
     */
    public List<Schedule> getTodaySchedules(User user) {
        return scheduleMapper.findTodaySchedules(user.getId());
    }

    /**
     * 获取用户即将到来的日程
     */
    public List<Schedule> getUpcomingSchedules(User user, int days) {
        LocalDateTime endTime = LocalDateTime.now().plusDays(days);
        return scheduleMapper.findUpcomingSchedules(user.getId(), endTime);
    }

    /**
     * 获取用户指定时间范围内的日程
     */
    public List<Schedule> getSchedulesByTimeRange(User user, LocalDateTime startTime, LocalDateTime endTime) {
        return scheduleMapper.findByUserAndTimeRange(user.getId(), startTime, endTime);
    }

    /**
     * 搜索日程
     */
    public List<Schedule> searchSchedules(User user, String keyword) {
        return scheduleMapper.searchByKeyword(user.getId(), keyword);
    }

    /**
     * 获取用户本周的日程
     */
    public List<Schedule> getWeeklySchedules(User user) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime weekStart = now.truncatedTo(ChronoUnit.DAYS).minusDays(now.getDayOfWeek().getValue() - 1);
        LocalDateTime weekEnd = weekStart.plusWeeks(1);

        return scheduleMapper.findWeeklySchedules(user.getId(), weekStart, weekEnd);
    }

    /**
     * 获取用户本月的日程
     */
    public List<Schedule> getMonthlySchedules(User user) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime monthStart = now.withDayOfMonth(1).truncatedTo(ChronoUnit.DAYS);
        LocalDateTime monthEnd = monthStart.plusMonths(1);

        return scheduleMapper.findMonthlySchedules(user.getId(), monthStart, monthEnd);
    }

    /**
     * 获取过期的日程
     */
    public List<Schedule> getOverdueSchedules(User user) {
        return scheduleMapper.findOverdueSchedules(user.getId());
    }

    /**
     * 获取需要提醒的日程
     */
    public List<Schedule> getSchedulesForReminder() {
        LocalDateTime reminderTime = LocalDateTime.now().plusMinutes(30); // 提前30分钟提醒
        return scheduleMapper.findSchedulesForReminder(reminderTime);
    }

    /**
     * 标记日程为完成
     */
    public Schedule completeSchedule(Long scheduleId, User user) {
        Schedule schedule = scheduleMapper.findByIdAndUserId(scheduleId, user.getId());

        if (schedule == null) {
            throw new RuntimeException("Schedule not found or access denied");
        }

        schedule.setStatus(Schedule.Status.COMPLETED);
        scheduleMapper.updateById(schedule);
        return schedule;
    }

    /**
     * 取消日程
     */
    public Schedule cancelSchedule(Long scheduleId, User user) {
        Schedule schedule = scheduleMapper.findByIdAndUserId(scheduleId, user.getId());

        if (schedule == null) {
            throw new RuntimeException("Schedule not found or access denied");
        }

        schedule.setStatus(Schedule.Status.CANCELLED);
        scheduleMapper.updateById(schedule);
        return schedule;
    }

    /**
     * 推迟日程
     */
    public Schedule postponeSchedule(Long scheduleId, User user, int minutesToAdd) {
        Schedule schedule = scheduleMapper.findByIdAndUserId(scheduleId, user.getId());

        if (schedule == null) {
            throw new RuntimeException("Schedule not found or access denied");
        }

        schedule.setStartTime(schedule.getStartTime().plusMinutes(minutesToAdd));
        if (schedule.getEndTime() != null) {
            schedule.setEndTime(schedule.getEndTime().plusMinutes(minutesToAdd));
        }
        schedule.setStatus(Schedule.Status.POSTPONED);

        scheduleMapper.updateById(schedule);
        return schedule;
    }

    /**
     * 获取用户日程统计信息
     */
    public ScheduleStats getUserScheduleStats(User user) {
        ScheduleStats stats = new ScheduleStats();

        stats.setTotalSchedules(scheduleMapper.countByUser(user.getId()));
        stats.setScheduledCount(scheduleMapper.countByUserAndStatus(user.getId(), Schedule.Status.SCHEDULED.name()));
        stats.setCompletedCount(scheduleMapper.countByUserAndStatus(user.getId(), Schedule.Status.COMPLETED.name()));
        stats.setCancelledCount(scheduleMapper.countByUserAndStatus(user.getId(), Schedule.Status.CANCELLED.name()));

        stats.setTodaySchedules(scheduleMapper.findTodaySchedules(user.getId()).size());
        stats.setUpcomingSchedules(scheduleMapper.findUpcomingSchedules(user.getId(), LocalDateTime.now().plusDays(7)).size());
        stats.setOverdueSchedules(scheduleMapper.findOverdueSchedules(user.getId()).size());

        return stats;
    }

    /**
     * 日程统计信息类
     */
    public static class ScheduleStats {
        private long totalSchedules;
        private long scheduledCount;
        private long completedCount;
        private long cancelledCount;
        private int todaySchedules;
        private int upcomingSchedules;
        private int overdueSchedules;

        // Getters and Setters
        public long getTotalSchedules() { return totalSchedules; }
        public void setTotalSchedules(long totalSchedules) { this.totalSchedules = totalSchedules; }

        public long getScheduledCount() { return scheduledCount; }
        public void setScheduledCount(long scheduledCount) { this.scheduledCount = scheduledCount; }

        public long getCompletedCount() { return completedCount; }
        public void setCompletedCount(long completedCount) { this.completedCount = completedCount; }

        public long getCancelledCount() { return cancelledCount; }
        public void setCancelledCount(long cancelledCount) { this.cancelledCount = cancelledCount; }

        public int getTodaySchedules() { return todaySchedules; }
        public void setTodaySchedules(int todaySchedules) { this.todaySchedules = todaySchedules; }

        public int getUpcomingSchedules() { return upcomingSchedules; }
        public void setUpcomingSchedules(int upcomingSchedules) { this.upcomingSchedules = upcomingSchedules; }

        public int getOverdueSchedules() { return overdueSchedules; }
        public void setOverdueSchedules(int overdueSchedules) { this.overdueSchedules = overdueSchedules; }
    }
}