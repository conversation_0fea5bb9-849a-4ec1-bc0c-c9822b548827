package com.voicehub.backend.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * 语音转文本服务
 * 集成百度语音识别API
 */
@Service
public class SpeechToTextService {

    private static final Logger logger = LoggerFactory.getLogger(SpeechToTextService.class);

    @Value("${ai.speech.baidu.app-id}")
    private String baiduAppId;

    @Value("${ai.speech.baidu.api-key}")
    private String baiduApiKey;

    @Value("${ai.speech.baidu.secret-key}")
    private String baiduSecretKey;

    private final RestTemplate restTemplate = new RestTemplate();
    private String accessToken;
    private long tokenExpireTime = 0;

    /**
     * 语音转文本主方法
     */
    public String convertSpeechToText(byte[] audioData, String format, int rate) {
        try {
            // 获取访问令牌
            String token = getAccessToken();
            if (token == null) {
                throw new RuntimeException("Failed to get access token");
            }

            // 调用语音识别API
            return recognizeSpeech(audioData, format, rate, token);

        } catch (Exception e) {
            logger.error("Speech to text conversion failed: {}", e.getMessage());
            throw new RuntimeException("语音识别失败: " + e.getMessage());
        }
    }

    /**
     * 获取百度API访问令牌
     */
    private String getAccessToken() {
        // 检查token是否过期
        if (accessToken != null && System.currentTimeMillis() < tokenExpireTime) {
            return accessToken;
        }

        try {
            String url = "https://aip.baidubce.com/oauth/2.0/token";
            
            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
            params.add("grant_type", "client_credentials");
            params.add("client_id", baiduApiKey);
            params.add("client_secret", baiduSecretKey);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);
            
            ResponseEntity<Map> response = restTemplate.postForEntity(url, request, Map.class);
            
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                accessToken = (String) responseBody.get("access_token");
                Integer expiresIn = (Integer) responseBody.get("expires_in");
                
                // 设置token过期时间（提前5分钟刷新）
                tokenExpireTime = System.currentTimeMillis() + (expiresIn - 300) * 1000L;
                
                logger.info("Successfully obtained Baidu API access token");
                return accessToken;
            }

        } catch (Exception e) {
            logger.error("Failed to get access token: {}", e.getMessage());
        }

        return null;
    }

    /**
     * 调用百度语音识别API
     */
    private String recognizeSpeech(byte[] audioData, String format, int rate, String token) {
        try {
            String url = "https://vop.baidu.com/server_api";
            
            // 将音频数据转换为Base64
            String audioBase64 = Base64.getEncoder().encodeToString(audioData);
            
            // 构建请求参数
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("format", format.toLowerCase());
            requestBody.put("rate", rate);
            requestBody.put("channel", 1);
            requestBody.put("cuid", "voicehub_" + System.currentTimeMillis());
            requestBody.put("token", token);
            requestBody.put("speech", audioBase64);
            requestBody.put("len", audioData.length);
            
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.add("Accept", "application/json");

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);
            
            // 发送请求
            ResponseEntity<Map> response = restTemplate.postForEntity(url, request, Map.class);
            
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                Integer errNo = (Integer) responseBody.get("err_no");
                
                if (errNo != null && errNo == 0) {
                    // 识别成功
                    Object resultObj = responseBody.get("result");
                    if (resultObj instanceof java.util.List) {
                        java.util.List<?> resultList = (java.util.List<?>) resultObj;
                        if (!resultList.isEmpty()) {
                            String recognizedText = resultList.get(0).toString();
                            logger.info("Speech recognition successful: {}", recognizedText);
                            return recognizedText;
                        }
                    }
                } else {
                    String errMsg = (String) responseBody.get("err_msg");
                    logger.error("Baidu API error: {} - {}", errNo, errMsg);
                    throw new RuntimeException("语音识别API错误: " + errMsg);
                }
            }

        } catch (Exception e) {
            logger.error("Speech recognition request failed: {}", e.getMessage());
            throw new RuntimeException("语音识别请求失败: " + e.getMessage());
        }

        return null;
    }

    /**
     * 支持的音频格式检查
     */
    public boolean isSupportedFormat(String format) {
        String[] supportedFormats = {"wav", "pcm", "opus", "amr", "m4a"};
        for (String supportedFormat : supportedFormats) {
            if (supportedFormat.equalsIgnoreCase(format)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取推荐的采样率
     */
    public int getRecommendedSampleRate(String format) {
        switch (format.toLowerCase()) {
            case "wav":
            case "pcm":
                return 16000; // 16kHz for better accuracy
            case "opus":
                return 24000;
            case "amr":
                return 8000;
            case "m4a":
                return 16000;
            default:
                return 16000;
        }
    }
}