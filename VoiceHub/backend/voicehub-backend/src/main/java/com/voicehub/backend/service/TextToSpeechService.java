package com.voicehub.backend.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Text-to-Speech Service
 * Integrates with Baidu TTS API for voice synthesis
 */
@Service
public class TextToSpeechService {

    private static final Logger logger = LoggerFactory.getLogger(TextToSpeechService.class);

    @Value("${baidu.tts.api-key}")
    private String apiKey;

    @Value("${baidu.tts.secret-key}")
    private String secretKey;

    @Value("${baidu.tts.token-url:https://aip.baidubce.com/oauth/2.0/token}")
    private String tokenUrl;

    @Value("${baidu.tts.synthesis-url:https://tsn.baidu.com/text2audio}")
    private String synthesisUrl;

    @Value("${app.upload.dir:uploads}")
    private String uploadDir;

    private final RestTemplate restTemplate = new RestTemplate();
    private String accessToken;
    private long tokenExpiryTime;

    /**
     * Voice configuration options
     */
    public enum Voice {
        FEMALE_STANDARD(0, "度小美", "female"),
        MALE_STANDARD(1, "度小宇", "male"),
        FEMALE_EMOTIONAL(3, "度逍遥", "female"),
        MALE_EMOTIONAL(4, "度丫丫", "male"),
        FEMALE_SWEET(5, "度小娇", "female"),
        MALE_MAGNETIC(6, "度米朵", "male"),
        FEMALE_WARM(7, "度博文", "female"),
        MALE_DEEP(8, "度小童", "male");

        private final int value;
        private final String name;
        private final String gender;

        Voice(int value, String name, String gender) {
            this.value = value;
            this.name = name;
            this.gender = gender;
        }

        public int getValue() { return value; }
        public String getName() { return name; }
        public String getGender() { return gender; }
    }

    /**
     * Audio format options
     */
    public enum AudioFormat {
        MP3(3, "mp3", "audio/mpeg"),
        PCM(4, "pcm", "audio/pcm"),
        WAV(5, "wav", "audio/wav"),
        AMR(6, "amr", "audio/amr");

        private final int value;
        private final String extension;
        private final String mimeType;

        AudioFormat(int value, String extension, String mimeType) {
            this.value = value;
            this.extension = extension;
            this.mimeType = mimeType;
        }

        public int getValue() { return value; }
        public String getExtension() { return extension; }
        public String getMimeType() { return mimeType; }
    }

    /**
     * TTS synthesis parameters
     */
    public static class TTSRequest {
        private String text;
        private Voice voice = Voice.FEMALE_STANDARD;
        private AudioFormat format = AudioFormat.MP3;
        private int speed = 5; // 0-15, 5 is normal
        private int pitch = 5; // 0-15, 5 is normal
        private int volume = 5; // 0-15, 5 is normal
        private String language = "zh"; // zh for Chinese, en for English

        // Constructors
        public TTSRequest() {}

        public TTSRequest(String text) {
            this.text = text;
        }

        public TTSRequest(String text, Voice voice) {
            this.text = text;
            this.voice = voice;
        }

        // Getters and Setters
        public String getText() { return text; }
        public void setText(String text) { this.text = text; }

        public Voice getVoice() { return voice; }
        public void setVoice(Voice voice) { this.voice = voice; }

        public AudioFormat getFormat() { return format; }
        public void setFormat(AudioFormat format) { this.format = format; }

        public int getSpeed() { return speed; }
        public void setSpeed(int speed) { 
            this.speed = Math.max(0, Math.min(15, speed)); 
        }

        public int getPitch() { return pitch; }
        public void setPitch(int pitch) { 
            this.pitch = Math.max(0, Math.min(15, pitch)); 
        }

        public int getVolume() { return volume; }
        public void setVolume(int volume) { 
            this.volume = Math.max(0, Math.min(15, volume)); 
        }

        public String getLanguage() { return language; }
        public void setLanguage(String language) { this.language = language; }
    }

    /**
     * TTS synthesis result
     */
    public static class TTSResult {
        private byte[] audioData;
        private String audioFormat;
        private String mimeType;
        private String filePath;
        private long fileSize;
        private double durationSeconds;

        public TTSResult(byte[] audioData, String audioFormat, String mimeType) {
            this.audioData = audioData;
            this.audioFormat = audioFormat;
            this.mimeType = mimeType;
            this.fileSize = audioData.length;
        }

        // Getters and Setters
        public byte[] getAudioData() { return audioData; }
        public void setAudioData(byte[] audioData) { this.audioData = audioData; }

        public String getAudioFormat() { return audioFormat; }
        public void setAudioFormat(String audioFormat) { this.audioFormat = audioFormat; }

        public String getMimeType() { return mimeType; }
        public void setMimeType(String mimeType) { this.mimeType = mimeType; }

        public String getFilePath() { return filePath; }
        public void setFilePath(String filePath) { this.filePath = filePath; }

        public long getFileSize() { return fileSize; }
        public void setFileSize(long fileSize) { this.fileSize = fileSize; }

        public double getDurationSeconds() { return durationSeconds; }
        public void setDurationSeconds(double durationSeconds) { this.durationSeconds = durationSeconds; }
    }

    /**
     * Convert text to speech
     */
    public TTSResult synthesizeSpeech(String text) throws IOException {
        return synthesizeSpeech(new TTSRequest(text));
    }

    /**
     * Convert text to speech with custom voice
     */
    public TTSResult synthesizeSpeech(String text, Voice voice) throws IOException {
        return synthesizeSpeech(new TTSRequest(text, voice));
    }

    /**
     * Convert text to speech with full configuration
     */
    public TTSResult synthesizeSpeech(TTSRequest request) throws IOException {
        logger.info("Starting TTS synthesis for text: {}", request.getText().substring(0, Math.min(50, request.getText().length())));

        // Validate input
        if (request.getText() == null || request.getText().trim().isEmpty()) {
            throw new IllegalArgumentException("Text cannot be empty");
        }

        if (request.getText().length() > 1024) {
            throw new IllegalArgumentException("Text length cannot exceed 1024 characters");
        }

        try {
            // Get access token
            String token = getAccessToken();

            // Prepare request parameters
            Map<String, Object> params = new HashMap<>();
            params.put("tex", request.getText());
            params.put("tok", token);
            params.put("cuid", "voicehub_" + System.currentTimeMillis());
            params.put("ctp", 1); // Client type
            params.put("lan", request.getLanguage());
            params.put("spd", request.getSpeed());
            params.put("pit", request.getPitch());
            params.put("vol", request.getVolume());
            params.put("per", request.getVoice().getValue());
            params.put("aue", request.getFormat().getValue());

            // Build URL with parameters
            StringBuilder urlBuilder = new StringBuilder(synthesisUrl + "?");
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                urlBuilder.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
            }
            String requestUrl = urlBuilder.toString().replaceAll("&$", "");

            // Make HTTP request
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            headers.set("User-Agent", "VoiceHub/1.0");

            HttpEntity<String> entity = new HttpEntity<>(headers);
            ResponseEntity<byte[]> response = restTemplate.exchange(
                requestUrl, HttpMethod.POST, entity, byte[].class);

            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                byte[] audioData = response.getBody();

                // Check if response is error (JSON format)
                if (isJsonError(audioData)) {
                    String errorMsg = new String(audioData);
                    logger.error("TTS API error: {}", errorMsg);
                    throw new IOException("TTS synthesis failed: " + errorMsg);
                }

                // Create result
                TTSResult result = new TTSResult(audioData, request.getFormat().getExtension(), 
                    request.getFormat().getMimeType());

                // Estimate duration (rough calculation)
                double estimatedDuration = estimateAudioDuration(request.getText(), request.getSpeed());
                result.setDurationSeconds(estimatedDuration);

                logger.info("TTS synthesis completed successfully. Audio size: {} bytes, Duration: {} seconds", 
                    audioData.length, estimatedDuration);

                return result;

            } else {
                throw new IOException("TTS API request failed with status: " + response.getStatusCode());
            }

        } catch (Exception e) {
            logger.error("TTS synthesis failed: {}", e.getMessage(), e);
            throw new IOException("TTS synthesis failed: " + e.getMessage(), e);
        }
    }

    /**
     * Convert text to speech and save to file
     */
    public TTSResult synthesizeAndSave(String text, String userId) throws IOException {
        return synthesizeAndSave(new TTSRequest(text), userId);
    }

    /**
     * Convert text to speech and save to file with custom configuration
     */
    public TTSResult synthesizeAndSave(TTSRequest request, String userId) throws IOException {
        TTSResult result = synthesizeSpeech(request);

        // Save to file
        String fileName = generateFileName(request.getFormat().getExtension());
        Path userDir = Paths.get(uploadDir, "tts", userId);
        Files.createDirectories(userDir);

        Path filePath = userDir.resolve(fileName);
        Files.write(filePath, result.getAudioData());

        result.setFilePath(filePath.toString());

        logger.info("TTS audio saved to file: {}", filePath);
        return result;
    }

    /**
     * Convert text to Base64 encoded audio
     */
    public String synthesizeToBase64(String text) throws IOException {
        TTSResult result = synthesizeSpeech(text);
        return Base64.getEncoder().encodeToString(result.getAudioData());
    }

    /**
     * Convert text to Base64 encoded audio with custom voice
     */
    public String synthesizeToBase64(String text, Voice voice) throws IOException {
        TTSResult result = synthesizeSpeech(text, voice);
        return Base64.getEncoder().encodeToString(result.getAudioData());
    }

    /**
     * Get available voices
     */
    public Voice[] getAvailableVoices() {
        return Voice.values();
    }

    /**
     * Get available audio formats
     */
    public AudioFormat[] getAvailableFormats() {
        return AudioFormat.values();
    }

    /**
     * Test TTS service connectivity
     */
    public boolean testConnection() {
        try {
            String testText = "测试语音合成服务";
            TTSResult result = synthesizeSpeech(testText);
            return result != null && result.getAudioData().length > 0;
        } catch (Exception e) {
            logger.error("TTS connection test failed: {}", e.getMessage());
            return false;
        }
    }

    // Private helper methods

    /**
     * Get access token for Baidu API
     */
    private String getAccessToken() throws IOException {
        // Check if token is still valid
        if (accessToken != null && System.currentTimeMillis() < tokenExpiryTime) {
            return accessToken;
        }

        logger.info("Requesting new access token for TTS service");

        try {
            String url = tokenUrl + "?grant_type=client_credentials&client_id=" + apiKey + "&client_secret=" + secretKey;

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<String> entity = new HttpEntity<>(headers);
            ResponseEntity<Map> response = restTemplate.exchange(url, HttpMethod.POST, entity, Map.class);

            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                
                if (responseBody.containsKey("access_token")) {
                    accessToken = (String) responseBody.get("access_token");
                    Integer expiresIn = (Integer) responseBody.get("expires_in");
                    tokenExpiryTime = System.currentTimeMillis() + (expiresIn * 1000L) - 60000; // 1 minute buffer

                    logger.info("Successfully obtained access token, expires in {} seconds", expiresIn);
                    return accessToken;
                } else {
                    throw new IOException("Invalid token response: " + responseBody);
                }
            } else {
                throw new IOException("Token request failed with status: " + response.getStatusCode());
            }

        } catch (Exception e) {
            logger.error("Failed to get access token: {}", e.getMessage(), e);
            throw new IOException("Failed to get access token: " + e.getMessage(), e);
        }
    }

    /**
     * Check if response is JSON error
     */
    private boolean isJsonError(byte[] data) {
        if (data.length < 10) return false;
        String start = new String(data, 0, Math.min(10, data.length));
        return start.trim().startsWith("{");
    }

    /**
     * Estimate audio duration based on text length and speed
     */
    private double estimateAudioDuration(String text, int speed) {
        // Rough estimation: Chinese characters ~0.5s each, English words ~0.3s each
        // Speed factor: 0-15 scale, 5 is normal (1.0x), 0 is slowest (0.5x), 15 is fastest (2.0x)
        double speedFactor = 0.5 + (speed / 15.0) * 1.5; // 0.5x to 2.0x

        int chineseChars = text.replaceAll("[^\\u4e00-\\u9fa5]", "").length();
        int englishWords = text.replaceAll("[\\u4e00-\\u9fa5]", "").split("\\s+").length;

        double baseDuration = (chineseChars * 0.5) + (englishWords * 0.3);
        return baseDuration / speedFactor;
    }

    /**
     * Generate unique file name
     */
    private String generateFileName(String extension) {
        return "tts_" + UUID.randomUUID().toString().replace("-", "") + "." + extension;
    }
}