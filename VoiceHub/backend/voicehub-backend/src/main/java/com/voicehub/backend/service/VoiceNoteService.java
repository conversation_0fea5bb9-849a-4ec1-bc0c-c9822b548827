package com.voicehub.backend.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.voicehub.backend.entity.User;
import com.voicehub.backend.entity.VoiceNote;
import com.voicehub.backend.mapper.VoiceNoteMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.HashSet;

/**
 * 语音笔记服务实现
 * 提供语音笔记相关的业务逻辑处理
 */
@Service
@Transactional
public class VoiceNoteService extends BaseServiceImpl<VoiceNoteMapper, VoiceNote> {

    @Autowired
    private VoiceNoteMapper voiceNoteMapper;

    /**
     * 创建语音笔记
     */
    public VoiceNote createVoiceNote(User user, String title, String description, 
                                   VoiceNote.Category category, VoiceNote.Priority priority, 
                                   Set<String> tags) {
        VoiceNote voiceNote = new VoiceNote();
        voiceNote.setUser(user);
        voiceNote.setTitle(title);
        voiceNote.setDescription(description);
        voiceNote.setCategory(category != null ? category : VoiceNote.Category.GENERAL);
        voiceNote.setPriority(priority != null ? priority : VoiceNote.Priority.MEDIUM);
        voiceNote.setTags(tags);
        voiceNote.setIsFavorite(false);
        voiceNote.setIsArchived(false);
        voiceNote.setCreatedAt(LocalDateTime.now());
        voiceNote.setUpdatedAt(LocalDateTime.now());
        
        voiceNoteMapper.insert(voiceNote);
        return voiceNote;
    }

    /**
     * 根据ID和用户查找语音笔记
     */
    public Optional<VoiceNote> findByIdAndUser(Long id, User user) {
        VoiceNote voiceNote = voiceNoteMapper.findByIdAndUserId(id, user.getId());
        return Optional.ofNullable(voiceNote);
    }

    /**
     * 获取用户的所有语音笔记
     */
    public List<VoiceNote> getUserVoiceNotes(User user) {
        return voiceNoteMapper.findByUserIdAndIsArchivedFalseOrderByCreatedAtDesc(user.getId());
    }

    /**
     * 分页获取用户的语音笔记
     */
    public IPage<VoiceNote> getUserVoiceNotes(User user, int pageNum, int pageSize) {
        Page<VoiceNote> page = new Page<>(pageNum, pageSize);
        return voiceNoteMapper.findByUserIdOrderByCreatedAtDesc(user.getId(), page);
    }

    /**
     * 搜索语音笔记
     */
    public List<VoiceNote> searchVoiceNotes(User user, String keyword) {
        return voiceNoteMapper.searchByKeyword(user.getId(), keyword);
    }

    /**
     * 根据分类获取语音笔记
     */
    public List<VoiceNote> getVoiceNotesByCategory(User user, VoiceNote.Category category) {
        return voiceNoteMapper.findByUserIdAndCategoryOrderByCreatedAtDesc(user.getId(), category.name());
    }

    /**
     * 根据优先级获取语音笔记
     */
    public List<VoiceNote> getVoiceNotesByPriority(User user, VoiceNote.Priority priority) {
        return voiceNoteMapper.findByUserIdAndPriorityOrderByCreatedAtDesc(user.getId(), priority.name());
    }

    /**
     * 获取收藏的语音笔记
     */
    public List<VoiceNote> getFavoriteVoiceNotes(User user) {
        return voiceNoteMapper.findByUserIdAndIsFavoriteTrueOrderByCreatedAtDesc(user.getId());
    }

    /**
     * 获取已归档的语音笔记
     */
    public List<VoiceNote> getArchivedVoiceNotes(User user) {
        return voiceNoteMapper.findByUserIdAndIsArchivedTrueOrderByCreatedAtDesc(user.getId());
    }

    /**
     * 根据标签获取语音笔记
     */
    public List<VoiceNote> getVoiceNotesByTag(User user, String tag) {
        return voiceNoteMapper.findByUserIdAndTag(user.getId(), tag.toLowerCase());
    }

    /**
     * 获取最近的语音笔记
     */
    public List<VoiceNote> getRecentVoiceNotes(User user, int days) {
        LocalDateTime since = LocalDateTime.now().minusDays(days);
        return voiceNoteMapper.findRecentVoiceNotes(user.getId(), since);
    }

    /**
     * 更新语音笔记
     */
    public VoiceNote updateVoiceNote(Long id, User user, String title, String description, 
                                   VoiceNote.Category category, VoiceNote.Priority priority, 
                                   Set<String> tags) {
        VoiceNote voiceNote = voiceNoteMapper.findByIdAndUserId(id, user.getId());
        if (voiceNote != null) {
            voiceNote.setTitle(title);
            voiceNote.setDescription(description);
            voiceNote.setCategory(category);
            voiceNote.setPriority(priority);
            voiceNote.setTags(tags);
            voiceNote.setUpdatedAt(LocalDateTime.now());
            voiceNoteMapper.updateById(voiceNote);
        }
        return voiceNote;
    }

    /**
     * 切换收藏状态
     */
    public VoiceNote toggleFavorite(Long id, User user) {
        VoiceNote voiceNote = voiceNoteMapper.findByIdAndUserId(id, user.getId());
        if (voiceNote != null) {
            voiceNote.setIsFavorite(!voiceNote.getIsFavorite());
            voiceNote.setUpdatedAt(LocalDateTime.now());
            voiceNoteMapper.updateById(voiceNote);
        }
        return voiceNote;
    }

    /**
     * 切换归档状态
     */
    public VoiceNote toggleArchive(Long id, User user) {
        VoiceNote voiceNote = voiceNoteMapper.findByIdAndUserId(id, user.getId());
        if (voiceNote != null) {
            voiceNote.setIsArchived(!voiceNote.getIsArchived());
            voiceNote.setUpdatedAt(LocalDateTime.now());
            voiceNoteMapper.updateById(voiceNote);
        }
        return voiceNote;
    }

    /**
     * 更新转写内容
     */
    public void updateTranscription(Long id, String transcription, Double confidence, String language) {
        VoiceNote voiceNote = voiceNoteMapper.selectById(id);
        if (voiceNote != null) {
            voiceNote.setTranscription(transcription);
            voiceNote.setTranscriptionConfidence(confidence);
            voiceNote.setLanguageDetected(language);
            voiceNote.setUpdatedAt(LocalDateTime.now());
            voiceNoteMapper.updateById(voiceNote);
        }
    }

    /**
     * 更新音频文件信息
     */
    public void updateAudioInfo(Long id, String filePath, String fileName, Long fileSize, 
                               Integer duration, String format) {
        VoiceNote voiceNote = voiceNoteMapper.selectById(id);
        if (voiceNote != null) {
            voiceNote.setAudioFilePath(filePath);
            voiceNote.setAudioFileName(fileName);
            voiceNote.setAudioFileSize(fileSize);
            voiceNote.setAudioDurationSeconds(duration);
            voiceNote.setAudioFormat(format);
            voiceNote.setUpdatedAt(LocalDateTime.now());
            voiceNoteMapper.updateById(voiceNote);
        }
    }

    /**
     * 查找需要转写的语音笔记
     */
    public List<VoiceNote> findVoiceNotesNeedingTranscription(User user) {
        return voiceNoteMapper.findVoiceNotesNeedingTranscription(user.getId());
    }

    /**
     * 查找低置信度转写的语音笔记
     */
    public List<VoiceNote> findLowConfidenceTranscriptions(User user, Double threshold) {
        return voiceNoteMapper.findLowConfidenceTranscriptions(user.getId(), threshold);
    }

    /**
     * 根据语言查找语音笔记
     */
    public List<VoiceNote> findByLanguage(User user, String language) {
        return voiceNoteMapper.findByUserIdAndLanguageDetectedOrderByCreatedAtDesc(user.getId(), language);
    }

    /**
     * 根据音频格式查找语音笔记
     */
    public List<VoiceNote> findByAudioFormat(User user, String audioFormat) {
        return voiceNoteMapper.findByUserIdAndAudioFormatOrderByCreatedAtDesc(user.getId(), audioFormat);
    }

    /**
     * 统计用户语音笔记总数
     */
    public long countByUser(User user) {
        return voiceNoteMapper.countByUserId(user.getId());
    }

    /**
     * 统计用户指定分类的语音笔记数
     */
    public long countByUserAndCategory(User user, VoiceNote.Category category) {
        return voiceNoteMapper.countByUserIdAndCategory(user.getId(), category.name());
    }

    /**
     * 统计用户收藏的语音笔记数
     */
    public long countFavoriteByUser(User user) {
        return voiceNoteMapper.countByUserIdAndIsFavoriteTrue(user.getId());
    }

    /**
     * 统计用户已归档的语音笔记数
     */
    public long countArchivedByUser(User user) {
        return voiceNoteMapper.countByUserIdAndIsArchivedTrue(user.getId());
    }

    // ========== Controller需要的方法 ==========

    /**
     * 创建语音笔记 - Controller版本
     */
    public VoiceNote createVoiceNote(String title, String description, MultipartFile audioFile,
                                   VoiceNote.Category category, VoiceNote.Priority priority,
                                   Set<String> tags, User user) {
        VoiceNote voiceNote = new VoiceNote();
        voiceNote.setUserId(user.getId());
        voiceNote.setUser(user);
        voiceNote.setTitle(title);
        voiceNote.setDescription(description);
        voiceNote.setCategory(category != null ? category : VoiceNote.Category.GENERAL);
        voiceNote.setPriority(priority != null ? priority : VoiceNote.Priority.MEDIUM);
        voiceNote.setTags(tags != null ? tags : new HashSet<>());
        voiceNote.setIsFavorite(false);
        voiceNote.setIsArchived(false);

        // 处理音频文件
        if (audioFile != null && !audioFile.isEmpty()) {
            voiceNote.setAudioFileName(audioFile.getOriginalFilename());
            voiceNote.setAudioFileSize(audioFile.getSize());
            voiceNote.setAudioFormat(getFileExtension(audioFile.getOriginalFilename()));
            // 这里应该保存文件到存储系统，暂时设置路径
            voiceNote.setAudioFilePath("/uploads/voice_notes/" + System.currentTimeMillis() + "_" + audioFile.getOriginalFilename());
        }

        voiceNote.setCreatedAt(LocalDateTime.now());
        voiceNote.setUpdatedAt(LocalDateTime.now());

        voiceNoteMapper.insert(voiceNote);
        return voiceNote;
    }

    /**
     * 从音频创建语音笔记
     */
    public VoiceNote createVoiceNoteFromAudio(String title, byte[] audioData, String fileName,
                                            VoiceNote.Category category, User user) {
        VoiceNote voiceNote = new VoiceNote();
        voiceNote.setUserId(user.getId());
        voiceNote.setUser(user);
        voiceNote.setTitle(title);
        voiceNote.setCategory(category != null ? category : VoiceNote.Category.GENERAL);
        voiceNote.setPriority(VoiceNote.Priority.MEDIUM);
        voiceNote.setTags(new HashSet<>());
        voiceNote.setIsFavorite(false);
        voiceNote.setIsArchived(false);

        // 处理音频数据
        voiceNote.setAudioFileName(fileName);
        voiceNote.setAudioFileSize((long) audioData.length);
        voiceNote.setAudioFormat(getFileExtension(fileName));
        voiceNote.setAudioFilePath("/uploads/voice_notes/" + System.currentTimeMillis() + "_" + fileName);

        voiceNote.setCreatedAt(LocalDateTime.now());
        voiceNote.setUpdatedAt(LocalDateTime.now());

        voiceNoteMapper.insert(voiceNote);
        return voiceNote;
    }

    /**
     * 获取用户语音笔记列表（分页）
     */
    public IPage<VoiceNote> getUserVoiceNotes(User user, Pageable pageable) {
        Page<VoiceNote> page = new Page<>(pageable.getPageNumber() + 1, pageable.getPageSize());
        return voiceNoteMapper.findByUserId(user.getId(), page);
    }

    /**
     * 获取语音笔记详情
     */
    public VoiceNote getVoiceNote(Long noteId, User user) {
        VoiceNote voiceNote = voiceNoteMapper.selectById(noteId);
        if (voiceNote == null || !voiceNote.getUserId().equals(user.getId())) {
            throw new RuntimeException("语音笔记不存在或无权限访问");
        }
        return voiceNote;
    }

    /**
     * 删除语音笔记
     */
    public void deleteVoiceNote(Long noteId, User user) {
        VoiceNote voiceNote = getVoiceNote(noteId, user);
        voiceNoteMapper.deleteById(noteId);
        // 这里应该同时删除音频文件
    }

    /**
     * 语音笔记统计信息内部类
     */
    public static class VoiceNoteStats {
        private long totalNotes;
        private long favoriteNotes;
        private long archivedNotes;
        private long totalDuration;

        public VoiceNoteStats(long total, long favorite, long archived, long duration) {
            this.totalNotes = total;
            this.favoriteNotes = favorite;
            this.archivedNotes = archived;
            this.totalDuration = duration;
        }

        // Getters
        public long getTotalNotes() { return totalNotes; }
        public long getFavoriteNotes() { return favoriteNotes; }
        public long getArchivedNotes() { return archivedNotes; }
        public long getTotalDuration() { return totalDuration; }
    }

    /**
     * 获取用户语音笔记统计
     */
    public VoiceNoteStats getUserVoiceNoteStats(User user) {
        long total = countByUser(user);
        long favorite = countFavoriteByUser(user);
        long archived = countArchivedByUser(user);
        long duration = voiceNoteMapper.getTotalDurationByUserId(user.getId());

        return new VoiceNoteStats(total, favorite, archived, duration);
    }

    /**
     * 获取用户标签
     */
    public Set<String> getUserTags(User user) {
        return voiceNoteMapper.findAllTagsByUserId(user.getId());
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        if (fileName == null || fileName.lastIndexOf('.') == -1) {
            return "";
        }
        return fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase();
    }
}
