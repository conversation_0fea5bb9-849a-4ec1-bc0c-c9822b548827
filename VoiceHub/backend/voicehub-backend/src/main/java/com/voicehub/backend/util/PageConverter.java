package com.voicehub.backend.util;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * 页面转换工具类
 * 用于MyBatis-Plus和Spring Data之间的类型转换
 */
public class PageConverter {

    /**
     * 将MyBatis-Plus的IPage转换为Spring Data的Page
     */
    public static <T> Page<T> convertToSpringPage(IPage<T> iPage) {
        if (iPage == null) {
            return Page.empty();
        }
        
        Pageable pageable = PageRequest.of(
            (int) iPage.getCurrent() - 1, // MyBatis-Plus从1开始，Spring Data从0开始
            (int) iPage.getSize()
        );
        
        return new PageImpl<>(
            iPage.getRecords(),
            pageable,
            iPage.getTotal()
        );
    }

    /**
     * 将实体对象包装为Optional
     */
    public static <T> Optional<T> toOptional(T entity) {
        return Optional.ofNullable(entity);
    }

    /**
     * 将Set转换为List
     */
    public static <T> List<T> setToList(Set<T> set) {
        if (set == null) {
            return new ArrayList<>();
        }
        return new ArrayList<>(set);
    }

    /**
     * 创建MyBatis-Plus的Page对象
     */
    public static <T> com.baomidou.mybatisplus.extension.plugins.pagination.Page<T> createMybatisPlusPage(
            int current, int size) {
        return new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(current, size);
    }

    /**
     * 从Spring Data的Pageable创建MyBatis-Plus的Page
     */
    public static <T> com.baomidou.mybatisplus.extension.plugins.pagination.Page<T> fromPageable(
            Pageable pageable) {
        return new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(
            pageable.getPageNumber() + 1, // Spring Data从0开始，MyBatis-Plus从1开始
            pageable.getPageSize()
        );
    }
}
