# Docker environment configuration
spring:
  profiles:
    active: docker
  
  datasource:
    url: ****************************************
    username: voicehub
    password: voicehub123
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
  
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        use_sql_comments: false
  
  data:
    redis:
      host: redis
      port: 6379
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
  
  flyway:
    enabled: true
    locations: classpath:db/migration
    baseline-on-migrate: true
    validate-on-migrate: true

# Server configuration
server:
  port: 8080
  servlet:
    context-path: /api

# Logging configuration
logging:
  level:
    com.voicehub: INFO
    org.springframework.security: WARN
    org.hibernate.SQL: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: /app/logs/voicehub.log

# Application specific configuration
voicehub:
  jwt:
    secret: ${JWT_SECRET:VoiceHubSecretKeyForJWTTokenGenerationInDockerEnvironment2024}
    expiration: 86400000 # 24 hours
  
  openai:
    api-key: ${OPENAI_API_KEY:your-openai-api-key}
    model: gpt-3.5-turbo
    max-tokens: 1000
  
  speech:
    provider: openai
    language: en-US
  
  file:
    upload-dir: /app/uploads
    max-size: 50MB
  
  cors:
    allowed-origins: 
      - http://localhost:3000
      - http://frontend:80
      - https://voicehub.example.com

# Management endpoints
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true