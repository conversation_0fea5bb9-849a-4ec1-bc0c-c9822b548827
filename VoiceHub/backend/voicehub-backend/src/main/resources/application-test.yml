spring:
  # 使用内存数据库进行测试
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
    username: sa
    password:

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false
    database-platform: org.hibernate.dialect.H2Dialect

# MyBatis-Plus测试配置
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.voicehub.backend.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: AUTO
      field-strategy: NOT_EMPTY
      table-underline: true
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
    banner: false

  # 禁用Flyway数据库迁移
  flyway:
    enabled: false

  # 禁用Redis
  data:
    redis:
      repositories:
        enabled: false

# 禁用不必要的自动配置
management:
  endpoints:
    enabled-by-default: false
  endpoint:
    health:
      enabled: true
  metrics:
    export:
      simple:
        enabled: false

# 服务器配置
server:
  port: 8080
  servlet:
    context-path: /api

# 日志配置
logging:
  level:
    com.voicehub: INFO
    org.springframework.security: WARN
    org.hibernate.SQL: WARN
