# VoiceHub智能语音助手后端配置
server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: voicehub-backend
  data:
    redis:
      host: localhost
      port: 6379
      password: localredis
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0

  # 允许循环引用（临时解决方案）
  main:
    allow-circular-references: true
  
  # PostgreSQL数据库配置
  datasource:
    url: ****************************************************************
    username: postgres
    password: localpostgres
    driver-class-name: org.postgresql.Driver
    hikari:
      connection-timeout: 20000
      maximum-pool-size: 10
      minimum-idle: 5

  # JPA配置 - 已移除，使用MyBatis-Plus
  # jpa:
  #   hibernate:
  #     ddl-auto: update  # 自动更新表结构
  #   show-sql: true
  #   properties:
  #     hibernate:
  #       dialect: org.hibernate.dialect.PostgreSQLDialect
  #       format_sql: true
  #       jdbc:
  #         lob:
  #           non_contextual_creation: true

# MyBatis-Plus配置
mybatis-plus:
  # 配置扫描路径
  mapper-locations: classpath*:/mapper/**/*.xml
  # 实体扫描，多个package用逗号或者分号分隔
  type-aliases-package: com.voicehub.backend.entity
  # 配置
  configuration:
    # 是否开启自动驼峰命名规则映射
    map-underscore-to-camel-case: true
    # 开启Mybatis二级缓存，默认为 true
    cache-enabled: false
    # 日志实现
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  # 全局配置
  global-config:
    # 数据库相关配置
    db-config:
      # 主键类型（AUTO:"数据库ID自增"，INPUT:"用户输入ID"，ID_WORKER:"全局唯一ID (数字类型唯一ID)"，UUID:"全局唯一ID UUID"）
      id-type: AUTO
      # 字段策略（IGNORED:"忽略判断"，NOT_NULL:"非NULL判断"，NOT_EMPTY:"非空判断"）
      field-strategy: NOT_EMPTY
      # 数据库大写下划线转换
      table-underline: true
      # 逻辑删除配置
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
    # 是否控制台 print mybatis-plus 的 LOGO
    banner: false

  # Flyway数据库迁移 - 完全禁用
  flyway:
    enabled: false



  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB

# JWT配置
jwt:
  secret: VoiceHubSecretKey2024ForJWTTokenGeneration
  expiration: 86400000 # 24小时

# AI服务配置
ai:
  openai:
    api-key: ${OPENAI_API_KEY:your-openai-api-key}
    base-url: https://api.openai.com/v1
  speech:
    baidu:
      app-id: ${BAIDU_APP_ID:your-baidu-app-id}
      api-key: ${BAIDU_API_KEY:your-baidu-api-key}
      secret-key: ${BAIDU_SECRET_KEY:your-baidu-secret-key}

# OpenAI配置 (兼容OpenAIService)
openai:
  api:
    key: ${OPENAI_API_KEY:sk-m75m8py9Oe7SRcxH8KbGmiqVxCSo6fXuPRdnytSjdZpvw3Em}
    url: https://tbai.xin
  model: gpt-4.1-nano
  max-tokens: 1000
  temperature: 0.7

# 百度TTS配置 (兼容TextToSpeechService)
baidu:
  tts:
    api-key: ${BAIDU_API_KEY:your-baidu-api-key}
    secret-key: ${BAIDU_SECRET_KEY:your-baidu-secret-key}
    token-url: https://aip.baidubce.com/oauth/2.0/token
    synthesis-url: https://tsn.baidu.com/text2audio

# 应用配置
app:
  upload:
    dir: uploads

# 日志配置
logging:
  level:
    com.voicehub: DEBUG
    org.springframework.security: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# Actuator配置
management:
  health:
    # 禁用Redis健康检查
    redis:
      enabled: false
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
      probes:
        enabled: true
  metrics:
    export:
      prometheus:
        enabled: true

# Swagger配置
springdoc:
  api-docs:
    path: /api-docs
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha