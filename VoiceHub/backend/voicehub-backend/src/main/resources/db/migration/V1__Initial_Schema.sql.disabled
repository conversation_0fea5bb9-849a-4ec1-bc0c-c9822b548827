-- VoiceHub Database Schema
-- Initial migration script for PostgreSQL

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name <PERSON><PERSON><PERSON><PERSON>(100),
    avatar_url VARCHAR(500),
    phone_number VARCHAR(20),
    preferred_language VARCHAR(10) DEFAULT 'en',
    timezone VARCHAR(50) DEFAULT 'UTC',
    voice_settings JSONB DEFAULT '{}',
    notification_settings JSONB DEFAULT '{}',
    subscription_type VARCHAR(20) DEFAULT 'FREE',
    subscription_expires_at TIMESTAMP,
    is_active BOOLEAN DEFAULT true,
    is_verified BOOLEAN DEFAULT false,
    last_login_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- User profiles table for extended information
CREATE TABLE user_profiles (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    bio TEXT,
    occupation VARCHAR(100),
    company VARCHAR(100),
    location VARCHAR(100),
    birth_date DATE,
    gender VARCHAR(10),
    interests TEXT[],
    goals TEXT[],
    privacy_settings JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Conversations table
CREATE TABLE conversations (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(200) NOT NULL,
    type VARCHAR(50) DEFAULT 'GENERAL',
    status VARCHAR(20) DEFAULT 'ACTIVE',
    mood_score DECIMAL(3,2),
    emotion_analysis JSONB DEFAULT '{}',
    context_data JSONB DEFAULT '{}',
    message_count INTEGER DEFAULT 0,
    total_tokens INTEGER DEFAULT 0,
    last_activity_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_favorite BOOLEAN DEFAULT false,
    is_archived BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Chat messages table
CREATE TABLE chat_messages (
    id BIGSERIAL PRIMARY KEY,
    conversation_id BIGINT NOT NULL REFERENCES conversations(id) ON DELETE CASCADE,
    role VARCHAR(20) NOT NULL, -- 'USER', 'ASSISTANT', 'SYSTEM'
    content TEXT NOT NULL,
    message_type VARCHAR(20) DEFAULT 'TEXT', -- 'TEXT', 'VOICE', 'SYSTEM', 'ERROR'
    voice_file_path VARCHAR(500),
    transcription_confidence DECIMAL(5,4),
    emotion VARCHAR(50),
    emotion_confidence DECIMAL(5,4),
    response_time_ms INTEGER,
    token_count INTEGER,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Voice notes table
CREATE TABLE voice_notes (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(200) NOT NULL,
    transcription TEXT,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT,
    duration_seconds INTEGER,
    audio_format VARCHAR(20),
    sample_rate INTEGER,
    bit_rate INTEGER,
    category VARCHAR(50) DEFAULT 'GENERAL',
    tags TEXT[],
    transcription_confidence DECIMAL(5,4),
    language VARCHAR(10) DEFAULT 'en',
    is_favorite BOOLEAN DEFAULT false,
    is_archived BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Schedules table
CREATE TABLE schedules (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP,
    timezone VARCHAR(50),
    location VARCHAR(200),
    attendees TEXT[],
    priority VARCHAR(20) DEFAULT 'MEDIUM',
    status VARCHAR(20) DEFAULT 'SCHEDULED',
    reminder_settings JSONB DEFAULT '{}',
    recurrence_rule VARCHAR(200),
    voice_created BOOLEAN DEFAULT false,
    voice_command TEXT,
    nlp_confidence DECIMAL(5,4),
    calendar_sync_id VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Voice analytics table
CREATE TABLE voice_analytics (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    session_id UUID DEFAULT uuid_generate_v4(),
    recording_type VARCHAR(50), -- 'NOTE', 'CONVERSATION', 'SCHEDULE'
    recording_id BIGINT,
    duration_seconds INTEGER NOT NULL,
    audio_quality_score DECIMAL(4,2),
    speech_rate_wpm INTEGER,
    pause_frequency DECIMAL(5,2),
    volume_consistency DECIMAL(5,2),
    clarity_score DECIMAL(4,2),
    pitch_analysis JSONB DEFAULT '{}',
    frequency_analysis JSONB DEFAULT '{}',
    noise_level DECIMAL(5,2),
    voice_activity_ratio DECIMAL(5,4),
    emotion_detected VARCHAR(50),
    emotion_confidence DECIMAL(5,4),
    language_detected VARCHAR(10),
    language_confidence DECIMAL(5,4),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Mood tracking table
CREATE TABLE mood_entries (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    mood VARCHAR(50) NOT NULL, -- 'HAPPY', 'SAD', 'ANGRY', 'SURPRISED', 'NEUTRAL', 'EXCITED'
    intensity INTEGER NOT NULL CHECK (intensity >= 1 AND intensity <= 10),
    note TEXT,
    triggers TEXT[],
    context VARCHAR(100), -- 'VOICE_SESSION', 'MANUAL', 'CONVERSATION'
    related_session_id UUID,
    location VARCHAR(100),
    weather VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- User sessions table for tracking usage
CREATE TABLE user_sessions (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    session_id UUID DEFAULT uuid_generate_v4(),
    session_type VARCHAR(50), -- 'VOICE_RECORDING', 'CONVERSATION', 'SCHEDULE_MANAGEMENT'
    start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    end_time TIMESTAMP,
    duration_seconds INTEGER,
    activities_count INTEGER DEFAULT 0,
    device_info JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Audio files table for centralized file management
CREATE TABLE audio_files (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    file_name VARCHAR(255) NOT NULL,
    original_name VARCHAR(255),
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(100),
    duration_seconds INTEGER,
    audio_format VARCHAR(20),
    sample_rate INTEGER,
    bit_rate INTEGER,
    channels INTEGER DEFAULT 1,
    file_hash VARCHAR(64), -- SHA-256 hash for deduplication
    upload_source VARCHAR(50), -- 'VOICE_NOTE', 'CONVERSATION', 'UPLOAD'
    processing_status VARCHAR(20) DEFAULT 'PENDING', -- 'PENDING', 'PROCESSING', 'COMPLETED', 'FAILED'
    transcription_status VARCHAR(20) DEFAULT 'PENDING',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Notifications table
CREATE TABLE notifications (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    type VARCHAR(50) NOT NULL, -- 'REMINDER', 'SYSTEM', 'ACHIEVEMENT', 'MOOD_CHECK'
    title VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    data JSONB DEFAULT '{}',
    is_read BOOLEAN DEFAULT false,
    priority VARCHAR(20) DEFAULT 'NORMAL', -- 'LOW', 'NORMAL', 'HIGH', 'URGENT'
    scheduled_for TIMESTAMP,
    expires_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- User achievements table
CREATE TABLE user_achievements (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    achievement_type VARCHAR(50) NOT NULL, -- 'FIRST_RECORDING', 'STREAK_7_DAYS', 'QUALITY_IMPROVEMENT'
    achievement_name VARCHAR(100) NOT NULL,
    description TEXT,
    points INTEGER DEFAULT 0,
    badge_icon VARCHAR(100),
    unlocked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data JSONB DEFAULT '{}'
);

-- System settings table
CREATE TABLE system_settings (
    id BIGSERIAL PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type VARCHAR(20) DEFAULT 'STRING', -- 'STRING', 'INTEGER', 'BOOLEAN', 'JSON'
    description TEXT,
    is_public BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_active ON users(is_active);
CREATE INDEX idx_users_created_at ON users(created_at);

CREATE INDEX idx_conversations_user_id ON conversations(user_id);
CREATE INDEX idx_conversations_type ON conversations(type);
CREATE INDEX idx_conversations_status ON conversations(status);
CREATE INDEX idx_conversations_created_at ON conversations(created_at);
CREATE INDEX idx_conversations_last_activity ON conversations(last_activity_at);

CREATE INDEX idx_chat_messages_conversation_id ON chat_messages(conversation_id);
CREATE INDEX idx_chat_messages_role ON chat_messages(role);
CREATE INDEX idx_chat_messages_created_at ON chat_messages(created_at);

CREATE INDEX idx_voice_notes_user_id ON voice_notes(user_id);
CREATE INDEX idx_voice_notes_category ON voice_notes(category);
CREATE INDEX idx_voice_notes_created_at ON voice_notes(created_at);
CREATE INDEX idx_voice_notes_tags ON voice_notes USING GIN(tags);

CREATE INDEX idx_schedules_user_id ON schedules(user_id);
CREATE INDEX idx_schedules_start_time ON schedules(start_time);
CREATE INDEX idx_schedules_status ON schedules(status);
CREATE INDEX idx_schedules_created_at ON schedules(created_at);

CREATE INDEX idx_voice_analytics_user_id ON voice_analytics(user_id);
CREATE INDEX idx_voice_analytics_session_id ON voice_analytics(session_id);
CREATE INDEX idx_voice_analytics_created_at ON voice_analytics(created_at);

CREATE INDEX idx_mood_entries_user_id ON mood_entries(user_id);
CREATE INDEX idx_mood_entries_mood ON mood_entries(mood);
CREATE INDEX idx_mood_entries_created_at ON mood_entries(created_at);

CREATE INDEX idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX idx_user_sessions_session_id ON user_sessions(session_id);
CREATE INDEX idx_user_sessions_created_at ON user_sessions(created_at);

CREATE INDEX idx_audio_files_user_id ON audio_files(user_id);
CREATE INDEX idx_audio_files_file_hash ON audio_files(file_hash);
CREATE INDEX idx_audio_files_processing_status ON audio_files(processing_status);

CREATE INDEX idx_notifications_user_id ON notifications(user_id);
CREATE INDEX idx_notifications_is_read ON notifications(is_read);
CREATE INDEX idx_notifications_created_at ON notifications(created_at);

-- Create triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON user_profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_conversations_updated_at BEFORE UPDATE ON conversations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_voice_notes_updated_at BEFORE UPDATE ON voice_notes
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_schedules_updated_at BEFORE UPDATE ON schedules
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_audio_files_updated_at BEFORE UPDATE ON audio_files
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_system_settings_updated_at BEFORE UPDATE ON system_settings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default system settings
INSERT INTO system_settings (setting_key, setting_value, setting_type, description, is_public) VALUES
('app_version', '1.0.0', 'STRING', 'Current application version', true),
('maintenance_mode', 'false', 'BOOLEAN', 'Enable maintenance mode', false),
('max_file_size_mb', '50', 'INTEGER', 'Maximum file upload size in MB', true),
('supported_audio_formats', '["mp3", "wav", "m4a", "ogg"]', 'JSON', 'Supported audio file formats', true),
('default_language', 'en', 'STRING', 'Default system language', true),
('voice_quality_threshold', '0.8', 'STRING', 'Minimum voice quality threshold', false),
('max_conversation_history', '1000', 'INTEGER', 'Maximum conversation history per user', false),
('session_timeout_minutes', '30', 'INTEGER', 'User session timeout in minutes', false);

-- Create views for common queries
CREATE VIEW user_stats AS
SELECT 
    u.id,
    u.username,
    u.email,
    u.created_at as user_since,
    COUNT(DISTINCT vn.id) as total_voice_notes,
    COUNT(DISTINCT c.id) as total_conversations,
    COUNT(DISTINCT s.id) as total_schedules,
    AVG(va.audio_quality_score) as avg_voice_quality,
    COUNT(DISTINCT us.id) as total_sessions,
    SUM(us.duration_seconds) as total_session_time
FROM users u
LEFT JOIN voice_notes vn ON u.id = vn.user_id
LEFT JOIN conversations c ON u.id = c.user_id
LEFT JOIN schedules s ON u.id = s.user_id
LEFT JOIN voice_analytics va ON u.id = va.user_id
LEFT JOIN user_sessions us ON u.id = us.user_id
WHERE u.is_active = true
GROUP BY u.id, u.username, u.email, u.created_at;

CREATE VIEW recent_activity AS
SELECT 
    'voice_note' as activity_type,
    vn.id as activity_id,
    vn.user_id,
    vn.title as activity_title,
    vn.created_at as activity_time
FROM voice_notes vn
WHERE vn.created_at >= CURRENT_DATE - INTERVAL '7 days'

UNION ALL

SELECT 
    'conversation' as activity_type,
    c.id as activity_id,
    c.user_id,
    c.title as activity_title,
    c.last_activity_at as activity_time
FROM conversations c
WHERE c.last_activity_at >= CURRENT_DATE - INTERVAL '7 days'

UNION ALL

SELECT 
    'schedule' as activity_type,
    s.id as activity_id,
    s.user_id,
    s.title as activity_title,
    s.created_at as activity_time
FROM schedules s
WHERE s.created_at >= CURRENT_DATE - INTERVAL '7 days'

ORDER BY activity_time DESC;

-- Create materialized view for analytics dashboard
CREATE MATERIALIZED VIEW daily_analytics AS
SELECT 
    DATE(created_at) as date,
    COUNT(*) as total_activities,
    COUNT(DISTINCT user_id) as active_users,
    'voice_note' as activity_type
FROM voice_notes
WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY DATE(created_at)

UNION ALL

SELECT 
    DATE(last_activity_at) as date,
    COUNT(*) as total_activities,
    COUNT(DISTINCT user_id) as active_users,
    'conversation' as activity_type
FROM conversations
WHERE last_activity_at >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY DATE(last_activity_at)

UNION ALL

SELECT 
    DATE(created_at) as date,
    COUNT(*) as total_activities,
    COUNT(DISTINCT user_id) as active_users,
    'schedule' as activity_type
FROM schedules
WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY DATE(created_at)

ORDER BY date DESC;

-- Create index on materialized view
CREATE INDEX idx_daily_analytics_date ON daily_analytics(date);
CREATE INDEX idx_daily_analytics_type ON daily_analytics(activity_type);

-- Refresh materialized view function
CREATE OR REPLACE FUNCTION refresh_daily_analytics()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW daily_analytics;
END;
$$ LANGUAGE plpgsql;

-- Comments for documentation
COMMENT ON TABLE users IS 'Main users table with authentication and profile information';
COMMENT ON TABLE user_profiles IS 'Extended user profile information';
COMMENT ON TABLE conversations IS 'AI conversation sessions with users';
COMMENT ON TABLE chat_messages IS 'Individual messages within conversations';
COMMENT ON TABLE voice_notes IS 'User voice recordings and transcriptions';
COMMENT ON TABLE schedules IS 'User calendar events and appointments';
COMMENT ON TABLE voice_analytics IS 'Voice quality and performance analytics';
COMMENT ON TABLE mood_entries IS 'User mood tracking and emotional state';
COMMENT ON TABLE user_sessions IS 'User activity sessions and usage tracking';
COMMENT ON TABLE audio_files IS 'Centralized audio file management';
COMMENT ON TABLE notifications IS 'System and user notifications';
COMMENT ON TABLE user_achievements IS 'User achievements and gamification';
COMMENT ON TABLE system_settings IS 'Application configuration settings';