package com.voicehub.backend;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 测试Java配置是否正确
 */
public class JavaConfigTest {

    @Test
    public void testJavaVersion() {
        String javaVersion = System.getProperty("java.version");
        System.out.println("Java Version: " + javaVersion);
        assertTrue(javaVersion.startsWith("17"), "应该使用Java 17");
    }

    @Test
    public void testTextBlocks() {
        // Java 17特性测试 - Text Blocks (Java 15+)
        String textBlock = """
                这是一个
                多行文本块
                测试Java 17功能
                """;
        assertNotNull(textBlock);
        assertTrue(textBlock.contains("多行文本块"));
    }

    @Test
    public void testRecords() {
        // Java 17特性测试 - Records (Java 14+)
        record TestRecord(String name, int value) {}
        
        TestRecord record = new TestRecord("test", 42);
        assertEquals("test", record.name());
        assertEquals(42, record.value());
    }
}
