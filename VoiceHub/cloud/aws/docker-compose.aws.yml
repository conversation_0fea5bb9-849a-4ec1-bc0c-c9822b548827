version: '3.8'

services:
  # Application Load Balancer will handle SSL termination
  # So we don't need nginx for SSL in AWS ECS
  
  # VoiceHub Backend Service
  backend:
    image: ${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/voicehub-backend:${IMAGE_TAG}
    environment:
      - SPRING_PROFILES_ACTIVE=aws
      - SPRING_DATASOURCE_URL=jdbc:postgresql://${RDS_ENDPOINT}:5432/voicehub
      - SPRING_DATASOURCE_USERNAME=${RDS_USERNAME}
      - SPRING_DATASOURCE_PASSWORD=${RDS_PASSWORD}
      - SPRING_DATA_REDIS_HOST=${ELASTICACHE_ENDPOINT}
      - SPRING_DATA_REDIS_PASSWORD=${REDIS_PASSWORD}
      - JWT_SECRET=${JWT_SECRET}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_S3_BUCKET=${S3_BUCKET_NAME}
      - AWS_REGION=${AWS_REGION}
    ports:
      - "8080:8080"
    logging:
      driver: awslogs
      options:
        awslogs-group: /ecs/voicehub-backend
        awslogs-region: ${AWS_REGION}
        awslogs-stream-prefix: ecs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # VoiceHub Frontend Service
  frontend:
    image: ${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/voicehub-frontend:${IMAGE_TAG}
    environment:
      - NODE_ENV=production
      - REACT_APP_API_BASE_URL=https://${DOMAIN_NAME}/api
    ports:
      - "80:80"
    logging:
      driver: awslogs
      options:
        awslogs-group: /ecs/voicehub-frontend
        awslogs-region: ${AWS_REGION}
        awslogs-stream-prefix: ecs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s