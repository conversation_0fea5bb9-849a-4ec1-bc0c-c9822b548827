{"family": "voicehub-task", "networkMode": "awsvpc", "requiresCompatibilities": ["FARGATE"], "cpu": "1024", "memory": "2048", "executionRoleArn": "arn:aws:iam::${AWS_ACCOUNT_ID}:role/ecsTaskExecutionRole", "taskRoleArn": "arn:aws:iam::${AWS_ACCOUNT_ID}:role/voicehub-task-role", "containerDefinitions": [{"name": "voicehub-backend", "image": "${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/voicehub-backend:${IMAGE_TAG}", "cpu": 512, "memory": 1024, "essential": true, "portMappings": [{"containerPort": 8080, "protocol": "tcp"}], "environment": [{"name": "SPRING_PROFILES_ACTIVE", "value": "aws"}, {"name": "AWS_REGION", "value": "${AWS_REGION}"}], "secrets": [{"name": "SPRING_DATASOURCE_URL", "valueFrom": "arn:aws:secretsmanager:${AWS_REGION}:${AWS_ACCOUNT_ID}:secret:voicehub/database:url::"}, {"name": "SPRING_DATASOURCE_PASSWORD", "valueFrom": "arn:aws:secretsmanager:${AWS_REGION}:${AWS_ACCOUNT_ID}:secret:voicehub/database:password::"}, {"name": "JWT_SECRET", "valueFrom": "arn:aws:secretsmanager:${AWS_REGION}:${AWS_ACCOUNT_ID}:secret:voicehub/jwt:secret::"}, {"name": "OPENAI_API_KEY", "valueFrom": "arn:aws:secretsmanager:${AWS_REGION}:${AWS_ACCOUNT_ID}:secret:voicehub/openai:api-key::"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/voicehub-backend", "awslogs-region": "${AWS_REGION}", "awslogs-stream-prefix": "ecs"}}, "healthCheck": {"command": ["CMD-SHELL", "curl -f http://localhost:8080/api/health || exit 1"], "interval": 30, "timeout": 5, "retries": 3, "startPeriod": 60}}, {"name": "voicehub-frontend", "image": "${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/voicehub-frontend:${IMAGE_TAG}", "cpu": 512, "memory": 1024, "essential": true, "portMappings": [{"containerPort": 80, "protocol": "tcp"}], "environment": [{"name": "NODE_ENV", "value": "production"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/voicehub-frontend", "awslogs-region": "${AWS_REGION}", "awslogs-stream-prefix": "ecs"}}, "healthCheck": {"command": ["CMD-SHELL", "curl -f http://localhost:80 || exit 1"], "interval": 30, "timeout": 5, "retries": 3, "startPeriod": 30}}]}