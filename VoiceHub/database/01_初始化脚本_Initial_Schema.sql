-- =====================================================
-- VoiceHub 智能语音助手平台 - 数据库初始化脚本
-- PostgreSQL 14+ 版本
-- 创建时间: 2024年
-- 描述: 创建所有必需的表、索引、触发器和视图
-- =====================================================

-- 启用必要的扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- =====================================================
-- 核心用户表
-- =====================================================

-- 用户主表
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100),
    avatar_url VARCHAR(500),
    phone_number VARCHAR(20),
    preferred_language VARCHAR(10) DEFAULT 'zh-CN',
    timezone VARCHAR(50) DEFAULT 'Asia/Shanghai',
    voice_settings JSONB DEFAULT '{}',
    notification_settings JSONB DEFAULT '{}',
    subscription_type VARCHAR(20) DEFAULT 'FREE',
    subscription_expires_at TIMESTAMP,
    is_active BOOLEAN DEFAULT true,
    is_verified BOOLEAN DEFAULT false,
    last_login_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 用户详细资料表
CREATE TABLE user_profiles (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    bio TEXT,
    occupation VARCHAR(100),
    company VARCHAR(100),
    location VARCHAR(100),
    birth_date DATE,
    gender VARCHAR(10),
    interests TEXT[],
    goals TEXT[],
    privacy_settings JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 对话和消息表
-- =====================================================

-- 对话会话表
CREATE TABLE conversations (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(200) NOT NULL,
    type VARCHAR(50) DEFAULT 'GENERAL',
    status VARCHAR(20) DEFAULT 'ACTIVE',
    mood_score DECIMAL(3,2),
    emotion_analysis JSONB DEFAULT '{}',
    context_data JSONB DEFAULT '{}',
    message_count INTEGER DEFAULT 0,
    total_tokens INTEGER DEFAULT 0,
    last_activity_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_favorite BOOLEAN DEFAULT false,
    is_archived BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 聊天消息表
CREATE TABLE chat_messages (
    id BIGSERIAL PRIMARY KEY,
    conversation_id BIGINT NOT NULL REFERENCES conversations(id) ON DELETE CASCADE,
    role VARCHAR(20) NOT NULL, -- 'USER', 'ASSISTANT', 'SYSTEM'
    content TEXT NOT NULL,
    message_type VARCHAR(20) DEFAULT 'TEXT', -- 'TEXT', 'VOICE', 'SYSTEM', 'ERROR'
    voice_file_path VARCHAR(500),
    transcription_confidence DECIMAL(5,4),
    emotion VARCHAR(50),
    emotion_confidence DECIMAL(5,4),
    response_time_ms INTEGER,
    token_count INTEGER,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 语音功能表
-- =====================================================

-- 语音笔记表
CREATE TABLE voice_notes (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(200) NOT NULL,
    transcription TEXT,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT,
    duration_seconds INTEGER,
    audio_format VARCHAR(20),
    sample_rate INTEGER,
    bit_rate INTEGER,
    category VARCHAR(50) DEFAULT 'GENERAL',
    tags TEXT[],
    transcription_confidence DECIMAL(5,4),
    language VARCHAR(10) DEFAULT 'zh-CN',
    is_favorite BOOLEAN DEFAULT false,
    is_archived BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 音频文件管理表
CREATE TABLE audio_files (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    file_name VARCHAR(255) NOT NULL,
    original_name VARCHAR(255),
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(100),
    duration_seconds INTEGER,
    audio_format VARCHAR(20),
    sample_rate INTEGER,
    bit_rate INTEGER,
    channels INTEGER DEFAULT 1,
    file_hash VARCHAR(64), -- SHA-256 hash for deduplication
    upload_source VARCHAR(50), -- 'VOICE_NOTE', 'CONVERSATION', 'UPLOAD'
    processing_status VARCHAR(20) DEFAULT 'PENDING', -- 'PENDING', 'PROCESSING', 'COMPLETED', 'FAILED'
    transcription_status VARCHAR(20) DEFAULT 'PENDING',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 日程管理表
-- =====================================================

-- 日程安排表
CREATE TABLE schedules (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP,
    timezone VARCHAR(50),
    location VARCHAR(200),
    attendees TEXT[],
    priority VARCHAR(20) DEFAULT 'MEDIUM',
    status VARCHAR(20) DEFAULT 'SCHEDULED',
    reminder_settings JSONB DEFAULT '{}',
    recurrence_rule VARCHAR(200),
    voice_created BOOLEAN DEFAULT false,
    voice_command TEXT,
    nlp_confidence DECIMAL(5,4),
    calendar_sync_id VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 分析和统计表
-- =====================================================

-- 语音分析表
CREATE TABLE voice_analytics (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    session_id UUID DEFAULT uuid_generate_v4(),
    recording_type VARCHAR(50), -- 'NOTE', 'CONVERSATION', 'SCHEDULE'
    recording_id BIGINT,
    duration_seconds INTEGER NOT NULL,
    audio_quality_score DECIMAL(4,2),
    speech_rate_wpm INTEGER,
    pause_frequency DECIMAL(5,2),
    volume_consistency DECIMAL(5,2),
    clarity_score DECIMAL(4,2),
    pitch_analysis JSONB DEFAULT '{}',
    frequency_analysis JSONB DEFAULT '{}',
    noise_level DECIMAL(5,2),
    voice_activity_ratio DECIMAL(5,4),
    emotion_detected VARCHAR(50),
    emotion_confidence DECIMAL(5,4),
    language_detected VARCHAR(10),
    language_confidence DECIMAL(5,4),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 情绪追踪表
CREATE TABLE mood_entries (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    mood VARCHAR(50) NOT NULL, -- 'HAPPY', 'SAD', 'ANGRY', 'SURPRISED', 'NEUTRAL', 'EXCITED'
    intensity INTEGER NOT NULL CHECK (intensity >= 1 AND intensity <= 10),
    note TEXT,
    triggers TEXT[],
    context VARCHAR(100), -- 'VOICE_SESSION', 'MANUAL', 'CONVERSATION'
    related_session_id UUID,
    location VARCHAR(100),
    weather VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 用户会话表
CREATE TABLE user_sessions (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    session_id UUID DEFAULT uuid_generate_v4(),
    session_type VARCHAR(50), -- 'VOICE_RECORDING', 'CONVERSATION', 'SCHEDULE_MANAGEMENT'
    start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    end_time TIMESTAMP,
    duration_seconds INTEGER,
    activities_count INTEGER DEFAULT 0,
    device_info JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 系统功能表
-- =====================================================

-- 通知表
CREATE TABLE notifications (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    type VARCHAR(50) NOT NULL, -- 'REMINDER', 'SYSTEM', 'ACHIEVEMENT', 'MOOD_CHECK'
    title VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    data JSONB DEFAULT '{}',
    is_read BOOLEAN DEFAULT false,
    priority VARCHAR(20) DEFAULT 'NORMAL', -- 'LOW', 'NORMAL', 'HIGH', 'URGENT'
    scheduled_for TIMESTAMP,
    expires_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 用户成就表
CREATE TABLE user_achievements (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    achievement_type VARCHAR(50) NOT NULL, -- 'FIRST_RECORDING', 'STREAK_7_DAYS', 'QUALITY_IMPROVEMENT'
    achievement_name VARCHAR(100) NOT NULL,
    description TEXT,
    points INTEGER DEFAULT 0,
    badge_icon VARCHAR(100),
    unlocked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data JSONB DEFAULT '{}'
);

-- 系统设置表
CREATE TABLE system_settings (
    id BIGSERIAL PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type VARCHAR(20) DEFAULT 'STRING', -- 'STRING', 'INTEGER', 'BOOLEAN', 'JSON'
    description TEXT,
    is_public BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 索引创建
-- =====================================================

-- 用户表索引
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_active ON users(is_active);
CREATE INDEX idx_users_created_at ON users(created_at);
CREATE INDEX idx_users_last_login ON users(last_login_at);

-- 对话表索引
CREATE INDEX idx_conversations_user_id ON conversations(user_id);
CREATE INDEX idx_conversations_type ON conversations(type);
CREATE INDEX idx_conversations_status ON conversations(status);
CREATE INDEX idx_conversations_created_at ON conversations(created_at);
CREATE INDEX idx_conversations_last_activity ON conversations(last_activity_at);
CREATE INDEX idx_conversations_favorite ON conversations(is_favorite);

-- 消息表索引
CREATE INDEX idx_chat_messages_conversation_id ON chat_messages(conversation_id);
CREATE INDEX idx_chat_messages_role ON chat_messages(role);
CREATE INDEX idx_chat_messages_created_at ON chat_messages(created_at);
CREATE INDEX idx_chat_messages_type ON chat_messages(message_type);

-- 语音笔记索引
CREATE INDEX idx_voice_notes_user_id ON voice_notes(user_id);
CREATE INDEX idx_voice_notes_category ON voice_notes(category);
CREATE INDEX idx_voice_notes_created_at ON voice_notes(created_at);
CREATE INDEX idx_voice_notes_tags ON voice_notes USING GIN(tags);
CREATE INDEX idx_voice_notes_favorite ON voice_notes(is_favorite);
CREATE INDEX idx_voice_notes_transcription ON voice_notes USING GIN(to_tsvector('chinese', transcription));

-- 日程表索引
CREATE INDEX idx_schedules_user_id ON schedules(user_id);
CREATE INDEX idx_schedules_start_time ON schedules(start_time);
CREATE INDEX idx_schedules_status ON schedules(status);
CREATE INDEX idx_schedules_created_at ON schedules(created_at);
CREATE INDEX idx_schedules_priority ON schedules(priority);

-- 语音分析索引
CREATE INDEX idx_voice_analytics_user_id ON voice_analytics(user_id);
CREATE INDEX idx_voice_analytics_session_id ON voice_analytics(session_id);
CREATE INDEX idx_voice_analytics_created_at ON voice_analytics(created_at);
CREATE INDEX idx_voice_analytics_recording_type ON voice_analytics(recording_type);

-- 情绪追踪索引
CREATE INDEX idx_mood_entries_user_id ON mood_entries(user_id);
CREATE INDEX idx_mood_entries_mood ON mood_entries(mood);
CREATE INDEX idx_mood_entries_created_at ON mood_entries(created_at);

-- 用户会话索引
CREATE INDEX idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX idx_user_sessions_session_id ON user_sessions(session_id);
CREATE INDEX idx_user_sessions_created_at ON user_sessions(created_at);
CREATE INDEX idx_user_sessions_type ON user_sessions(session_type);

-- 音频文件索引
CREATE INDEX idx_audio_files_user_id ON audio_files(user_id);
CREATE INDEX idx_audio_files_file_hash ON audio_files(file_hash);
CREATE INDEX idx_audio_files_processing_status ON audio_files(processing_status);
CREATE INDEX idx_audio_files_created_at ON audio_files(created_at);

-- 通知索引
CREATE INDEX idx_notifications_user_id ON notifications(user_id);
CREATE INDEX idx_notifications_is_read ON notifications(is_read);
CREATE INDEX idx_notifications_created_at ON notifications(created_at);
CREATE INDEX idx_notifications_type ON notifications(type);

-- =====================================================
-- 触发器和函数
-- =====================================================

-- 更新时间戳触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为相关表创建更新时间戳触发器
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON user_profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_conversations_updated_at BEFORE UPDATE ON conversations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_voice_notes_updated_at BEFORE UPDATE ON voice_notes
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_schedules_updated_at BEFORE UPDATE ON schedules
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_audio_files_updated_at BEFORE UPDATE ON audio_files
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_system_settings_updated_at BEFORE UPDATE ON system_settings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 对话消息计数触发器
CREATE OR REPLACE FUNCTION update_conversation_message_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE conversations 
        SET message_count = message_count + 1,
            last_activity_at = CURRENT_TIMESTAMP
        WHERE id = NEW.conversation_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE conversations 
        SET message_count = message_count - 1
        WHERE id = OLD.conversation_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_conversation_message_count_trigger
    AFTER INSERT OR DELETE ON chat_messages
    FOR EACH ROW EXECUTE FUNCTION update_conversation_message_count();

-- =====================================================
-- 视图创建
-- =====================================================

-- 用户统计视图
CREATE VIEW user_stats AS
SELECT 
    u.id,
    u.username,
    u.email,
    u.full_name,
    u.created_at as user_since,
    u.last_login_at,
    COUNT(DISTINCT vn.id) as total_voice_notes,
    COUNT(DISTINCT c.id) as total_conversations,
    COUNT(DISTINCT s.id) as total_schedules,
    AVG(va.audio_quality_score) as avg_voice_quality,
    COUNT(DISTINCT us.id) as total_sessions,
    SUM(us.duration_seconds) as total_session_time,
    COUNT(DISTINCT ua.id) as total_achievements
FROM users u
LEFT JOIN voice_notes vn ON u.id = vn.user_id AND vn.is_archived = false
LEFT JOIN conversations c ON u.id = c.user_id AND c.is_archived = false
LEFT JOIN schedules s ON u.id = s.user_id
LEFT JOIN voice_analytics va ON u.id = va.user_id
LEFT JOIN user_sessions us ON u.id = us.user_id
LEFT JOIN user_achievements ua ON u.id = ua.user_id
WHERE u.is_active = true
GROUP BY u.id, u.username, u.email, u.full_name, u.created_at, u.last_login_at;

-- 最近活动视图
CREATE VIEW recent_activity AS
SELECT 
    'voice_note' as activity_type,
    vn.id as activity_id,
    vn.user_id,
    vn.title as activity_title,
    vn.created_at as activity_time,
    'created' as action
FROM voice_notes vn
WHERE vn.created_at >= CURRENT_DATE - INTERVAL '7 days'

UNION ALL

SELECT 
    'conversation' as activity_type,
    c.id as activity_id,
    c.user_id,
    c.title as activity_title,
    c.last_activity_at as activity_time,
    'updated' as action
FROM conversations c
WHERE c.last_activity_at >= CURRENT_DATE - INTERVAL '7 days'

UNION ALL

SELECT 
    'schedule' as activity_type,
    s.id as activity_id,
    s.user_id,
    s.title as activity_title,
    s.created_at as activity_time,
    'created' as action
FROM schedules s
WHERE s.created_at >= CURRENT_DATE - INTERVAL '7 days'

ORDER BY activity_time DESC;

-- 语音质量趋势视图
CREATE VIEW voice_quality_trends AS
SELECT 
    user_id,
    DATE(created_at) as date,
    AVG(audio_quality_score) as avg_quality,
    AVG(speech_rate_wpm) as avg_speech_rate,
    AVG(clarity_score) as avg_clarity,
    COUNT(*) as recording_count
FROM voice_analytics
WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY user_id, DATE(created_at)
ORDER BY user_id, date;

-- =====================================================
-- 物化视图
-- =====================================================

-- 每日分析物化视图
CREATE MATERIALIZED VIEW daily_analytics AS
SELECT 
    DATE(created_at) as date,
    COUNT(*) as total_activities,
    COUNT(DISTINCT user_id) as active_users,
    'voice_note' as activity_type
FROM voice_notes
WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY DATE(created_at)

UNION ALL

SELECT 
    DATE(last_activity_at) as date,
    COUNT(*) as total_activities,
    COUNT(DISTINCT user_id) as active_users,
    'conversation' as activity_type
FROM conversations
WHERE last_activity_at >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY DATE(last_activity_at)

UNION ALL

SELECT 
    DATE(created_at) as date,
    COUNT(*) as total_activities,
    COUNT(DISTINCT user_id) as active_users,
    'schedule' as activity_type
FROM schedules
WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY DATE(created_at)

ORDER BY date DESC;

-- 物化视图索引
CREATE INDEX idx_daily_analytics_date ON daily_analytics(date);
CREATE INDEX idx_daily_analytics_type ON daily_analytics(activity_type);

-- 刷新物化视图函数
CREATE OR REPLACE FUNCTION refresh_daily_analytics()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW daily_analytics;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 表注释
-- =====================================================

COMMENT ON TABLE users IS '用户主表 - 存储用户基本信息和认证数据';
COMMENT ON TABLE user_profiles IS '用户详细资料表 - 存储扩展的用户信息';
COMMENT ON TABLE conversations IS 'AI对话会话表 - 存储用户与AI的对话会话';
COMMENT ON TABLE chat_messages IS '聊天消息表 - 存储对话中的具体消息';
COMMENT ON TABLE voice_notes IS '语音笔记表 - 存储用户的语音录音和转写';
COMMENT ON TABLE schedules IS '日程安排表 - 存储用户的日历事件和约会';
COMMENT ON TABLE voice_analytics IS '语音分析表 - 存储语音质量和性能分析数据';
COMMENT ON TABLE mood_entries IS '情绪追踪表 - 存储用户情绪状态和心理健康数据';
COMMENT ON TABLE user_sessions IS '用户会话表 - 存储用户活动会话和使用追踪';
COMMENT ON TABLE audio_files IS '音频文件表 - 集中管理音频文件';
COMMENT ON TABLE notifications IS '通知表 - 存储系统和用户通知';
COMMENT ON TABLE user_achievements IS '用户成就表 - 存储用户成就和游戏化元素';
COMMENT ON TABLE system_settings IS '系统设置表 - 存储应用配置设置';

-- =====================================================
-- 初始化系统设置
-- =====================================================

INSERT INTO system_settings (setting_key, setting_value, setting_type, description, is_public) VALUES
('app_version', '1.0.0', 'STRING', '当前应用版本', true),
('maintenance_mode', 'false', 'BOOLEAN', '启用维护模式', false),
('max_file_size_mb', '50', 'INTEGER', '最大文件上传大小(MB)', true),
('supported_audio_formats', '["mp3", "wav", "m4a", "ogg", "flac"]', 'JSON', '支持的音频文件格式', true),
('default_language', 'zh-CN', 'STRING', '默认系统语言', true),
('voice_quality_threshold', '0.8', 'STRING', '最低语音质量阈值', false),
('max_conversation_history', '1000', 'INTEGER', '每用户最大对话历史记录', false),
('session_timeout_minutes', '30', 'INTEGER', '用户会话超时时间(分钟)', false),
('ai_model_version', 'gpt-3.5-turbo', 'STRING', '当前使用的AI模型版本', false),
('speech_recognition_provider', 'azure', 'STRING', '语音识别服务提供商', false),
('text_to_speech_provider', 'azure', 'STRING', '语音合成服务提供商', false),
('max_daily_requests', '1000', 'INTEGER', '每日最大请求数限制', false),
('backup_retention_days', '30', 'INTEGER', '备份保留天数', false),
('log_retention_days', '7', 'INTEGER', '日志保留天数', false);

-- =====================================================
-- 完成提示
-- =====================================================

-- 显示创建完成信息
DO $$
BEGIN
    RAISE NOTICE '==============================================';
    RAISE NOTICE 'VoiceHub 数据库初始化完成!';
    RAISE NOTICE '创建了 % 个表', (SELECT count(*) FROM information_schema.tables WHERE table_schema = 'public' AND table_type = 'BASE TABLE');
    RAISE NOTICE '创建了 % 个索引', (SELECT count(*) FROM pg_indexes WHERE schemaname = 'public');
    RAISE NOTICE '创建了 % 个视图', (SELECT count(*) FROM information_schema.views WHERE table_schema = 'public');
    RAISE NOTICE '==============================================';
END $$;