-- =====================================================
-- VoiceHub 智能语音助手平台 - 测试数据脚本
-- 描述: 插入测试数据用于开发和测试
-- =====================================================

-- 清理现有测试数据（可选）
-- TRUNCATE TABLE users CASCADE;

-- =====================================================
-- 插入测试用户
-- =====================================================

-- 管理员用户
INSERT INTO users (username, email, password_hash, full_name, preferred_language, subscription_type, is_verified) VALUES
('admin', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM5lE2cNQbRV5Em8b.Ey', '系统管理员', 'zh-CN', 'PREMIUM', true),
('testuser1', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM5lE2cNQbRV5Em8b.Ey', '测试用户一', 'zh-CN', 'FREE', true),
('testuser2', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM5lE2cNQbRV5Em8b.Ey', '测试用户二', 'zh-CN', 'BASIC', true),
('demouser', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM5lE2cNQbRV5Em8b.Ey', '演示用户', 'zh-CN', 'PREMIUM', true);

-- =====================================================
-- 插入用户详细资料
-- =====================================================

INSERT INTO user_profiles (user_id, bio, occupation, company, location, interests, goals) VALUES
(1, '系统管理员，负责平台维护和用户支持', '系统管理员', 'VoiceHub', '北京', ARRAY['技术', '管理', '用户体验'], ARRAY['提升平台稳定性', '优化用户体验']),
(2, '软件工程师，热爱新技术', '软件工程师', '科技公司', '上海', ARRAY['编程', '人工智能', '语音技术'], ARRAY['提高工作效率', '学习新技术']),
(3, '产品经理，专注用户体验设计', '产品经理', '互联网公司', '深圳', ARRAY['产品设计', '用户研究', '数据分析'], ARRAY['设计更好的产品', '理解用户需求']),
(4, '演示账户，用于产品展示', '演示用户', 'VoiceHub', '广州', ARRAY['语音助手', '智能科技'], ARRAY['体验新功能', '提供反馈']);

-- =====================================================
-- 插入测试对话
-- =====================================================

INSERT INTO conversations (user_id, title, type, status, mood_score) VALUES
(2, '工作日程规划', 'SCHEDULE', 'ACTIVE', 7.5),
(2, '技术学习笔记', 'GENERAL', 'ACTIVE', 8.0),
(3, '产品需求讨论', 'WORK', 'ACTIVE', 6.8),
(3, '用户反馈整理', 'ANALYSIS', 'COMPLETED', 7.2),
(4, '功能演示对话', 'DEMO', 'ACTIVE', 9.0);

-- =====================================================
-- 插入测试消息
-- =====================================================

INSERT INTO chat_messages (conversation_id, role, content, message_type, emotion, emotion_confidence) VALUES
(1, 'USER', '帮我安排明天上午10点的会议', 'TEXT', 'NEUTRAL', 0.85),
(1, 'ASSISTANT', '好的，我已经为您安排了明天上午10点的会议。请问会议主题是什么？', 'TEXT', 'HELPFUL', 0.90),
(1, 'USER', '讨论新项目的技术方案', 'TEXT', 'FOCUSED', 0.88),
(1, 'ASSISTANT', '已经创建会议：新项目技术方案讨论，时间：明天上午10:00-11:00。', 'TEXT', 'CONFIDENT', 0.92),

(2, 'USER', '今天学习了React Hooks的使用', 'TEXT', 'EXCITED', 0.87),
(2, 'ASSISTANT', '很好！React Hooks是现代React开发的重要概念。您学习了哪些具体的Hook？', 'TEXT', 'ENCOURAGING', 0.89),
(2, 'USER', '主要是useState和useEffect', 'TEXT', 'SATISFIED', 0.83),
(2, 'ASSISTANT', '这两个是最基础也是最重要的Hook。useState管理组件状态，useEffect处理副作用。建议您接下来学习useContext和useReducer。', 'TEXT', 'EDUCATIONAL', 0.91),

(3, 'USER', '用户反馈说语音识别准确率需要提升', 'TEXT', 'CONCERNED', 0.79),
(3, 'ASSISTANT', '这是一个重要的反馈。我们可以从以下几个方面改进：1. 优化噪音过滤算法 2. 增加训练数据 3. 调整识别模型参数。', 'TEXT', 'ANALYTICAL', 0.88),

(4, 'USER', '展示一下语音笔记功能', 'TEXT', 'CURIOUS', 0.86),
(4, 'ASSISTANT', '当然！语音笔记功能可以让您通过语音快速记录想法，系统会自动转写为文字，并支持智能分类和搜索。', 'TEXT', 'DEMONSTRATIVE', 0.93);

-- =====================================================
-- 插入测试语音笔记
-- =====================================================

INSERT INTO voice_notes (user_id, title, transcription, file_path, file_size, duration_seconds, audio_format, category, tags, transcription_confidence, language) VALUES
(2, '今日工作总结', '今天完成了用户认证模块的开发，包括JWT令牌生成和验证功能。明天计划开始语音识别API的集成工作。', '/uploads/voice_notes/user2_note1.wav', 1024000, 45, 'wav', 'WORK', ARRAY['工作', '开发', '总结'], 0.92, 'zh-CN'),
(2, '技术学习笔记', 'React Hooks的学习心得：useState用于状态管理，useEffect用于副作用处理，useContext用于跨组件状态共享。', '/uploads/voice_notes/user2_note2.wav', 856000, 38, 'wav', 'LEARNING', ARRAY['React', '技术', '学习'], 0.89, 'zh-CN'),
(3, '产品会议记录', '讨论了新版本的功能规划，重点是提升语音识别准确率和增加多语言支持。预计下个月发布beta版本。', '/uploads/voice_notes/user3_note1.wav', 1200000, 52, 'wav', 'MEETING', ARRAY['会议', '产品', '规划'], 0.94, 'zh-CN'),
(3, '用户反馈整理', '收集到的主要反馈：1. 语音识别准确率待提升 2. 界面需要更直观 3. 希望支持更多语言 4. 需要离线功能。', '/uploads/voice_notes/user3_note2.wav', 980000, 42, 'wav', 'FEEDBACK', ARRAY['反馈', '用户', '改进'], 0.91, 'zh-CN'),
(4, '功能演示脚本', '欢迎使用VoiceHub智能语音助手！这里演示语音笔记功能，您可以通过语音快速记录想法和灵感。', '/uploads/voice_notes/user4_demo1.wav', 720000, 28, 'wav', 'DEMO', ARRAY['演示', '介绍'], 0.96, 'zh-CN');

-- =====================================================
-- 插入测试日程安排
-- =====================================================

INSERT INTO schedules (user_id, title, description, start_time, end_time, location, priority, status, voice_created, voice_command) VALUES
(2, '项目技术评审', '对新项目的技术方案进行评审和讨论', '2024-01-15 10:00:00', '2024-01-15 11:30:00', '会议室A', 'HIGH', 'SCHEDULED', true, '安排明天上午10点的技术评审会议'),
(2, '代码审查', '审查本周提交的代码，确保质量标准', '2024-01-16 14:00:00', '2024-01-16 15:00:00', '开发区', 'MEDIUM', 'SCHEDULED', false, null),
(3, '用户调研会议', '与用户代表讨论产品需求和改进建议', '2024-01-17 09:00:00', '2024-01-17 10:30:00', '用户体验室', 'HIGH', 'SCHEDULED', true, '安排后天上午9点的用户调研'),
(3, '产品规划会', '制定下季度产品发展规划', '2024-01-18 15:00:00', '2024-01-18 17:00:00', '战略会议室', 'HIGH', 'SCHEDULED', false, null),
(4, '功能演示准备', '准备下周的产品功能演示', '2024-01-19 13:00:00', '2024-01-19 14:00:00', '演示厅', 'MEDIUM', 'SCHEDULED', true, '安排周五下午1点的演示准备');

-- =====================================================
-- 插入测试语音分析数据
-- =====================================================

INSERT INTO voice_analytics (user_id, recording_type, recording_id, duration_seconds, audio_quality_score, speech_rate_wpm, clarity_score, emotion_detected, emotion_confidence, language_detected, language_confidence) VALUES
(2, 'NOTE', 1, 45, 8.5, 150, 8.2, 'FOCUSED', 0.87, 'zh-CN', 0.95),
(2, 'NOTE', 2, 38, 8.8, 145, 8.6, 'EXCITED', 0.82, 'zh-CN', 0.96),
(3, 'NOTE', 3, 52, 8.3, 160, 8.0, 'PROFESSIONAL', 0.89, 'zh-CN', 0.94),
(3, 'NOTE', 4, 42, 8.7, 155, 8.4, 'ANALYTICAL', 0.85, 'zh-CN', 0.93),
(4, 'NOTE', 5, 28, 9.2, 140, 9.0, 'CONFIDENT', 0.91, 'zh-CN', 0.97);

-- =====================================================
-- 插入测试情绪数据
-- =====================================================

INSERT INTO mood_entries (user_id, mood, intensity, note, triggers, context) VALUES
(2, 'FOCUSED', 8, '今天工作状态很好，完成了重要的开发任务', ARRAY['工作成就', '技术突破'], 'VOICE_SESSION'),
(2, 'EXCITED', 7, '学习新技术让我很兴奋', ARRAY['学习', '新知识'], 'VOICE_SESSION'),
(3, 'PROFESSIONAL', 7, '会议讨论很有成效', ARRAY['团队合作', '产品规划'], 'VOICE_SESSION'),
(3, 'CONCERNED', 6, '用户反馈让我有些担心产品质量', ARRAY['用户反馈', '质量问题'], 'VOICE_SESSION'),
(4, 'CONFIDENT', 9, '演示准备充分，对产品很有信心', ARRAY['产品演示', '功能完善'], 'VOICE_SESSION');

-- =====================================================
-- 插入测试音频文件记录
-- =====================================================

INSERT INTO audio_files (user_id, file_name, original_name, file_path, file_size, duration_seconds, audio_format, upload_source, processing_status, transcription_status) VALUES
(2, 'voice_note_20240115_001.wav', '工作总结.wav', '/uploads/audio/2024/01/voice_note_20240115_001.wav', 1024000, 45, 'wav', 'VOICE_NOTE', 'COMPLETED', 'COMPLETED'),
(2, 'voice_note_20240115_002.wav', '学习笔记.wav', '/uploads/audio/2024/01/voice_note_20240115_002.wav', 856000, 38, 'wav', 'VOICE_NOTE', 'COMPLETED', 'COMPLETED'),
(3, 'voice_note_20240115_003.wav', '会议记录.wav', '/uploads/audio/2024/01/voice_note_20240115_003.wav', 1200000, 52, 'wav', 'VOICE_NOTE', 'COMPLETED', 'COMPLETED'),
(3, 'voice_note_20240115_004.wav', '反馈整理.wav', '/uploads/audio/2024/01/voice_note_20240115_004.wav', 980000, 42, 'wav', 'VOICE_NOTE', 'COMPLETED', 'COMPLETED'),
(4, 'demo_voice_001.wav', '演示录音.wav', '/uploads/audio/2024/01/demo_voice_001.wav', 720000, 28, 'wav', 'VOICE_NOTE', 'COMPLETED', 'COMPLETED');

-- =====================================================
-- 插入测试用户会话
-- =====================================================

INSERT INTO user_sessions (user_id, session_type, start_time, end_time, duration_seconds, activities_count, device_info, ip_address) VALUES
(2, 'VOICE_RECORDING', '2024-01-15 09:00:00', '2024-01-15 09:15:00', 900, 2, '{"device": "iPhone 14", "os": "iOS 16.0", "browser": "Safari"}', '*************'),
(2, 'CONVERSATION', '2024-01-15 14:00:00', '2024-01-15 14:20:00', 1200, 4, '{"device": "MacBook Pro", "os": "macOS 13.0", "browser": "Chrome"}', '*************'),
(3, 'SCHEDULE_MANAGEMENT', '2024-01-15 10:30:00', '2024-01-15 10:45:00', 900, 3, '{"device": "iPad Pro", "os": "iPadOS 16.0", "browser": "Safari"}', '*************'),
(3, 'VOICE_RECORDING', '2024-01-15 16:00:00', '2024-01-15 16:12:00', 720, 2, '{"device": "iPhone 13", "os": "iOS 15.0", "browser": "Safari"}', '*************'),
(4, 'CONVERSATION', '2024-01-15 11:00:00', '2024-01-15 11:10:00', 600, 2, '{"device": "Windows PC", "os": "Windows 11", "browser": "Edge"}', '*************');

-- =====================================================
-- 插入测试通知
-- =====================================================

INSERT INTO notifications (user_id, type, title, message, priority, is_read) VALUES
(2, 'REMINDER', '会议提醒', '您有一个会议将在30分钟后开始：项目技术评审', 'HIGH', false),
(2, 'ACHIEVEMENT', '成就解锁', '恭喜！您已连续使用语音笔记功能7天', 'NORMAL', false),
(3, 'SYSTEM', '功能更新', '语音识别准确率已提升至95%，快来体验吧！', 'NORMAL', true),
(3, 'MOOD_CHECK', '情绪关怀', '今天感觉怎么样？记录一下您的心情吧', 'LOW', false),
(4, 'SYSTEM', '欢迎使用', '欢迎使用VoiceHub智能语音助手！', 'NORMAL', true);

-- =====================================================
-- 插入测试用户成就
-- =====================================================

INSERT INTO user_achievements (user_id, achievement_type, achievement_name, description, points, badge_icon) VALUES
(2, 'FIRST_RECORDING', '首次录音', '完成第一次语音录音', 10, 'microphone'),
(2, 'STREAK_7_DAYS', '连续使用7天', '连续7天使用语音功能', 50, 'calendar-check'),
(2, 'QUALITY_IMPROVEMENT', '语音质量提升', '语音质量评分达到8.5分以上', 30, 'trending-up'),
(3, 'FIRST_RECORDING', '首次录音', '完成第一次语音录音', 10, 'microphone'),
(3, 'PRODUCTIVE_USER', '高效用户', '单日完成10个以上语音任务', 40, 'zap'),
(4, 'DEMO_MASTER', '演示专家', '完成产品功能演示', 20, 'presentation');

-- =====================================================
-- 更新统计数据
-- =====================================================

-- 更新对话消息计数
UPDATE conversations SET message_count = (
    SELECT COUNT(*) FROM chat_messages WHERE conversation_id = conversations.id
);

-- 刷新物化视图
REFRESH MATERIALIZED VIEW daily_analytics;

-- =====================================================
-- 验证数据插入
-- =====================================================

DO $$
DECLARE
    user_count INTEGER;
    conversation_count INTEGER;
    note_count INTEGER;
    schedule_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO user_count FROM users;
    SELECT COUNT(*) INTO conversation_count FROM conversations;
    SELECT COUNT(*) INTO note_count FROM voice_notes;
    SELECT COUNT(*) INTO schedule_count FROM schedules;
    
    RAISE NOTICE '==============================================';
    RAISE NOTICE 'VoiceHub 测试数据插入完成!';
    RAISE NOTICE '插入用户数: %', user_count;
    RAISE NOTICE '插入对话数: %', conversation_count;
    RAISE NOTICE '插入语音笔记数: %', note_count;
    RAISE NOTICE '插入日程数: %', schedule_count;
    RAISE NOTICE '==============================================';
END $$;

-- =====================================================
-- 测试查询示例
-- =====================================================

-- 查看用户统计
-- SELECT * FROM user_stats;

-- 查看最近活动
-- SELECT * FROM recent_activity LIMIT 10;

-- 查看语音质量趋势
-- SELECT * FROM voice_quality_trends WHERE user_id = 2;

-- 查看每日分析数据
-- SELECT * FROM daily_analytics ORDER BY date DESC LIMIT 7;
