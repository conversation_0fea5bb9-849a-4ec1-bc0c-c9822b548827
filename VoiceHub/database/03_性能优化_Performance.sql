-- =====================================================
-- VoiceHub 智能语音助手平台 - 性能优化脚本
-- 描述: 数据库性能优化配置和查询优化
-- =====================================================

-- =====================================================
-- 数据库配置优化
-- =====================================================

-- 设置共享缓冲区大小（建议为系统内存的25%）
-- ALTER SYSTEM SET shared_buffers = '256MB';

-- 设置工作内存大小
-- ALTER SYSTEM SET work_mem = '4MB';

-- 设置维护工作内存
-- ALTER SYSTEM SET maintenance_work_mem = '64MB';

-- 设置有效缓存大小
-- ALTER SYSTEM SET effective_cache_size = '1GB';

-- 启用查询计划缓存
-- ALTER SYSTEM SET plan_cache_mode = 'auto';

-- 设置检查点配置
-- ALTER SYSTEM SET checkpoint_completion_target = 0.7;
-- ALTER SYSTEM SET wal_buffers = '16MB';

-- 设置日志配置
-- ALTER SYSTEM SET log_min_duration_statement = 1000;
-- ALTER SYSTEM SET log_checkpoints = on;
-- ALTER SYSTEM SET log_connections = on;
-- ALTER SYSTEM SET log_disconnections = on;

-- =====================================================
-- 分区表创建（用于大数据量场景）
-- =====================================================

-- 按月分区的语音分析表
CREATE TABLE voice_analytics_partitioned (
    LIKE voice_analytics INCLUDING ALL
) PARTITION BY RANGE (created_at);

-- 创建月度分区
CREATE TABLE voice_analytics_2024_01 PARTITION OF voice_analytics_partitioned
    FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');

CREATE TABLE voice_analytics_2024_02 PARTITION OF voice_analytics_partitioned
    FOR VALUES FROM ('2024-02-01') TO ('2024-03-01');

CREATE TABLE voice_analytics_2024_03 PARTITION OF voice_analytics_partitioned
    FOR VALUES FROM ('2024-03-01') TO ('2024-04-01');

-- 按用户ID分区的聊天消息表（用于高并发场景）
CREATE TABLE chat_messages_partitioned (
    LIKE chat_messages INCLUDING ALL
) PARTITION BY HASH (user_id);

-- 创建哈希分区
CREATE TABLE chat_messages_part_0 PARTITION OF chat_messages_partitioned
    FOR VALUES WITH (modulus 4, remainder 0);

CREATE TABLE chat_messages_part_1 PARTITION OF chat_messages_partitioned
    FOR VALUES WITH (modulus 4, remainder 1);

CREATE TABLE chat_messages_part_2 PARTITION OF chat_messages_partitioned
    FOR VALUES WITH (modulus 4, remainder 2);

CREATE TABLE chat_messages_part_3 PARTITION OF chat_messages_partitioned
    FOR VALUES WITH (modulus 4, remainder 3);

-- =====================================================
-- 复合索引优化
-- =====================================================

-- 用户活动复合索引
CREATE INDEX idx_users_active_created ON users(is_active, created_at) WHERE is_active = true;

-- 对话状态和时间复合索引
CREATE INDEX idx_conversations_user_status_time ON conversations(user_id, status, last_activity_at);

-- 语音笔记分类和时间复合索引
CREATE INDEX idx_voice_notes_user_category_time ON voice_notes(user_id, category, created_at) WHERE is_archived = false;

-- 消息类型和时间复合索引
CREATE INDEX idx_chat_messages_conv_type_time ON chat_messages(conversation_id, message_type, created_at);

-- 日程状态和时间复合索引
CREATE INDEX idx_schedules_user_status_start ON schedules(user_id, status, start_time);

-- 语音分析类型和时间复合索引
CREATE INDEX idx_voice_analytics_user_type_time ON voice_analytics(user_id, recording_type, created_at);

-- 通知状态和优先级复合索引
CREATE INDEX idx_notifications_user_read_priority ON notifications(user_id, is_read, priority);

-- =====================================================
-- 部分索引（条件索引）
-- =====================================================

-- 仅为活跃用户创建索引
CREATE INDEX idx_users_active_email ON users(email) WHERE is_active = true;

-- 仅为未读通知创建索引
CREATE INDEX idx_notifications_unread ON notifications(user_id, created_at) WHERE is_read = false;

-- 仅为未归档的语音笔记创建索引
CREATE INDEX idx_voice_notes_active ON voice_notes(user_id, created_at) WHERE is_archived = false;

-- 仅为进行中的对话创建索引
CREATE INDEX idx_conversations_active ON conversations(user_id, last_activity_at) WHERE status = 'ACTIVE';

-- =====================================================
-- 表达式索引
-- =====================================================

-- 用户名小写索引（用于不区分大小写搜索）
CREATE INDEX idx_users_username_lower ON users(LOWER(username));

-- 邮箱小写索引
CREATE INDEX idx_users_email_lower ON users(LOWER(email));

-- 语音笔记标题全文搜索索引
CREATE INDEX idx_voice_notes_title_fts ON voice_notes USING GIN(to_tsvector('chinese', title));

-- 对话标题全文搜索索引
CREATE INDEX idx_conversations_title_fts ON conversations USING GIN(to_tsvector('chinese', title));

-- =====================================================
-- 聚集索引优化
-- =====================================================

-- 重新组织表以优化物理存储
-- CLUSTER users USING idx_users_created_at;
-- CLUSTER conversations USING idx_conversations_user_id;
-- CLUSTER voice_notes USING idx_voice_notes_user_id;

-- =====================================================
-- 统计信息更新
-- =====================================================

-- 创建自动更新统计信息的函数
CREATE OR REPLACE FUNCTION update_table_statistics()
RETURNS void AS $$
BEGIN
    -- 更新主要表的统计信息
    ANALYZE users;
    ANALYZE conversations;
    ANALYZE chat_messages;
    ANALYZE voice_notes;
    ANALYZE schedules;
    ANALYZE voice_analytics;
    ANALYZE audio_files;
    
    RAISE NOTICE '统计信息更新完成';
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 查询优化视图
-- =====================================================

-- 用户活动摘要视图（优化版）
CREATE VIEW user_activity_summary AS
SELECT 
    u.id,
    u.username,
    u.full_name,
    u.last_login_at,
    COALESCE(stats.note_count, 0) as voice_notes_count,
    COALESCE(stats.conversation_count, 0) as conversations_count,
    COALESCE(stats.schedule_count, 0) as schedules_count,
    COALESCE(stats.avg_quality, 0) as avg_voice_quality
FROM users u
LEFT JOIN (
    SELECT 
        user_id,
        COUNT(CASE WHEN table_name = 'voice_notes' THEN 1 END) as note_count,
        COUNT(CASE WHEN table_name = 'conversations' THEN 1 END) as conversation_count,
        COUNT(CASE WHEN table_name = 'schedules' THEN 1 END) as schedule_count,
        AVG(CASE WHEN table_name = 'voice_analytics' THEN quality_score END) as avg_quality
    FROM (
        SELECT user_id, 'voice_notes' as table_name, NULL as quality_score FROM voice_notes WHERE is_archived = false
        UNION ALL
        SELECT user_id, 'conversations' as table_name, NULL as quality_score FROM conversations WHERE is_archived = false
        UNION ALL
        SELECT user_id, 'schedules' as table_name, NULL as quality_score FROM schedules
        UNION ALL
        SELECT user_id, 'voice_analytics' as table_name, audio_quality_score as quality_score FROM voice_analytics
    ) combined
    GROUP BY user_id
) stats ON u.id = stats.user_id
WHERE u.is_active = true;

-- 热门标签视图
CREATE VIEW popular_tags AS
SELECT 
    tag,
    COUNT(*) as usage_count,
    COUNT(DISTINCT user_id) as user_count
FROM (
    SELECT user_id, unnest(tags) as tag
    FROM voice_notes
    WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'
    AND is_archived = false
) tag_usage
GROUP BY tag
HAVING COUNT(*) >= 2
ORDER BY usage_count DESC, user_count DESC;

-- =====================================================
-- 缓存表创建
-- =====================================================

-- 用户统计缓存表
CREATE TABLE user_stats_cache (
    user_id BIGINT PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
    total_voice_notes INTEGER DEFAULT 0,
    total_conversations INTEGER DEFAULT 0,
    total_schedules INTEGER DEFAULT 0,
    avg_voice_quality DECIMAL(4,2) DEFAULT 0,
    total_session_time INTEGER DEFAULT 0,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建缓存更新函数
CREATE OR REPLACE FUNCTION refresh_user_stats_cache(target_user_id BIGINT DEFAULT NULL)
RETURNS void AS $$
BEGIN
    IF target_user_id IS NOT NULL THEN
        -- 更新特定用户的统计
        INSERT INTO user_stats_cache (user_id, total_voice_notes, total_conversations, total_schedules, avg_voice_quality, total_session_time)
        SELECT 
            u.id,
            COUNT(DISTINCT vn.id),
            COUNT(DISTINCT c.id),
            COUNT(DISTINCT s.id),
            AVG(va.audio_quality_score),
            SUM(us.duration_seconds)
        FROM users u
        LEFT JOIN voice_notes vn ON u.id = vn.user_id AND vn.is_archived = false
        LEFT JOIN conversations c ON u.id = c.user_id AND c.is_archived = false
        LEFT JOIN schedules s ON u.id = s.user_id
        LEFT JOIN voice_analytics va ON u.id = va.user_id
        LEFT JOIN user_sessions us ON u.id = us.user_id
        WHERE u.id = target_user_id AND u.is_active = true
        GROUP BY u.id
        ON CONFLICT (user_id) DO UPDATE SET
            total_voice_notes = EXCLUDED.total_voice_notes,
            total_conversations = EXCLUDED.total_conversations,
            total_schedules = EXCLUDED.total_schedules,
            avg_voice_quality = EXCLUDED.avg_voice_quality,
            total_session_time = EXCLUDED.total_session_time,
            last_updated = CURRENT_TIMESTAMP;
    ELSE
        -- 更新所有用户的统计
        TRUNCATE user_stats_cache;
        INSERT INTO user_stats_cache (user_id, total_voice_notes, total_conversations, total_schedules, avg_voice_quality, total_session_time)
        SELECT 
            u.id,
            COUNT(DISTINCT vn.id),
            COUNT(DISTINCT c.id),
            COUNT(DISTINCT s.id),
            AVG(va.audio_quality_score),
            SUM(us.duration_seconds)
        FROM users u
        LEFT JOIN voice_notes vn ON u.id = vn.user_id AND vn.is_archived = false
        LEFT JOIN conversations c ON u.id = c.user_id AND c.is_archived = false
        LEFT JOIN schedules s ON u.id = s.user_id
        LEFT JOIN voice_analytics va ON u.id = va.user_id
        LEFT JOIN user_sessions us ON u.id = us.user_id
        WHERE u.is_active = true
        GROUP BY u.id;
    END IF;
    
    RAISE NOTICE '用户统计缓存更新完成';
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 自动维护任务
-- =====================================================

-- 创建自动清理过期数据的函数
CREATE OR REPLACE FUNCTION cleanup_expired_data()
RETURNS void AS $$
BEGIN
    -- 清理过期的用户会话（超过30天）
    DELETE FROM user_sessions 
    WHERE created_at < CURRENT_DATE - INTERVAL '30 days';
    
    -- 清理过期的通知（超过7天且已读）
    DELETE FROM notifications 
    WHERE created_at < CURRENT_DATE - INTERVAL '7 days' 
    AND is_read = true;
    
    -- 清理过期的语音分析数据（超过90天）
    DELETE FROM voice_analytics 
    WHERE created_at < CURRENT_DATE - INTERVAL '90 days';
    
    -- 清理过期的情绪记录（超过180天）
    DELETE FROM mood_entries 
    WHERE created_at < CURRENT_DATE - INTERVAL '180 days';
    
    RAISE NOTICE '过期数据清理完成';
END;
$$ LANGUAGE plpgsql;

-- 创建数据库维护函数
CREATE OR REPLACE FUNCTION database_maintenance()
RETURNS void AS $$
BEGIN
    -- 更新统计信息
    PERFORM update_table_statistics();
    
    -- 刷新物化视图
    REFRESH MATERIALIZED VIEW daily_analytics;
    
    -- 更新用户统计缓存
    PERFORM refresh_user_stats_cache();
    
    -- 清理过期数据
    PERFORM cleanup_expired_data();
    
    -- 重建索引（可选，根据需要启用）
    -- REINDEX DATABASE voicehub;
    
    RAISE NOTICE '数据库维护完成';
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 监控查询
-- =====================================================

-- 慢查询监控视图
CREATE VIEW slow_queries AS
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    rows,
    100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
FROM pg_stat_statements
WHERE mean_time > 100  -- 平均执行时间超过100ms的查询
ORDER BY mean_time DESC;

-- 表大小监控视图
CREATE VIEW table_sizes AS
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
    pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- 索引使用情况监控视图
CREATE VIEW index_usage AS
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_tup_read,
    idx_tup_fetch,
    idx_scan,
    pg_size_pretty(pg_relation_size(indexrelid)) as index_size
FROM pg_stat_user_indexes
ORDER BY idx_scan DESC;

-- =====================================================
-- 性能测试函数
-- =====================================================

-- 创建性能测试函数
CREATE OR REPLACE FUNCTION performance_test()
RETURNS TABLE(test_name TEXT, execution_time INTERVAL, result_count BIGINT) AS $$
DECLARE
    start_time TIMESTAMP;
    end_time TIMESTAMP;
BEGIN
    -- 测试用户查询性能
    start_time := clock_timestamp();
    PERFORM COUNT(*) FROM users WHERE is_active = true;
    end_time := clock_timestamp();
    
    test_name := '活跃用户查询';
    execution_time := end_time - start_time;
    SELECT COUNT(*) INTO result_count FROM users WHERE is_active = true;
    RETURN NEXT;
    
    -- 测试语音笔记查询性能
    start_time := clock_timestamp();
    PERFORM COUNT(*) FROM voice_notes WHERE created_at >= CURRENT_DATE - INTERVAL '7 days';
    end_time := clock_timestamp();
    
    test_name := '最近语音笔记查询';
    execution_time := end_time - start_time;
    SELECT COUNT(*) INTO result_count FROM voice_notes WHERE created_at >= CURRENT_DATE - INTERVAL '7 days';
    RETURN NEXT;
    
    -- 测试复杂连接查询性能
    start_time := clock_timestamp();
    PERFORM COUNT(*) FROM users u 
    JOIN conversations c ON u.id = c.user_id 
    WHERE u.is_active = true AND c.status = 'ACTIVE';
    end_time := clock_timestamp();
    
    test_name := '用户对话连接查询';
    execution_time := end_time - start_time;
    SELECT COUNT(*) INTO result_count FROM users u 
    JOIN conversations c ON u.id = c.user_id 
    WHERE u.is_active = true AND c.status = 'ACTIVE';
    RETURN NEXT;
    
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 连接池优化建议
-- =====================================================

-- 设置连接池参数（在应用层配置）
/*
建议的连接池配置：
- 最小连接数: 5
- 最大连接数: 20
- 连接超时: 30秒
- 空闲超时: 10分钟
- 验证查询: SELECT 1
*/

-- =====================================================
-- 备份和恢复优化
-- =====================================================

-- 创建备份函数
CREATE OR REPLACE FUNCTION create_backup(backup_name TEXT DEFAULT NULL)
RETURNS TEXT AS $$
DECLARE
    backup_file TEXT;
BEGIN
    IF backup_name IS NULL THEN
        backup_file := 'voicehub_backup_' || to_char(CURRENT_TIMESTAMP, 'YYYYMMDD_HH24MISS');
    ELSE
        backup_file := backup_name;
    END IF;
    
    -- 这里只是示例，实际备份需要在系统层面执行
    -- COPY (SELECT 'pg_dump voicehub > ' || backup_file || '.sql') TO '/tmp/backup_command.txt';
    
    RETURN '备份文件名: ' || backup_file || '.sql';
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 性能监控报告
-- =====================================================

-- 创建性能报告函数
CREATE OR REPLACE FUNCTION generate_performance_report()
RETURNS TABLE(
    metric_name TEXT,
    metric_value TEXT,
    status TEXT,
    recommendation TEXT
) AS $$
BEGIN
    -- 数据库大小检查
    metric_name := '数据库大小';
    SELECT pg_size_pretty(pg_database_size(current_database())) INTO metric_value;
    status := CASE 
        WHEN pg_database_size(current_database()) > 10737418240 THEN 'WARNING'  -- 10GB
        ELSE 'OK' 
    END;
    recommendation := CASE 
        WHEN pg_database_size(current_database()) > 10737418240 THEN '考虑数据归档或分区'
        ELSE '数据库大小正常'
    END;
    RETURN NEXT;
    
    -- 活跃连接数检查
    metric_name := '活跃连接数';
    SELECT COUNT(*)::TEXT INTO metric_value FROM pg_stat_activity WHERE state = 'active';
    status := CASE 
        WHEN (SELECT COUNT(*) FROM pg_stat_activity WHERE state = 'active') > 50 THEN 'WARNING'
        ELSE 'OK'
    END;
    recommendation := CASE 
        WHEN (SELECT COUNT(*) FROM pg_stat_activity WHERE state = 'active') > 50 THEN '检查连接池配置'
        ELSE '连接数正常'
    END;
    RETURN NEXT;
    
    -- 缓存命中率检查
    metric_name := '缓存命中率';
    SELECT ROUND(100.0 * sum(blks_hit) / (sum(blks_hit) + sum(blks_read)), 2)::TEXT || '%' 
    INTO metric_value FROM pg_stat_database WHERE datname = current_database();
    status := CASE 
        WHEN (SELECT 100.0 * sum(blks_hit) / (sum(blks_hit) + sum(blks_read)) FROM pg_stat_database WHERE datname = current_database()) < 90 THEN 'WARNING'
        ELSE 'OK'
    END;
    recommendation := CASE 
        WHEN (SELECT 100.0 * sum(blks_hit) / (sum(blks_hit) + sum(blks_read)) FROM pg_stat_database WHERE datname = current_database()) < 90 THEN '增加shared_buffers大小'
        ELSE '缓存命中率良好'
    END;
    RETURN NEXT;
    
    -- 最大表大小检查
    metric_name := '最大表大小';
    SELECT pg_size_pretty(MAX(pg_total_relation_size(schemaname||'.'||tablename))) 
    INTO metric_value FROM pg_tables WHERE schemaname = 'public';
    status := 'INFO';
    recommendation := '监控表增长趋势';
    RETURN NEXT;
    
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 完成提示
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '==============================================';
    RAISE NOTICE 'VoiceHub 性能优化脚本执行完成!';
    RAISE NOTICE '已创建性能优化索引和函数';
    RAISE NOTICE '建议定期执行 database_maintenance() 函数';
    RAISE NOTICE '使用 SELECT * FROM generate_performance_report() 查看性能报告';
    RAISE NOTICE '==============================================';
END $$;
