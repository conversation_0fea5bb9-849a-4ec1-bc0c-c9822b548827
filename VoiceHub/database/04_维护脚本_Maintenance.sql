-- =====================================================
-- VoiceHub 智能语音助手平台 - 数据库维护脚本
-- 描述: 数据库日常维护、备份、清理和监控脚本
-- =====================================================

-- =====================================================
-- 数据清理函数
-- =====================================================

-- 清理过期会话数据
CREATE OR REPLACE FUNCTION cleanup_expired_sessions()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- 删除30天前的用户会话记录
    DELETE FROM user_sessions 
    WHERE created_at < CURRENT_DATE - INTERVAL '30 days';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RAISE NOTICE '已清理 % 条过期会话记录', deleted_count;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- 清理过期通知
CREATE OR REPLACE FUNCTION cleanup_expired_notifications()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- 删除已读且超过7天的通知
    DELETE FROM notifications 
    WHERE is_read = true 
    AND created_at < CURRENT_DATE - INTERVAL '7 days';
    
    -- 删除过期的未读通知（超过30天）
    DELETE FROM notifications 
    WHERE expires_at IS NOT NULL 
    AND expires_at < CURRENT_TIMESTAMP;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RAISE NOTICE '已清理 % 条过期通知', deleted_count;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- 清理过期语音分析数据
CREATE OR REPLACE FUNCTION cleanup_old_analytics()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- 删除90天前的语音分析数据
    DELETE FROM voice_analytics 
    WHERE created_at < CURRENT_DATE - INTERVAL '90 days';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RAISE NOTICE '已清理 % 条过期分析数据', deleted_count;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- 清理孤立的音频文件记录
CREATE OR REPLACE FUNCTION cleanup_orphaned_audio_files()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- 删除没有关联语音笔记的音频文件记录
    DELETE FROM audio_files af
    WHERE af.upload_source = 'VOICE_NOTE'
    AND NOT EXISTS (
        SELECT 1 FROM voice_notes vn 
        WHERE vn.file_path = af.file_path
    );
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RAISE NOTICE '已清理 % 条孤立音频文件记录', deleted_count;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- 综合清理函数
CREATE OR REPLACE FUNCTION comprehensive_cleanup()
RETURNS TABLE(cleanup_type TEXT, deleted_count INTEGER) AS $$
BEGIN
    -- 清理过期会话
    cleanup_type := '过期会话';
    SELECT cleanup_expired_sessions() INTO deleted_count;
    RETURN NEXT;
    
    -- 清理过期通知
    cleanup_type := '过期通知';
    SELECT cleanup_expired_notifications() INTO deleted_count;
    RETURN NEXT;
    
    -- 清理过期分析数据
    cleanup_type := '过期分析数据';
    SELECT cleanup_old_analytics() INTO deleted_count;
    RETURN NEXT;
    
    -- 清理孤立文件
    cleanup_type := '孤立音频文件';
    SELECT cleanup_orphaned_audio_files() INTO deleted_count;
    RETURN NEXT;
    
    -- 清理空的对话
    cleanup_type := '空对话';
    DELETE FROM conversations 
    WHERE message_count = 0 
    AND created_at < CURRENT_DATE - INTERVAL '7 days';
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN NEXT;
    
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 数据归档函数
-- =====================================================

-- 创建归档表
CREATE TABLE IF NOT EXISTS archived_voice_notes (
    LIKE voice_notes INCLUDING ALL,
    archived_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS archived_conversations (
    LIKE conversations INCLUDING ALL,
    archived_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 归档旧的语音笔记
CREATE OR REPLACE FUNCTION archive_old_voice_notes(days_old INTEGER DEFAULT 365)
RETURNS INTEGER AS $$
DECLARE
    archived_count INTEGER;
BEGIN
    -- 将旧的语音笔记移动到归档表
    INSERT INTO archived_voice_notes 
    SELECT *, CURRENT_TIMESTAMP 
    FROM voice_notes 
    WHERE created_at < CURRENT_DATE - INTERVAL '1 day' * days_old
    AND is_archived = false;
    
    GET DIAGNOSTICS archived_count = ROW_COUNT;
    
    -- 标记原记录为已归档
    UPDATE voice_notes 
    SET is_archived = true, updated_at = CURRENT_TIMESTAMP
    WHERE created_at < CURRENT_DATE - INTERVAL '1 day' * days_old
    AND is_archived = false;
    
    RAISE NOTICE '已归档 % 条语音笔记', archived_count;
    RETURN archived_count;
END;
$$ LANGUAGE plpgsql;

-- 归档旧的对话
CREATE OR REPLACE FUNCTION archive_old_conversations(days_old INTEGER DEFAULT 180)
RETURNS INTEGER AS $$
DECLARE
    archived_count INTEGER;
BEGIN
    -- 将旧的对话移动到归档表
    INSERT INTO archived_conversations 
    SELECT *, CURRENT_TIMESTAMP 
    FROM conversations 
    WHERE last_activity_at < CURRENT_DATE - INTERVAL '1 day' * days_old
    AND is_archived = false;
    
    GET DIAGNOSTICS archived_count = ROW_COUNT;
    
    -- 标记原记录为已归档
    UPDATE conversations 
    SET is_archived = true, updated_at = CURRENT_TIMESTAMP
    WHERE last_activity_at < CURRENT_DATE - INTERVAL '1 day' * days_old
    AND is_archived = false;
    
    RAISE NOTICE '已归档 % 条对话', archived_count;
    RETURN archived_count;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 数据库健康检查
-- =====================================================

-- 检查数据库连接
CREATE OR REPLACE FUNCTION check_database_health()
RETURNS TABLE(
    check_name TEXT,
    status TEXT,
    value TEXT,
    recommendation TEXT
) AS $$
DECLARE
    db_size BIGINT;
    active_connections INTEGER;
    cache_hit_ratio NUMERIC;
    deadlock_count BIGINT;
BEGIN
    -- 检查数据库大小
    SELECT pg_database_size(current_database()) INTO db_size;
    check_name := '数据库大小';
    value := pg_size_pretty(db_size);
    status := CASE 
        WHEN db_size > 21474836480 THEN 'CRITICAL'  -- 20GB
        WHEN db_size > 10737418240 THEN 'WARNING'   -- 10GB
        ELSE 'OK' 
    END;
    recommendation := CASE 
        WHEN db_size > 21474836480 THEN '立即进行数据归档'
        WHEN db_size > 10737418240 THEN '考虑数据归档'
        ELSE '数据库大小正常'
    END;
    RETURN NEXT;
    
    -- 检查活跃连接数
    SELECT COUNT(*) INTO active_connections 
    FROM pg_stat_activity 
    WHERE state = 'active' AND pid != pg_backend_pid();
    
    check_name := '活跃连接数';
    value := active_connections::TEXT;
    status := CASE 
        WHEN active_connections > 80 THEN 'CRITICAL'
        WHEN active_connections > 50 THEN 'WARNING'
        ELSE 'OK'
    END;
    recommendation := CASE 
        WHEN active_connections > 80 THEN '立即检查连接泄漏'
        WHEN active_connections > 50 THEN '优化连接池配置'
        ELSE '连接数正常'
    END;
    RETURN NEXT;
    
    -- 检查缓存命中率
    SELECT ROUND(100.0 * sum(blks_hit) / NULLIF(sum(blks_hit) + sum(blks_read), 0), 2)
    INTO cache_hit_ratio
    FROM pg_stat_database 
    WHERE datname = current_database();
    
    check_name := '缓存命中率';
    value := COALESCE(cache_hit_ratio::TEXT || '%', '0%');
    status := CASE 
        WHEN cache_hit_ratio < 85 THEN 'CRITICAL'
        WHEN cache_hit_ratio < 95 THEN 'WARNING'
        ELSE 'OK'
    END;
    recommendation := CASE 
        WHEN cache_hit_ratio < 85 THEN '增加shared_buffers配置'
        WHEN cache_hit_ratio < 95 THEN '考虑优化查询或增加内存'
        ELSE '缓存性能良好'
    END;
    RETURN NEXT;
    
    -- 检查死锁数量
    SELECT COALESCE(deadlocks, 0) INTO deadlock_count
    FROM pg_stat_database 
    WHERE datname = current_database();
    
    check_name := '死锁数量';
    value := deadlock_count::TEXT;
    status := CASE 
        WHEN deadlock_count > 100 THEN 'WARNING'
        ELSE 'OK'
    END;
    recommendation := CASE 
        WHEN deadlock_count > 100 THEN '检查应用程序事务逻辑'
        ELSE '死锁数量正常'
    END;
    RETURN NEXT;
    
    -- 检查长时间运行的查询
    SELECT COUNT(*) INTO active_connections
    FROM pg_stat_activity 
    WHERE state = 'active' 
    AND query_start < CURRENT_TIMESTAMP - INTERVAL '5 minutes'
    AND pid != pg_backend_pid();
    
    check_name := '长时间运行查询';
    value := active_connections::TEXT;
    status := CASE 
        WHEN active_connections > 5 THEN 'WARNING'
        WHEN active_connections > 0 THEN 'INFO'
        ELSE 'OK'
    END;
    recommendation := CASE 
        WHEN active_connections > 5 THEN '检查慢查询并优化'
        WHEN active_connections > 0 THEN '监控查询性能'
        ELSE '查询性能正常'
    END;
    RETURN NEXT;
    
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 索引维护
-- =====================================================

-- 重建索引函数
CREATE OR REPLACE FUNCTION rebuild_indexes(table_name TEXT DEFAULT NULL)
RETURNS TEXT AS $$
DECLARE
    index_record RECORD;
    result_text TEXT := '';
BEGIN
    IF table_name IS NOT NULL THEN
        -- 重建指定表的索引
        FOR index_record IN 
            SELECT indexname 
            FROM pg_indexes 
            WHERE tablename = table_name AND schemaname = 'public'
        LOOP
            EXECUTE 'REINDEX INDEX ' || index_record.indexname;
            result_text := result_text || '重建索引: ' || index_record.indexname || E'\n';
        END LOOP;
    ELSE
        -- 重建所有索引
        REINDEX DATABASE voicehub;
        result_text := '已重建所有数据库索引';
    END IF;
    
    RETURN result_text;
END;
$$ LANGUAGE plpgsql;

-- 分析表统计信息
CREATE OR REPLACE FUNCTION analyze_tables()
RETURNS TEXT AS $$
DECLARE
    table_record RECORD;
    result_text TEXT := '';
BEGIN
    FOR table_record IN 
        SELECT tablename 
        FROM pg_tables 
        WHERE schemaname = 'public'
    LOOP
        EXECUTE 'ANALYZE ' || table_record.tablename;
        result_text := result_text || '分析表: ' || table_record.tablename || E'\n';
    END LOOP;
    
    RETURN result_text;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 备份相关函数
-- =====================================================

-- 创建逻辑备份信息
CREATE OR REPLACE FUNCTION create_backup_info()
RETURNS TABLE(
    backup_type TEXT,
    command TEXT,
    description TEXT
) AS $$
BEGIN
    -- 完整数据库备份
    backup_type := '完整备份';
    command := 'pg_dump -h localhost -U voicehub -d voicehub -f voicehub_full_' || 
               to_char(CURRENT_TIMESTAMP, 'YYYYMMDD_HH24MISS') || '.sql';
    description := '包含所有数据和结构的完整备份';
    RETURN NEXT;
    
    -- 仅结构备份
    backup_type := '结构备份';
    command := 'pg_dump -h localhost -U voicehub -d voicehub -s -f voicehub_schema_' || 
               to_char(CURRENT_TIMESTAMP, 'YYYYMMDD_HH24MISS') || '.sql';
    description := '仅包含数据库结构，不包含数据';
    RETURN NEXT;
    
    -- 仅数据备份
    backup_type := '数据备份';
    command := 'pg_dump -h localhost -U voicehub -d voicehub -a -f voicehub_data_' || 
               to_char(CURRENT_TIMESTAMP, 'YYYYMMDD_HH24MISS') || '.sql';
    description := '仅包含数据，不包含结构';
    RETURN NEXT;
    
    -- 压缩备份
    backup_type := '压缩备份';
    command := 'pg_dump -h localhost -U voicehub -d voicehub -Fc -f voicehub_compressed_' || 
               to_char(CURRENT_TIMESTAMP, 'YYYYMMDD_HH24MISS') || '.dump';
    description := '使用自定义格式的压缩备份';
    RETURN NEXT;
    
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 监控和报警
-- =====================================================

-- 创建监控报告
CREATE OR REPLACE FUNCTION generate_monitoring_report()
RETURNS TABLE(
    category TEXT,
    metric TEXT,
    current_value TEXT,
    threshold TEXT,
    status TEXT
) AS $$
BEGIN
    -- 用户活跃度监控
    category := '用户活跃度';
    metric := '今日活跃用户';
    SELECT COUNT(DISTINCT user_id)::TEXT INTO current_value
    FROM user_sessions 
    WHERE DATE(created_at) = CURRENT_DATE;
    threshold := '> 10';
    status := CASE 
        WHEN (SELECT COUNT(DISTINCT user_id) FROM user_sessions WHERE DATE(created_at) = CURRENT_DATE) > 10 THEN 'OK'
        ELSE 'LOW'
    END;
    RETURN NEXT;
    
    -- 语音笔记创建监控
    category := '内容创建';
    metric := '今日语音笔记';
    SELECT COUNT(*)::TEXT INTO current_value
    FROM voice_notes 
    WHERE DATE(created_at) = CURRENT_DATE;
    threshold := '> 5';
    status := CASE 
        WHEN (SELECT COUNT(*) FROM voice_notes WHERE DATE(created_at) = CURRENT_DATE) > 5 THEN 'OK'
        ELSE 'LOW'
    END;
    RETURN NEXT;
    
    -- 对话活跃度监控
    category := '对话活跃度';
    metric := '今日对话消息';
    SELECT COUNT(*)::TEXT INTO current_value
    FROM chat_messages 
    WHERE DATE(created_at) = CURRENT_DATE;
    threshold := '> 20';
    status := CASE 
        WHEN (SELECT COUNT(*) FROM chat_messages WHERE DATE(created_at) = CURRENT_DATE) > 20 THEN 'OK'
        ELSE 'LOW'
    END;
    RETURN NEXT;
    
    -- 错误率监控
    category := '系统健康';
    metric := '处理失败的音频文件';
    SELECT COUNT(*)::TEXT INTO current_value
    FROM audio_files 
    WHERE processing_status = 'FAILED'
    AND DATE(created_at) = CURRENT_DATE;
    threshold := '< 5';
    status := CASE 
        WHEN (SELECT COUNT(*) FROM audio_files WHERE processing_status = 'FAILED' AND DATE(created_at) = CURRENT_DATE) < 5 THEN 'OK'
        ELSE 'HIGH'
    END;
    RETURN NEXT;
    
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 定期维护任务调度
-- =====================================================

-- 每日维护任务
CREATE OR REPLACE FUNCTION daily_maintenance()
RETURNS TEXT AS $$
DECLARE
    result_text TEXT := '';
    cleanup_results RECORD;
BEGIN
    result_text := '=== 每日维护任务开始 ===' || E'\n';
    result_text := result_text || '执行时间: ' || CURRENT_TIMESTAMP || E'\n\n';
    
    -- 执行数据清理
    result_text := result_text || '数据清理结果:' || E'\n';
    FOR cleanup_results IN SELECT * FROM comprehensive_cleanup() LOOP
        result_text := result_text || '- ' || cleanup_results.cleanup_type || ': ' || 
                      cleanup_results.deleted_count || ' 条记录' || E'\n';
    END LOOP;
    
    -- 更新统计信息
    PERFORM analyze_tables();
    result_text := result_text || E'\n已更新所有表的统计信息' || E'\n';
    
    -- 刷新物化视图
    REFRESH MATERIALIZED VIEW daily_analytics;
    result_text := result_text || '已刷新物化视图' || E'\n';
    
    -- 更新用户统计缓存
    PERFORM refresh_user_stats_cache();
    result_text := result_text || '已更新用户统计缓存' || E'\n';
    
    result_text := result_text || E'\n=== 每日维护任务完成 ===';
    
    RETURN result_text;
END;
$$ LANGUAGE plpgsql;

-- 每周维护任务
CREATE OR REPLACE FUNCTION weekly_maintenance()
RETURNS TEXT AS $$
DECLARE
    result_text TEXT := '';
BEGIN
    result_text := '=== 每周维护任务开始 ===' || E'\n';
    result_text := result_text || '执行时间: ' || CURRENT_TIMESTAMP || E'\n\n';
    
    -- 执行每日维护
    result_text := result_text || daily_maintenance() || E'\n\n';
    
    -- 数据归档
    result_text := result_text || '数据归档结果:' || E'\n';
    result_text := result_text || '- 语音笔记: ' || archive_old_voice_notes() || ' 条' || E'\n';
    result_text := result_text || '- 对话记录: ' || archive_old_conversations() || ' 条' || E'\n';
    
    -- 重建索引
    result_text := result_text || E'\n索引维护:' || E'\n';
    result_text := result_text || rebuild_indexes() || E'\n';
    
    result_text := result_text || E'\n=== 每周维护任务完成 ===';
    
    RETURN result_text;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 使用示例和说明
-- =====================================================

-- 创建使用说明视图
CREATE VIEW maintenance_commands AS
SELECT 
    '每日维护' as task_type,
    'SELECT daily_maintenance();' as command,
    '执行每日数据清理和统计更新' as description
UNION ALL
SELECT 
    '每周维护' as task_type,
    'SELECT weekly_maintenance();' as command,
    '执行每周完整维护，包括归档和索引重建' as description
UNION ALL
SELECT 
    '健康检查' as task_type,
    'SELECT * FROM check_database_health();' as command,
    '检查数据库健康状态' as description
UNION ALL
SELECT 
    '监控报告' as task_type,
    'SELECT * FROM generate_monitoring_report();' as command,
    '生成系统监控报告' as description
UNION ALL
SELECT 
    '备份信息' as task_type,
    'SELECT * FROM create_backup_info();' as command,
    '获取备份命令信息' as description;

-- =====================================================
-- 完成提示
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '==============================================';
    RAISE NOTICE 'VoiceHub 维护脚本创建完成!';
    RAISE NOTICE '可用的维护命令:';
    RAISE NOTICE '- SELECT daily_maintenance(); -- 每日维护';
    RAISE NOTICE '- SELECT weekly_maintenance(); -- 每周维护';
    RAISE NOTICE '- SELECT * FROM check_database_health(); -- 健康检查';
    RAISE NOTICE '- SELECT * FROM generate_monitoring_report(); -- 监控报告';
    RAISE NOTICE '- SELECT * FROM maintenance_commands; -- 查看所有命令';
    RAISE NOTICE '==============================================';
END $$;