-- =====================================================
-- 逻辑删除字段迁移脚本
-- 为所有继承BaseEntity的表添加deleted字段
-- =====================================================

-- 设置客户端编码
SET client_encoding = 'UTF8';

-- 开始事务
BEGIN;

-- =====================================================
-- 为users表添加deleted字段
-- =====================================================
DO $$
BEGIN
    -- 检查deleted字段是否已存在
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'users' 
        AND column_name = 'deleted'
        AND table_schema = 'public'
    ) THEN
        -- 添加deleted字段
        ALTER TABLE users ADD COLUMN deleted INTEGER DEFAULT 0 NOT NULL;
        
        -- 添加注释
        COMMENT ON COLUMN users.deleted IS '逻辑删除标记：0=未删除，1=已删除';
        
        -- 创建索引以提高查询性能
        CREATE INDEX idx_users_deleted ON users(deleted);
        
        RAISE NOTICE '已为users表添加deleted字段';
    ELSE
        RAISE NOTICE 'users表的deleted字段已存在，跳过';
    END IF;
END $$;

-- =====================================================
-- 为conversations表添加deleted字段
-- =====================================================
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'conversations' 
        AND column_name = 'deleted'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE conversations ADD COLUMN deleted INTEGER DEFAULT 0 NOT NULL;
        COMMENT ON COLUMN conversations.deleted IS '逻辑删除标记：0=未删除，1=已删除';
        CREATE INDEX idx_conversations_deleted ON conversations(deleted);
        RAISE NOTICE '已为conversations表添加deleted字段';
    ELSE
        RAISE NOTICE 'conversations表的deleted字段已存在，跳过';
    END IF;
END $$;

-- =====================================================
-- 为chat_messages表添加deleted字段
-- =====================================================
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'chat_messages' 
        AND column_name = 'deleted'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE chat_messages ADD COLUMN deleted INTEGER DEFAULT 0 NOT NULL;
        COMMENT ON COLUMN chat_messages.deleted IS '逻辑删除标记：0=未删除，1=已删除';
        CREATE INDEX idx_chat_messages_deleted ON chat_messages(deleted);
        RAISE NOTICE '已为chat_messages表添加deleted字段';
    ELSE
        RAISE NOTICE 'chat_messages表的deleted字段已存在，跳过';
    END IF;
END $$;

-- =====================================================
-- 为voice_notes表添加deleted字段
-- =====================================================
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'voice_notes' 
        AND column_name = 'deleted'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE voice_notes ADD COLUMN deleted INTEGER DEFAULT 0 NOT NULL;
        COMMENT ON COLUMN voice_notes.deleted IS '逻辑删除标记：0=未删除，1=已删除';
        CREATE INDEX idx_voice_notes_deleted ON voice_notes(deleted);
        RAISE NOTICE '已为voice_notes表添加deleted字段';
    ELSE
        RAISE NOTICE 'voice_notes表的deleted字段已存在，跳过';
    END IF;
END $$;

-- =====================================================
-- 为schedules表添加deleted字段
-- =====================================================
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'schedules' 
        AND column_name = 'deleted'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE schedules ADD COLUMN deleted INTEGER DEFAULT 0 NOT NULL;
        COMMENT ON COLUMN schedules.deleted IS '逻辑删除标记：0=未删除，1=已删除';
        CREATE INDEX idx_schedules_deleted ON schedules(deleted);
        RAISE NOTICE '已为schedules表添加deleted字段';
    ELSE
        RAISE NOTICE 'schedules表的deleted字段已存在，跳过';
    END IF;
END $$;

-- =====================================================
-- 为mood_entries表添加deleted字段
-- =====================================================
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'mood_entries' 
        AND column_name = 'deleted'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE mood_entries ADD COLUMN deleted INTEGER DEFAULT 0 NOT NULL;
        COMMENT ON COLUMN mood_entries.deleted IS '逻辑删除标记：0=未删除，1=已删除';
        CREATE INDEX idx_mood_entries_deleted ON mood_entries(deleted);
        RAISE NOTICE '已为mood_entries表添加deleted字段';
    ELSE
        RAISE NOTICE 'mood_entries表的deleted字段已存在，跳过';
    END IF;
END $$;

-- =====================================================
-- 为voice_analytics表添加deleted字段
-- =====================================================
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'voice_analytics' 
        AND column_name = 'deleted'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE voice_analytics ADD COLUMN deleted INTEGER DEFAULT 0 NOT NULL;
        COMMENT ON COLUMN voice_analytics.deleted IS '逻辑删除标记：0=未删除，1=已删除';
        CREATE INDEX idx_voice_analytics_deleted ON voice_analytics(deleted);
        RAISE NOTICE '已为voice_analytics表添加deleted字段';
    ELSE
        RAISE NOTICE 'voice_analytics表的deleted字段已存在，跳过';
    END IF;
END $$;

-- =====================================================
-- 创建逻辑删除相关的辅助函数
-- =====================================================

-- 软删除函数：将记录标记为已删除而不是物理删除
CREATE OR REPLACE FUNCTION soft_delete_record(table_name TEXT, record_id BIGINT)
RETURNS BOOLEAN AS $$
DECLARE
    sql_query TEXT;
    affected_rows INTEGER;
BEGIN
    -- 构建动态SQL
    sql_query := format('UPDATE %I SET deleted = 1, updated_at = NOW() WHERE id = %L AND deleted = 0', table_name, record_id);

    -- 执行更新
    EXECUTE sql_query;

    -- 获取影响的行数
    GET DIAGNOSTICS affected_rows = ROW_COUNT;

    -- 返回是否成功
    RETURN affected_rows > 0;
END;
$$ LANGUAGE plpgsql;

-- 恢复软删除记录的函数
CREATE OR REPLACE FUNCTION restore_deleted_record(table_name TEXT, record_id BIGINT)
RETURNS BOOLEAN AS $$
DECLARE
    sql_query TEXT;
    affected_rows INTEGER;
BEGIN
    -- 构建动态SQL
    sql_query := format('UPDATE %I SET deleted = 0, updated_at = NOW() WHERE id = %L AND deleted = 1', table_name, record_id);

    -- 执行更新
    EXECUTE sql_query;

    -- 获取影响的行数
    GET DIAGNOSTICS affected_rows = ROW_COUNT;

    -- 返回是否成功
    RETURN affected_rows > 0;
END;
$$ LANGUAGE plpgsql;

-- 清理已删除记录的函数（物理删除）
CREATE OR REPLACE FUNCTION cleanup_deleted_records(table_name TEXT, days_old INTEGER DEFAULT 30)
RETURNS INTEGER AS $$
DECLARE
    sql_query TEXT;
    affected_rows INTEGER;
BEGIN
    -- 构建动态SQL，删除指定天数前的已删除记录
    sql_query := format(
        'DELETE FROM %I WHERE deleted = 1 AND updated_at < NOW() - INTERVAL ''%s days''',
        table_name,
        days_old
    );

    -- 执行删除
    EXECUTE sql_query;

    -- 获取影响的行数
    GET DIAGNOSTICS affected_rows = ROW_COUNT;

    RAISE NOTICE '从表 % 中物理删除了 % 条记录', table_name, affected_rows;

    -- 返回删除的记录数
    RETURN affected_rows;
END;
$$ LANGUAGE plpgsql;

-- 提交事务
COMMIT;

-- =====================================================
-- 验证迁移结果
-- =====================================================

-- 检查所有表的deleted字段是否已添加
DO $$
DECLARE
    table_record RECORD;
    missing_tables TEXT[] := '{}';
    success_count INTEGER := 0;
    total_count INTEGER := 0;
BEGIN
    -- 检查所有需要deleted字段的表
    FOR table_record IN 
        SELECT table_name 
        FROM unnest(ARRAY['users', 'conversations', 'chat_messages', 'voice_notes', 'schedules', 'mood_entries', 'voice_analytics']) AS table_name
    LOOP
        total_count := total_count + 1;
        
        -- 检查deleted字段是否存在
        IF EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = table_record.table_name 
            AND column_name = 'deleted'
            AND table_schema = 'public'
        ) THEN
            success_count := success_count + 1;
            RAISE NOTICE '✓ 表 % 已成功添加deleted字段', table_record.table_name;
        ELSE
            missing_tables := array_append(missing_tables, table_record.table_name);
            RAISE WARNING '✗ 表 % 缺少deleted字段', table_record.table_name;
        END IF;
    END LOOP;
    
    -- 输出总结
    RAISE NOTICE '==========================================';
    RAISE NOTICE '逻辑删除字段迁移完成';
    RAISE NOTICE '成功: %/%', success_count, total_count;
    
    IF array_length(missing_tables, 1) > 0 THEN
        RAISE WARNING '失败的表: %', array_to_string(missing_tables, ', ');
    ELSE
        RAISE NOTICE '所有表都已成功添加deleted字段！';
    END IF;
    RAISE NOTICE '==========================================';
END $$;
