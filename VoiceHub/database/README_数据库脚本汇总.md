# 🗄️ VoiceHub 智能语音助手平台 - PostgreSQL 数据库脚本汇总

## 📋 脚本概览

本目录包含了 VoiceHub 智能语音助手平台的完整 PostgreSQL 数据库脚本集合，涵盖了从初始化到维护的所有数据库操作。

### 📁 脚本文件列表

| 文件名 | 描述 | 用途 |
|--------|------|------|
| `01_初始化脚本_Initial_Schema.sql` | 数据库初始化脚本 | 创建所有表、索引、触发器和视图 |
| `02_测试数据_Test_Data.sql` | 测试数据脚本 | 插入开发和测试用的示例数据 |
| `03_性能优化_Performance.sql` | 性能优化脚本 | 数据库性能优化配置和查询优化 |
| `04_维护脚本_Maintenance.sql` | 维护脚本 | 日常维护、备份、清理和监控 |
| `05_安全配置_Security.sql` | 安全配置脚本 | 用户权限、数据加密、审计日志 |

---

## 🚀 快速开始

### 1. 数据库初始化

```bash
# 1. 创建数据库
createdb -U postgres voicehub

# 2. 执行初始化脚本
psql -U postgres -d voicehub -f 01_初始化脚本_Initial_Schema.sql

# 3. 插入测试数据（可选）
psql -U postgres -d voicehub -f 02_测试数据_Test_Data.sql

# 4. 应用性能优化
psql -U postgres -d voicehub -f 03_性能优化_Performance.sql

# 5. 配置安全设置
psql -U postgres -d voicehub -f 05_安全配置_Security.sql
```

### 2. 验证安装

```sql
-- 检查表是否创建成功
SELECT count(*) as table_count FROM information_schema.tables 
WHERE table_schema = 'public' AND table_type = 'BASE TABLE';

-- 检查测试数据
SELECT count(*) as user_count FROM users;

-- 检查数据库健康状态
SELECT * FROM check_database_health();
```

---

## 📊 数据库架构详解

### 🏗️ 核心表结构

#### 用户管理
- **users** - 用户主表，存储基本信息和认证数据
- **user_profiles** - 用户详细资料表
- **user_sessions** - 用户会话追踪
- **user_achievements** - 用户成就系统

#### 对话系统
- **conversations** - AI对话会话
- **chat_messages** - 聊天消息记录

#### 语音功能
- **voice_notes** - 语音笔记
- **audio_files** - 音频文件管理
- **voice_analytics** - 语音质量分析

#### 日程管理
- **schedules** - 日程安排

#### 分析统计
- **mood_entries** - 情绪追踪
- **voice_analytics** - 语音分析数据

#### 系统功能
- **notifications** - 通知系统
- **system_settings** - 系统配置
- **audit_log** - 审计日志
- **security_events** - 安全事件

### 📈 数据关系图

```mermaid
erDiagram
    users ||--o{ user_profiles : has
    users ||--o{ conversations : creates
    users ||--o{ voice_notes : records
    users ||--o{ schedules : manages
    users ||--o{ mood_entries : tracks
    users ||--o{ notifications : receives
    users ||--o{ user_achievements : earns
    
    conversations ||--o{ chat_messages : contains
    voice_notes ||--o{ audio_files : references
    users ||--o{ voice_analytics : generates
    users ||--o{ user_sessions : participates
```

---

## ⚡ 性能优化特性

### 🔍 索引策略
- **复合索引** - 针对常用查询组合优化
- **部分索引** - 仅为活跃数据创建索引
- **表达式索引** - 支持函数查询优化
- **全文搜索索引** - 中文全文搜索支持

### 📊 查询优化
- **物化视图** - 预计算复杂统计数据
- **缓存表** - 用户统计信息缓存
- **分区表** - 大数据量场景支持

### 🔧 性能监控
```sql
-- 查看慢查询
SELECT * FROM slow_queries;

-- 查看表大小
SELECT * FROM table_sizes;

-- 查看索引使用情况
SELECT * FROM index_usage;

-- 性能测试
SELECT * FROM performance_test();
```

---

## 🔒 安全特性

### 👥 用户权限管理
- **voicehub_app_role** - 应用程序读写权限
- **voicehub_readonly_role** - 只读权限
- **voicehub_backup_role** - 备份权限

### 🛡️ 数据保护
- **行级安全 (RLS)** - 用户数据隔离
- **数据加密** - 敏感信息加密存储
- **数据脱敏** - 查询结果脱敏显示
- **审计日志** - 完整的操作记录

### 🔐 安全检查
```sql
-- 密码强度检查
SELECT * FROM check_password_strength('your_password');

-- 安全配置检查
SELECT * FROM security_configuration_check();

-- 查看安全事件
SELECT * FROM security_events ORDER BY created_at DESC LIMIT 10;
```

---

## 🛠️ 维护操作

### 📅 日常维护
```sql
-- 每日维护任务
SELECT daily_maintenance();

-- 每周维护任务
SELECT weekly_maintenance();

-- 数据库健康检查
SELECT * FROM check_database_health();

-- 监控报告
SELECT * FROM generate_monitoring_report();
```

### 🧹 数据清理
```sql
-- 综合清理
SELECT * FROM comprehensive_cleanup();

-- 清理过期会话
SELECT cleanup_expired_sessions();

-- 清理过期通知
SELECT cleanup_expired_notifications();

-- 清理孤立文件
SELECT cleanup_orphaned_audio_files();
```

### 📦 数据归档
```sql
-- 归档旧的语音笔记（365天前）
SELECT archive_old_voice_notes(365);

-- 归档旧的对话（180天前）
SELECT archive_old_conversations(180);
```

### 💾 备份操作
```sql
-- 获取备份命令
SELECT * FROM create_backup_info();

-- 生成安全备份脚本
SELECT create_secure_backup_script();
```

---

## 📋 常用查询示例

### 👤 用户相关查询
```sql
-- 查看用户统计
SELECT * FROM user_stats WHERE username = 'testuser1';

-- 查看用户活动摘要
SELECT * FROM user_activity_summary WHERE username = 'testuser1';

-- 查看最近活动
SELECT * FROM recent_activity WHERE user_id = 2 LIMIT 10;
```

### 🎤 语音功能查询
```sql
-- 查看语音质量趋势
SELECT * FROM voice_quality_trends WHERE user_id = 2;

-- 查看热门标签
SELECT * FROM popular_tags;

-- 查看语音笔记统计
SELECT 
    category,
    COUNT(*) as note_count,
    AVG(duration_seconds) as avg_duration,
    AVG(transcription_confidence) as avg_confidence
FROM voice_notes 
WHERE user_id = 2 AND is_archived = false
GROUP BY category;
```

### 📊 分析查询
```sql
-- 查看每日分析数据
SELECT * FROM daily_analytics ORDER BY date DESC LIMIT 7;

-- 查看情绪趋势
SELECT 
    DATE(created_at) as date,
    mood,
    AVG(intensity) as avg_intensity,
    COUNT(*) as entry_count
FROM mood_entries 
WHERE user_id = 2 
AND created_at >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY DATE(created_at), mood
ORDER BY date DESC;
```

---

## 🔧 故障排除

### 常见问题解决

#### 1. 连接问题
```sql
-- 检查活跃连接
SELECT count(*) FROM pg_stat_activity;

-- 查看连接详情
SELECT pid, usename, application_name, client_addr, state 
FROM pg_stat_activity 
WHERE state = 'active';
```

#### 2. 性能问题
```sql
-- 查看慢查询
SELECT query, mean_time, calls FROM pg_stat_statements 
WHERE mean_time > 1000 ORDER BY mean_time DESC;

-- 查看表膨胀
SELECT schemaname, tablename, 
       pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables WHERE schemaname = 'public' 
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
```

#### 3. 锁等待问题
```sql
-- 查看锁等待
SELECT blocked_locks.pid AS blocked_pid,
       blocked_activity.usename AS blocked_user,
       blocking_locks.pid AS blocking_pid,
       blocking_activity.usename AS blocking_user,
       blocked_activity.query AS blocked_statement,
       blocking_activity.query AS current_statement_in_blocking_process
FROM pg_catalog.pg_locks blocked_locks
JOIN pg_catalog.pg_stat_activity blocked_activity ON blocked_activity.pid = blocked_locks.pid
JOIN pg_catalog.pg_locks blocking_locks ON blocking_locks.locktype = blocked_locks.locktype
JOIN pg_catalog.pg_stat_activity blocking_activity ON blocking_activity.pid = blocking_locks.pid
WHERE NOT blocked_locks.granted;
```

---

## 📈 监控指标

### 关键性能指标 (KPI)
- **数据库大小**: 监控数据增长趋势
- **活跃连接数**: 避免连接池耗尽
- **缓存命中率**: 确保 > 95%
- **查询响应时间**: 平均 < 100ms
- **磁盘使用率**: 保持 < 80%

### 业务指标
- **日活跃用户数**: 用户参与度
- **语音笔记创建数**: 功能使用情况
- **对话消息数**: AI交互活跃度
- **语音质量评分**: 服务质量指标

---

## 🔄 版本升级

### 数据库迁移脚本命名规范
```
V{版本号}__{描述}.sql
例如：
V1_1_0__Add_voice_optimization_table.sql
V1_2_0__Update_user_settings_schema.sql
```

### 升级步骤
1. **备份当前数据库**
2. **测试迁移脚本**
3. **执行迁移**
4. **验证数据完整性**
5. **更新应用程序**

---

## 📚 最佳实践

### 🔒 安全最佳实践
1. **定期更换密码** - 每90天更换数据库密码
2. **最小权限原则** - 仅授予必要的权限
3. **定期审计** - 检查用户权限和访问日志
4. **数据加密** - 敏感数据必须加密存储
5. **备份加密** - 备份文件必须加密

### ⚡ 性能最佳实践
1. **定期维护** - 每日执行维护任务
2. **监控慢查询** - 及时优化性能问题
3. **合理分区** - 大表考虑分区策略
4. **索引优化** - 定期检查索引使用情况
5. **统计信息更新** - 保持统计信息最新

### 💾 备份最佳实践
1. **多重备份** - 本地+云端备份
2. **定期测试** - 验证备份可恢复性
3. **版本保留** - 保留多个版本的备份
4. **自动化** - 使用自动化备份脚本
5. **监控告警** - 备份失败及时告警

---

## 📞 技术支持

### 联系方式
- **项目仓库**: https://github.com/your-org/voicehub
- **文档网站**: https://voicehub.docs.com
- **问题反馈**: https://github.com/your-org/voicehub/issues

### 常用命令速查
```bash
# 连接数据库
psql -h localhost -U voicehub -d voicehub

# 执行脚本
psql -h localhost -U voicehub -d voicehub -f script.sql

# 备份数据库
pg_dump -h localhost -U voicehub -d voicehub -f backup.sql

# 恢复数据库
psql -h localhost -U voicehub -d voicehub -f backup.sql

# 查看数据库大小
psql -h localhost -U voicehub -d voicehub -c "SELECT pg_size_pretty(pg_database_size('voicehub'));"
```

---

## 📝 更新日志

### v1.0.0 (2024-01-15)
- ✅ 初始数据库架构设计
- ✅ 完整的表结构和关系
- ✅ 基础索引和触发器
- ✅ 测试数据和示例

### v1.1.0 (计划中)
- 🔄 语音优化表结构
- 🔄 多语言支持增强
- 🔄 性能监控改进
- 🔄 安全策略升级

---

## 🎯 总结

VoiceHub 数据库脚本集合提供了：

✅ **完整的数据库架构** - 13个核心表，覆盖所有业务需求  
✅ **高性能设计** - 优化的索引策略和查询性能  
✅ **企业级安全** - 行级安全、数据加密、审计日志  
✅ **自动化维护** - 完整的维护和监控体系  
✅ **详细文档** - 清晰的使用说明和最佳实践  

这套数据库脚本为 VoiceHub 智能语音助手平台提供了坚实的数据基础，支持高并发、高可用的生产环境部署。

---

<div align="center">

**🎉 VoiceHub 数据库脚本汇总完成！**

**让智能语音助手拥有强大的数据支撑！**

Made with ❤️ by VoiceHub Team

</div>
