#!/bin/bash

# =====================================================
# VoiceHub 智能语音助手平台 - 数据库安装脚本
# 描述: 自动化数据库初始化和配置
# 使用方法: ./setup_database.sh [选项]
# =====================================================

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认配置
DB_HOST="localhost"
DB_PORT="5432"
DB_NAME="voicehub"
DB_USER="voicehub"
DB_PASSWORD=""
POSTGRES_USER="postgres"
INCLUDE_TEST_DATA=false
INCLUDE_PERFORMANCE=true
INCLUDE_SECURITY=true

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
VoiceHub 数据库安装脚本

使用方法:
    $0 [选项]

选项:
    -h, --host HOST         数据库主机地址 (默认: localhost)
    -p, --port PORT         数据库端口 (默认: 5432)
    -d, --database NAME     数据库名称 (默认: voicehub)
    -u, --user USER         数据库用户 (默认: voicehub)
    -w, --password PASS     数据库密码
    --postgres-user USER    PostgreSQL超级用户 (默认: postgres)
    -t, --test-data         包含测试数据
    --no-performance        跳过性能优化
    --no-security          跳过安全配置
    --help                  显示此帮助信息

示例:
    $0 --host localhost --database voicehub --user voicehub --password mypass
    $0 -t --no-security  # 包含测试数据，跳过安全配置
EOF
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--host)
                DB_HOST="$2"
                shift 2
                ;;
            -p|--port)
                DB_PORT="$2"
                shift 2
                ;;
            -d|--database)
                DB_NAME="$2"
                shift 2
                ;;
            -u|--user)
                DB_USER="$2"
                shift 2
                ;;
            -w|--password)
                DB_PASSWORD="$2"
                shift 2
                ;;
            --postgres-user)
                POSTGRES_USER="$2"
                shift 2
                ;;
            -t|--test-data)
                INCLUDE_TEST_DATA=true
                shift
                ;;
            --no-performance)
                INCLUDE_PERFORMANCE=false
                shift
                ;;
            --no-security)
                INCLUDE_SECURITY=false
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v psql &> /dev/null; then
        log_error "psql 未找到，请安装 PostgreSQL 客户端"
        exit 1
    fi
    
    if ! command -v createdb &> /dev/null; then
        log_error "createdb 未找到，请安装 PostgreSQL 客户端工具"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 检查数据库连接
check_connection() {
    log_info "检查数据库连接..."
    
    if ! PGPASSWORD="$POSTGRES_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$POSTGRES_USER" -d postgres -c "SELECT 1;" &> /dev/null; then
        log_error "无法连接到 PostgreSQL 服务器"
        log_error "请检查服务器是否运行，以及连接参数是否正确"
        exit 1
    fi
    
    log_success "数据库连接正常"
}

# 创建数据库和用户
create_database() {
    log_info "创建数据库和用户..."
    
    # 检查数据库是否存在
    if PGPASSWORD="$POSTGRES_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$POSTGRES_USER" -lqt | cut -d \| -f 1 | grep -qw "$DB_NAME"; then
        log_warning "数据库 '$DB_NAME' 已存在"
        read -p "是否要删除并重新创建? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            PGPASSWORD="$POSTGRES_PASSWORD" dropdb -h "$DB_HOST" -p "$DB_PORT" -U "$POSTGRES_USER" "$DB_NAME"
            log_info "已删除现有数据库"
        else
            log_info "使用现有数据库"
        fi
    fi
    
    # 创建数据库
    if ! PGPASSWORD="$POSTGRES_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$POSTGRES_USER" -lqt | cut -d \| -f 1 | grep -qw "$DB_NAME"; then
        PGPASSWORD="$POSTGRES_PASSWORD" createdb -h "$DB_HOST" -p "$DB_PORT" -U "$POSTGRES_USER" "$DB_NAME"
        log_success "数据库 '$DB_NAME' 创建成功"
    fi
    
    # 创建用户
    if [ -n "$DB_PASSWORD" ]; then
        PGPASSWORD="$POSTGRES_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$POSTGRES_USER" -d "$DB_NAME" -c "
            DO \$\$
            BEGIN
                IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = '$DB_USER') THEN
                    CREATE USER $DB_USER WITH PASSWORD '$DB_PASSWORD';
                    GRANT ALL PRIVILEGES ON DATABASE $DB_NAME TO $DB_USER;
                END IF;
            END
            \$\$;
        " &> /dev/null
        log_success "用户 '$DB_USER' 配置完成"
    fi
}

# 执行SQL脚本
execute_sql_script() {
    local script_file="$1"
    local description="$2"
    
    if [ ! -f "$script_file" ]; then
        log_error "脚本文件不存在: $script_file"
        return 1
    fi
    
    log_info "执行 $description..."
    
    local password_option=""
    if [ -n "$DB_PASSWORD" ]; then
        password_option="PGPASSWORD=$DB_PASSWORD"
    else
        password_option="PGPASSWORD=$POSTGRES_PASSWORD"
    fi
    
    local user_option="$DB_USER"
    if [ -z "$DB_PASSWORD" ]; then
        user_option="$POSTGRES_USER"
    fi
    
    if eval "$password_option psql -h $DB_HOST -p $DB_PORT -U $user_option -d $DB_NAME -f $script_file"; then
        log_success "$description 执行成功"
        return 0
    else
        log_error "$description 执行失败"
        return 1
    fi
}

# 验证安装
verify_installation() {
    log_info "验证安装..."
    
    local password_option=""
    local user_option="$DB_USER"
    
    if [ -n "$DB_PASSWORD" ]; then
        password_option="PGPASSWORD=$DB_PASSWORD"
    else
        password_option="PGPASSWORD=$POSTGRES_PASSWORD"
        user_option="$POSTGRES_USER"
    fi
    
    # 检查表数量
    local table_count
    table_count=$(eval "$password_option psql -h $DB_HOST -p $DB_PORT -U $user_option -d $DB_NAME -t -c \"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'public' AND table_type = 'BASE TABLE';\"" | tr -d ' ')
    
    if [ "$table_count" -gt 10 ]; then
        log_success "数据库表创建成功 (共 $table_count 个表)"
    else
        log_error "数据库表创建可能有问题 (仅 $table_count 个表)"
        return 1
    fi
    
    # 检查测试数据
    if [ "$INCLUDE_TEST_DATA" = true ]; then
        local user_count
        user_count=$(eval "$password_option psql -h $DB_HOST -p $DB_PORT -U $user_option -d $DB_NAME -t -c \"SELECT count(*) FROM users;\"" | tr -d ' ')
        
        if [ "$user_count" -gt 0 ]; then
            log_success "测试数据插入成功 (共 $user_count 个用户)"
        else
            log_warning "未找到测试数据"
        fi
    fi
    
    # 检查函数
    local function_count
    function_count=$(eval "$password_option psql -h $DB_HOST -p $DB_PORT -U $user_option -d $DB_NAME -t -c \"SELECT count(*) FROM information_schema.routines WHERE routine_schema = 'public';\"" | tr -d ' ')
    
    if [ "$function_count" -gt 5 ]; then
        log_success "数据库函数创建成功 (共 $function_count 个函数)"
    else
        log_warning "数据库函数可能创建不完整"
    fi
}

# 显示连接信息
show_connection_info() {
    log_info "数据库连接信息:"
    echo "  主机: $DB_HOST"
    echo "  端口: $DB_PORT"
    echo "  数据库: $DB_NAME"
    echo "  用户: $DB_USER"
    echo ""
    echo "连接命令:"
    if [ -n "$DB_PASSWORD" ]; then
        echo "  psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME"
    else
        echo "  psql -h $DB_HOST -p $DB_PORT -U $POSTGRES_USER -d $DB_NAME"
    fi
}

# 主函数
main() {
    echo "========================================"
    echo "VoiceHub 数据库安装脚本"
    echo "========================================"
    echo ""
    
    # 解析参数
    parse_args "$@"
    
    # 如果没有提供密码，提示输入
    if [ -z "$DB_PASSWORD" ] && [ "$DB_USER" != "$POSTGRES_USER" ]; then
        read -s -p "请输入数据库用户 '$DB_USER' 的密码: " DB_PASSWORD
        echo ""
    fi
    
    if [ -z "$POSTGRES_PASSWORD" ]; then
        read -s -p "请输入 PostgreSQL 超级用户 '$POSTGRES_USER' 的密码: " POSTGRES_PASSWORD
        echo ""
    fi
    
    # 执行安装步骤
    check_dependencies
    check_connection
    create_database
    
    # 执行初始化脚本
    execute_sql_script "$SCRIPT_DIR/01_初始化脚本_Initial_Schema.sql" "数据库初始化"
    
    # 执行测试数据脚本
    if [ "$INCLUDE_TEST_DATA" = true ]; then
        execute_sql_script "$SCRIPT_DIR/02_测试数据_Test_Data.sql" "测试数据插入"
    fi
    
    # 执行性能优化脚本
    if [ "$INCLUDE_PERFORMANCE" = true ]; then
        execute_sql_script "$SCRIPT_DIR/03_性能优化_Performance.sql" "性能优化配置"
    fi
    
    # 执行安全配置脚本
    if [ "$INCLUDE_SECURITY" = true ]; then
        execute_sql_script "$SCRIPT_DIR/05_安全配置_Security.sql" "安全配置"
    fi
    
    # 验证安装
    verify_installation
    
    echo ""
    echo "========================================"
    log_success "VoiceHub 数据库安装完成!"
    echo "========================================"
    echo ""
    
    show_connection_info
    
    echo ""
    echo "后续步骤:"
    echo "1. 配置应用程序连接参数"
    echo "2. 运行 'SELECT daily_maintenance();' 进行日常维护"
    echo "3. 查看 README_数据库脚本汇总.md 了解更多信息"
    echo ""
}

# 执行主函数
main "$@"