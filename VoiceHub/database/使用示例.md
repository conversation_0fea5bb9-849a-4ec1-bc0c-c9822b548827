# 🚀 VoiceHub 数据库脚本使用示例

## 📋 快速开始

### 1. 自动化安装（推荐）

```bash
# 进入数据库脚本目录
cd VoiceHub/database

# 基础安装（不包含测试数据）
./setup_database.sh --host localhost --database voicehub --user voicehub --password your_password

# 完整安装（包含测试数据）
./setup_database.sh -t --host localhost --database voicehub --user voicehub --password your_password

# 开发环境安装（包含测试数据，跳过安全配置）
./setup_database.sh -t --no-security
```

### 2. 手动安装

```bash
# 1. 创建数据库
createdb -U postgres voicehub

# 2. 按顺序执行脚本
psql -U postgres -d voicehub -f 01_初始化脚本_Initial_Schema.sql
psql -U postgres -d voicehub -f 02_测试数据_Test_Data.sql
psql -U postgres -d voicehub -f 03_性能优化_Performance.sql
psql -U postgres -d voicehub -f 05_安全配置_Security.sql
```

## 🔧 常用维护命令

### 连接数据库
```bash
psql -h localhost -U voicehub -d voicehub
```

### 日常维护
```sql
-- 每日维护任务
SELECT daily_maintenance();

-- 查看维护结果
=== 每日维护任务开始 ===
执行时间: 2024-01-15 10:30:00

数据清理结果:
- 过期会话: 150 条记录
- 过期通知: 45 条记录
- 过期分析数据: 200 条记录
- 孤立音频文件: 5 条记录
- 空对话: 3 条记录

已更新所有表的统计信息
已刷新物化视图
已更新用户统计缓存

=== 每日维护任务完成 ===
```

### 健康检查
```sql
-- 数据库健康检查
SELECT * FROM check_database_health();

-- 预期结果
 check_name    | status |  value   |    recommendation    
---------------+--------+----------+---------------------
 数据库大小    | OK     | 2.5 GB   | 数据库大小正常
 活跃连接数    | OK     | 12       | 连接数正常
 缓存命中率    | OK     | 96.5%    | 缓存性能良好
 死锁数量      | OK     | 2        | 死锁数量正常
 长时间运行查询| OK     | 0        | 查询性能正常
```

### 性能监控
```sql
-- 查看慢查询
SELECT * FROM slow_queries LIMIT 5;

-- 查看表大小
SELECT * FROM table_sizes LIMIT 10;

-- 性能测试
SELECT * FROM performance_test();
```

## 📊 业务查询示例

### 用户数据查询
```sql
-- 查看用户统计
SELECT * FROM user_stats WHERE username = 'testuser1';

-- 结果示例
 id | username  |        email        |    full_name    |     user_since      |   last_login_at     | total_voice_notes | total_conversations | total_schedules | avg_voice_quality | total_sessions | total_session_time | total_achievements
----+-----------+--------------------+----------------+--------------------+--------------------+------------------+--------------------+----------------+------------------+---------------+-------------------+-------------------
  2 | testuser1 | <EMAIL> | 测试用户一      | 2024-01-15 10:00:00| 2024-01-15 14:30:00|                 2|                   2|               2|              8.65|              2|               2100|                  3
```

### 语音功能查询
```sql
-- 查看用户的语音笔记
SELECT title, category, duration_seconds, transcription_confidence, created_at
FROM voice_notes 
WHERE user_id = 2 AND is_archived = false
ORDER BY created_at DESC;

-- 结果示例
      title       | category  | duration_seconds | transcription_confidence |     created_at      
------------------+-----------+------------------+-------------------------+--------------------
 技术学习笔记     | LEARNING  |               38 |                  0.8900 | 2024-01-15 10:15:00
 今日工作总结     | WORK      |               45 |                  0.9200 | 2024-01-15 10:00:00
```

### 分析数据查询
```sql
-- 查看语音质量趋势
SELECT date, avg_quality, avg_speech_rate, recording_count
FROM voice_quality_trends 
WHERE user_id = 2 
ORDER BY date DESC 
LIMIT 7;

-- 结果示例
    date    | avg_quality | avg_speech_rate | recording_count
------------+-------------+----------------+----------------
 2024-01-15 |        8.65 |            147 |               2
 2024-01-14 |        8.20 |            152 |               1
 2024-01-13 |        8.45 |            149 |               3
```

## 🔒 安全操作示例

### 用户权限管理
```sql
-- 创建只读用户
CREATE USER readonly_user WITH PASSWORD 'readonly_pass';
GRANT voicehub_readonly_role TO readonly_user;

-- 检查用户权限
SELECT r.rolname, r.rolsuper, r.rolinherit, r.rolcreaterole, r.rolcreatedb
FROM pg_roles r 
WHERE r.rolname LIKE '%voicehub%';
```

### 密码安全检查
```sql
-- 检查密码强度
SELECT * FROM check_password_strength('MyPassword123!');

-- 结果示例
 is_strong | score |                    feedback                    
-----------+-------+-----------------------------------------------
 t         |     5 | {}

-- 弱密码示例
SELECT * FROM check_password_strength('123456');

-- 结果示例
 is_strong | score |                           feedback                           
-----------+-------+-------------------------------------------------------------
 f         |     1 | {需要包含大写字母,需要包含小写字母,需要包含特殊字符,不能使用常见弱密码}
```

### 数据脱敏查询
```sql
-- 查看脱敏后的用户数据
SELECT id, username, email, phone_number, full_name 
FROM users_masked 
LIMIT 3;

-- 结果示例
 id | username  |        email         | phone_number |  full_name  
----+-----------+---------------------+--------------+-------------
  1 | admin     | adm***@voicehub.com  | 138****5678  | 系*
  2 | testuser1 | tes***@voicehub.com  | 139****1234  | 测*
  3 | testuser2 | tes***@voicehub.com  | 136****9876  | 测*
```

## 📈 监控和报告

### 系统监控报告
```sql
-- 生成监控报告
SELECT * FROM generate_monitoring_report();

-- 结果示例
   category   |      metric      | current_value | threshold | status 
--------------+------------------+--------------+-----------+--------
 用户活跃度   | 今日活跃用户     | 15           | > 10      | OK
 内容创建     | 今日语音笔记     | 8            | > 5       | OK
 对话活跃度   | 今日对话消息     | 25           | > 20      | OK
 系统健康     | 处理失败的音频文件| 1            | < 5       | OK
```

### 性能报告
```sql
-- 生成性能报告
SELECT * FROM generate_performance_report();

-- 结果示例
  metric_name  | metric_value |  status  |    recommendation    
---------------+--------------+----------+---------------------
 数据库大小    | 2.1 GB       | OK       | 数据库大小正常
 活跃连接数    | 8            | OK       | 连接数正常
 缓存命中率    | 97.2%        | OK       | 缓存命中率良好
 最大表大小    | 156 MB       | INFO     | 监控表增长趋势
```

## 🧹 数据清理示例

### 手动清理操作
```sql
-- 清理过期会话（返回清理数量）
SELECT cleanup_expired_sessions();
-- 结果: 清理了 45 条过期会话记录

-- 清理过期通知
SELECT cleanup_expired_notifications();
-- 结果: 清理了 23 条过期通知

-- 综合清理
SELECT * FROM comprehensive_cleanup();

-- 结果示例
  cleanup_type   | deleted_count 
-----------------+--------------
 过期会话        |            45
 过期通知        |            23
 过期分析数据    |           120
 孤立音频文件    |             3
 空对话          |             2
```

### 数据归档操作
```sql
-- 归档一年前的语音笔记
SELECT archive_old_voice_notes(365);
-- 结果: 已归档 156 条语音笔记

-- 归档6个月前的对话
SELECT archive_old_conversations(180);
-- 结果: 已归档 89 条对话
```

## 💾 备份和恢复示例

### 获取备份命令
```sql
-- 查看备份命令
SELECT * FROM create_backup_info();

-- 结果示例
 backup_type |                           command                            |        description        
-------------+-------------------------------------------------------------+---------------------------
 完整备份    | pg_dump -h localhost -U voicehub -d voicehub -f voicehub_full_20240115_143000.sql | 包含所有数据和结构的完整备份
 结构备份    | pg_dump -h localhost -U voicehub -d voicehub -s -f voicehub_schema_20240115_143000.sql | 仅包含数据库结构，不包含数据
 数据备份    | pg_dump -h localhost -U voicehub -d voicehub -a -f voicehub_data_20240115_143000.sql | 仅包含数据，不包含结构
 压缩备份    | pg_dump -h localhost -U voicehub -d voicehub -Fc -f voicehub_compressed_20240115_143000.dump | 使用自定义格式的压缩备份
```

### 执行备份
```bash
# 完整备份
pg_dump -h localhost -U voicehub -d voicehub -f voicehub_backup_$(date +%Y%m%d_%H%M%S).sql

# 压缩备份
pg_dump -h localhost -U voicehub -d voicehub -Fc -f voicehub_backup_$(date +%Y%m%d_%H%M%S).dump

# 生成安全备份脚本
psql -h localhost -U voicehub -d voicehub -c "SELECT create_secure_backup_script();" -t > backup_script.sh
chmod +x backup_script.sh
./backup_script.sh
```

## 🔍 故障排除示例

### 查看系统状态
```sql
-- 查看当前连接
SELECT pid, usename, application_name, client_addr, state, query_start
FROM pg_stat_activity 
WHERE state = 'active' AND pid != pg_backend_pid()
ORDER BY query_start;

-- 查看锁等待
SELECT 
    blocked_locks.pid AS blocked_pid,
    blocked_activity.usename AS blocked_user,
    blocking_locks.pid AS blocking_pid,
    blocking_activity.usename AS blocking_user,
    blocked_activity.query AS blocked_statement
FROM pg_catalog.pg_locks blocked_locks
JOIN pg_catalog.pg_stat_activity blocked_activity ON blocked_activity.pid = blocked_locks.pid
JOIN pg_catalog.pg_locks blocking_locks ON blocking_locks.locktype = blocked_locks.locktype
JOIN pg_catalog.pg_stat_activity blocking_activity ON blocking_activity.pid = blocking_locks.pid
WHERE NOT blocked_locks.granted;
```

### 性能问题诊断
```sql
-- 查看最慢的查询
SELECT query, mean_time, calls, total_time
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 5;

-- 查看表膨胀情况
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as table_size,
    pg_size_pretty(pg_relation_size(schemaname||'.'||tablename)) as data_size
FROM pg_tables 
WHERE schemaname = 'public' 
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
```

## 📊 业务分析查询

### 用户活跃度分析
```sql
-- 最近7天的用户活跃度
SELECT 
    DATE(created_at) as date,
    COUNT(DISTINCT user_id) as active_users,
    COUNT(*) as total_sessions,
    AVG(duration_seconds) as avg_session_duration
FROM user_sessions 
WHERE created_at >= CURRENT_DATE - INTERVAL '7 days'
GROUP BY DATE(created_at)
ORDER BY date DESC;
```

### 语音功能使用分析
```sql
-- 语音笔记分类统计
SELECT 
    category,
    COUNT(*) as note_count,
    AVG(duration_seconds) as avg_duration,
    AVG(transcription_confidence) as avg_confidence,
    COUNT(DISTINCT user_id) as user_count
FROM voice_notes 
WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'
AND is_archived = false
GROUP BY category
ORDER BY note_count DESC;
```

### 情绪分析统计
```sql
-- 用户情绪趋势
SELECT 
    mood,
    AVG(intensity) as avg_intensity,
    COUNT(*) as entry_count,
    COUNT(DISTINCT user_id) as user_count
FROM mood_entries 
WHERE created_at >= CURRENT_DATE - INTERVAL '7 days'
GROUP BY mood
ORDER BY avg_intensity DESC;
```

## 🔧 开发环境配置

### Docker 环境
```yaml
# docker-compose.yml
version: '3.8'
services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: voicehub
      POSTGRES_USER: voicehub
      POSTGRES_PASSWORD: voicehub123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database:/docker-entrypoint-initdb.d
    command: postgres -c log_statement=all

volumes:
  postgres_data:
```

### 应用程序配置
```yaml
# application.yml
spring:
  datasource:
    url: *****************************************
    username: voicehub
    password: voicehub123
    driver-class-name: org.postgresql.Driver
  
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
```

## 📝 定期维护计划

### 每日任务
```bash
#!/bin/bash
# daily_maintenance.sh

echo "执行每日维护任务..."
psql -h localhost -U voicehub -d voicehub -c "SELECT daily_maintenance();"

echo "检查数据库健康状态..."
psql -h localhost -U voicehub -d voicehub -c "SELECT * FROM check_database_health();"

echo "生成监控报告..."
psql -h localhost -U voicehub -d voicehub -c "SELECT * FROM generate_monitoring_report();"
```

### 每周任务
```bash
#!/bin/bash
# weekly_maintenance.sh

echo "执行每周维护任务..."
psql -h localhost -U voicehub -d voicehub -c "SELECT weekly_maintenance();"

echo "创建数据库备份..."
pg_dump -h localhost -U voicehub -d voicehub -Fc -f "voicehub_weekly_backup_$(date +%Y%m%d).dump"

echo "检查安全配置..."
psql -h localhost -U voicehub -d voicehub -c "SELECT * FROM security_configuration_check();"
```

## 🎯 最佳实践总结

### ✅ 推荐做法
1. **定期维护**: 每天执行 `daily_maintenance()` 函数
2. **监控健康**: 定期检查 `check_database_health()` 结果
3. **备份策略**: 每日增量备份，每周完整备份
4. **性能监控**: 关注慢查询和表大小增长
5. **安全审计**: 定期检查用户权限和访问日志

### ❌ 避免的操作
1. **直接删除数据**: 使用归档而不是直接删除
2. **跳过维护**: 不执行定期维护会导致性能下降
3. **忽略告警**: 及时处理健康检查中的警告
4. **弱密码**: 使用强密码并定期更换
5. **权限过度**: 遵循最小权限原则

---

## 📞 技术支持

如果在使用过程中遇到问题，请：

1. **查看日志**: 检查 PostgreSQL 日志文件
2. **运行诊断**: 执行健康检查和性能报告
3. **查阅文档**: 参考 `README_数据库脚本汇总.md`
4. **联系支持**: 提交 GitHub Issue 或联系技术团队

---

<div align="center">

**🎉 VoiceHub 数据库使用示例完成！**

**祝您使用愉快！**

</div>
