version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:15
    container_name: voicehub-postgres
    environment:
      POSTGRES_DB: voicehub
      POSTGRES_USER: voicehub
      POSTGRES_PASSWORD: voicehub123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - voicehub-network

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: voicehub-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - voicehub-network

  # VoiceHub后端服务
  backend:
    build:
      context: ../backend/voicehub-backend
      dockerfile: Dockerfile
    container_name: voicehub-backend
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - SPRING_DATASOURCE_URL=****************************************
      - SPRING_DATA_REDIS_HOST=redis
    depends_on:
      - postgres
      - redis
    networks:
      - voicehub-network

  # VoiceHub前端应用
  frontend:
    build:
      context: ../frontend/voicehub-frontend
      dockerfile: Dockerfile
    container_name: voicehub-frontend
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_BASE_URL=http://localhost:8080/api
    depends_on:
      - backend
    networks:
      - voicehub-network

volumes:
  postgres_data:
  redis_data:

networks:
  voicehub-network:
    driver: bridge