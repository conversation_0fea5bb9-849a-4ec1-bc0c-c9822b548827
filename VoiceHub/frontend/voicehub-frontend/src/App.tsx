import React, { useState, useEffect } from 'react';
import VoiceRecorder from './components/VoiceRecorder';
import LoginForm from './components/LoginForm';
import RegisterForm from './components/RegisterForm';
import authService from './services/authService';
import './App.css';

function App() {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [currentView, setCurrentView] = useState<'login' | 'register' | 'main'>('login');

  useEffect(() => {
    // Check if user is already authenticated
    const token = localStorage.getItem('voicehub_token');
    if (token) {
      setIsAuthenticated(true);
      setCurrentView('main');
    }
    setIsLoading(false);
  }, []);

  const handleLoginSuccess = () => {
    setIsAuthenticated(true);
    setCurrentView('main');
  };

  const handleLogout = () => {
    authService.logout();
    setIsAuthenticated(false);
    setCurrentView('login');
  };

  const handleRecordingComplete = (audioBlob: Blob) => {
    console.log('Recording completed:', audioBlob);
    // Here you can implement saving to voice notes
  };

  const handleTranscriptionReceived = (text: string) => {
    console.log('Transcription received:', text);
    // Here you can implement saving transcription
  };

  if (isLoading) {
    return (
      <div className="App">
        <div className="loading">
          <h2>Loading VoiceHub...</h2>
        </div>
      </div>
    );
  }

  return (
    <div className="App">
      {!isAuthenticated ? (
        <div className="auth-container">
          <div className="auth-header">
            <h1>🎙️ VoiceHub</h1>
            <p>智能语音笔记平台</p>
          </div>

          {currentView === 'login' ? (
            <div className="auth-form">
              <LoginForm onLoginSuccess={handleLoginSuccess} />
              <p className="auth-switch">
                没有账户？
                <button
                  className="link-button"
                  onClick={() => setCurrentView('register')}
                >
                  立即注册
                </button>
              </p>
            </div>
          ) : (
            <div className="auth-form">
              <RegisterForm onRegisterSuccess={handleLoginSuccess} />
              <p className="auth-switch">
                已有账户？
                <button
                  className="link-button"
                  onClick={() => setCurrentView('login')}
                >
                  立即登录
                </button>
              </p>
            </div>
          )}
        </div>
      ) : (
        <div className="main-container">
          <header className="app-header">
            <div className="header-content">
              <h1>🎙️ VoiceHub</h1>
              <button className="logout-button" onClick={handleLogout}>
                退出登录
              </button>
            </div>
          </header>

          <main className="app-main">
            <div className="voice-recorder-section">
              <h2>语音录制</h2>
              <VoiceRecorder
                onRecordingComplete={handleRecordingComplete}
                onTranscriptionReceived={handleTranscriptionReceived}
                autoTranscribe={true}
                className="main-voice-recorder"
              />
            </div>
          </main>
        </div>
      )}
    </div>
  );
}

export default App;
