import React, { useState, useEffect, useRef } from 'react';
import { Mi<PERSON>, MicOff, Square, Play, Pause, Volume2, FileText, Loader } from 'lucide-react';
import webrtcService, { AudioAnalysisData } from '../services/webrtcService';
import speechService from '../services/speechService';

interface VoiceRecorderProps {
  onRecordingComplete?: (audioBlob: Blob) => void;
  onTranscriptionReceived?: (text: string) => void;
  className?: string;
  disabled?: boolean;
  autoTranscribe?: boolean;
}

const VoiceRecorder: React.FC<VoiceRecorderProps> = ({
  onRecordingComplete,
  onTranscriptionReceived,
  className = '',
  disabled = false,
  autoTranscribe = true
}) => {
  const [isInitialized, setIsInitialized] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [audioAnalysis, setAudioAnalysis] = useState<AudioAnalysisData | null>(null);
  const [error, setError] = useState<string>('');
  const [recordingDuration, setRecordingDuration] = useState(0);
  const [transcription, setTranscription] = useState<string>('');
  const [isTranscribing, setIsTranscribing] = useState(false);
  const [transcriptionError, setTranscriptionError] = useState<string>('');
  
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationFrameRef = useRef<number | null>(null);
  const recordingTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Initialize WebRTC and Speech Service on component mount
  useEffect(() => {
    const initializeServices = async () => {
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia || !window.MediaRecorder) {
        setError('Your browser does not support voice recording');
        return;
      }

      const success = await webrtcService.initialize();
      if (success) {
        setIsInitialized(true);
        
        // Set up audio analysis callback
        webrtcService.setOnAnalysisUpdate((data: AudioAnalysisData) => {
          setAudioAnalysis(data);
        });

        // Initialize speech service
        await speechService.initialize();
      } else {
        setError('Failed to access microphone. Please check permissions.');
      }
    };

    initializeServices();

    return () => {
      webrtcService.cleanup();
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
      if (recordingTimerRef.current) {
        clearInterval(recordingTimerRef.current);
      }
    };
  }, []);

  // Draw audio visualization
  useEffect(() => {
    if (!audioAnalysis || !canvasRef.current) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const { width, height } = canvas;
    ctx.clearRect(0, 0, width, height);

    if (isRecording) {
      // Draw waveform visualization
      const barWidth = width / audioAnalysis.frequency.length;
      
      ctx.fillStyle = audioAnalysis.isVoiceDetected 
        ? 'rgba(59, 130, 246, 0.8)' // Blue when voice detected
        : 'rgba(156, 163, 175, 0.5)'; // Gray when silent

      audioAnalysis.frequency.forEach((value, index) => {
        const barHeight = (value / 255) * height * 0.8;
        const x = index * barWidth;
        const y = height - barHeight;
        
        ctx.fillRect(x, y, barWidth - 1, barHeight);
      });

      // Draw volume indicator
      const volumeHeight = audioAnalysis.volume * height;
      ctx.fillStyle = 'rgba(34, 197, 94, 0.6)';
      ctx.fillRect(width - 20, height - volumeHeight, 15, volumeHeight);
    }
  }, [audioAnalysis, isRecording]);

  const startRecording = async () => {
    if (!isInitialized || disabled) return;

    setError('');
    setAudioBlob(null);
    setRecordingDuration(0);

    const success = await webrtcService.startRecording();
    if (success) {
      setIsRecording(true);
      
      // Start recording timer
      recordingTimerRef.current = setInterval(() => {
        setRecordingDuration(prev => prev + 1);
      }, 1000);
    } else {
      setError('Failed to start recording');
    }
  };

  const stopRecording = async () => {
    if (!isRecording) return;

    const recordedBlob = webrtcService.stopRecording();
    if (recordedBlob) {
      setAudioBlob(recordedBlob);
      if (onRecordingComplete) {
        onRecordingComplete(recordedBlob);
      }

      // Auto-transcribe if enabled
      if (autoTranscribe) {
        await transcribeAudio(recordedBlob);
      }
    }

    setIsRecording(false);
    
    // Clear recording timer
    if (recordingTimerRef.current) {
      clearInterval(recordingTimerRef.current);
    }
  };

  const transcribeAudio = async (audioBlob: Blob) => {
    setIsTranscribing(true);
    setTranscriptionError('');
    setTranscription('');

    try {
      // Convert WebM to WAV for better compatibility
      let processedBlob = audioBlob;
      if (audioBlob.type.includes('webm')) {
        processedBlob = await speechService.convertWebMToWav(audioBlob);
      }

      const result = await speechService.recognizeSpeech({
        audio: processedBlob,
        format: 'wav',
        sampleRate: 16000
      });

      if (result.success && result.text) {
        setTranscription(result.text);
        if (onTranscriptionReceived) {
          onTranscriptionReceived(result.text);
        }
      } else {
        setTranscriptionError(result.error || 'Failed to transcribe audio');
      }
    } catch (error: any) {
      setTranscriptionError(error.message || 'Transcription failed');
    } finally {
      setIsTranscribing(false);
    }
  };

  const manualTranscribe = async () => {
    if (!audioBlob) return;
    await transcribeAudio(audioBlob);
  };

  const playRecording = async () => {
    if (!audioBlob || isPlaying) return;

    setIsPlaying(true);
    try {
      await webrtcService.playAudio(audioBlob);
    } catch (error) {
      console.error('Failed to play audio:', error);
    } finally {
      setIsPlaying(false);
    }
  };

  const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (error) {
    return (
      <div className={`p-4 bg-red-50 border border-red-200 rounded-lg ${className}`}>
        <p className="text-red-600 text-sm">{error}</p>
      </div>
    );
  }

  if (!isInitialized) {
    return (
      <div className={`p-4 bg-gray-50 border border-gray-200 rounded-lg ${className}`}>
        <div className="flex items-center justify-center">
          <div className="w-5 h-5 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mr-2"></div>
          <p className="text-gray-600 text-sm">Initializing microphone...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white/70 backdrop-blur-md rounded-2xl shadow-lg border border-white/20 p-6 ${className}`}>
      {/* Recording Controls */}
      <div className="flex items-center justify-center space-x-4 mb-4">
        {!isRecording ? (
          <button
            onClick={startRecording}
            disabled={disabled}
            className="flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-600 to-cyan-600 text-white rounded-full hover:from-blue-700 hover:to-cyan-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all shadow-lg"
          >
            <Mic className="w-8 h-8" />
          </button>
        ) : (
          <button
            onClick={stopRecording}
            className="flex items-center justify-center w-16 h-16 bg-gradient-to-r from-red-600 to-pink-600 text-white rounded-full hover:from-red-700 hover:to-pink-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-all shadow-lg animate-pulse"
          >
            <Square className="w-8 h-8" />
          </button>
        )}

        {audioBlob && !isRecording && (
          <>
            <button
              onClick={playRecording}
              disabled={isPlaying}
              className="flex items-center justify-center w-12 h-12 bg-green-600 text-white rounded-full hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 transition-all"
            >
              {isPlaying ? <Pause className="w-6 h-6" /> : <Play className="w-6 h-6" />}
            </button>

            {!autoTranscribe && (
              <button
                onClick={manualTranscribe}
                disabled={isTranscribing}
                className="flex items-center justify-center w-12 h-12 bg-purple-600 text-white rounded-full hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 disabled:opacity-50 transition-all"
              >
                {isTranscribing ? <Loader className="w-6 h-6 animate-spin" /> : <FileText className="w-6 h-6" />}
              </button>
            )}
          </>
        )}
      </div>

      {/* Recording Status */}
      <div className="text-center mb-4">
        {isRecording ? (
          <div className="flex items-center justify-center space-x-2">
            <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
            <span className="text-red-600 font-medium">Recording: {formatDuration(recordingDuration)}</span>
          </div>
        ) : audioBlob ? (
          <div className="flex items-center justify-center space-x-2">
            <Volume2 className="w-4 h-4 text-green-600" />
            <span className="text-green-600 font-medium">Recording ready</span>
          </div>
        ) : (
          <span className="text-gray-500">Click to start recording</span>
        )}
      </div>

      {/* Audio Visualization */}
      <div className="bg-gray-900/10 rounded-lg p-4 mb-4">
        <canvas
          ref={canvasRef}
          width={300}
          height={100}
          className="w-full h-20 rounded"
        />
      </div>

      {/* Voice Activity Indicator */}
      {isRecording && audioAnalysis && (
        <div className="flex items-center justify-between text-sm mb-4">
          <div className="flex items-center space-x-2">
            <div className={`w-2 h-2 rounded-full ${
              audioAnalysis.isVoiceDetected ? 'bg-green-500' : 'bg-gray-400'
            }`}></div>
            <span className="text-gray-600">
              {audioAnalysis.isVoiceDetected ? 'Voice detected' : 'Listening...'}
            </span>
          </div>
          <div className="flex items-center space-x-1">
            <span className="text-gray-500">Volume:</span>
            <div className="w-16 h-2 bg-gray-200 rounded-full overflow-hidden">
              <div 
                className="h-full bg-blue-500 transition-all duration-100"
                style={{ width: `${audioAnalysis.volume * 100}%` }}
              ></div>
            </div>
          </div>
        </div>
      )}

      {/* Transcription Section */}
      {(transcription || isTranscribing || transcriptionError) && (
        <div className="mt-4 p-4 bg-gray-50/50 rounded-lg border border-gray-200/50">
          <div className="flex items-center space-x-2 mb-2">
            <FileText className="w-4 h-4 text-blue-600" />
            <span className="text-sm font-medium text-gray-700">Transcription</span>
            {isTranscribing && <Loader className="w-4 h-4 animate-spin text-blue-600" />}
          </div>
          
          {isTranscribing && (
            <div className="text-sm text-gray-500">
              Converting speech to text...
            </div>
          )}
          
          {transcription && (
            <div className="text-sm text-gray-800 bg-white p-3 rounded border">
              {transcription}
            </div>
          )}
          
          {transcriptionError && (
            <div className="text-sm text-red-600 bg-red-50 p-3 rounded border border-red-200">
              {transcriptionError}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default VoiceRecorder;