import api from './api';

// 认证相关的接口类型定义
export interface LoginRequest {
  usernameOrEmail: string;
  password: string;
}

export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
  fullName?: string;
  phoneNumber?: string;
}

export interface AuthResponse {
  token: string;
  type: string;
  user: {
    id: number;
    username: string;
    email: string;
    fullName?: string;
    avatarUrl?: string;
    phoneNumber?: string;
    role: string;
  };
}

// 认证服务类
class AuthService {
  private readonly TOKEN_KEY = 'voicehub_token';
  private readonly USER_KEY = 'voicehub_user';

  /**
   * 用户登录
   */
  async login(loginData: LoginRequest): Promise<AuthResponse> {
    try {
      const response = await api.post('/auth/login', loginData);
      const authResponse: AuthResponse = response.data;
      
      // 保存token和用户信息到本地存储
      this.setToken(authResponse.token);
      this.setUser(authResponse.user);
      
      return authResponse;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '登录失败');
    }
  }

  /**
   * 用户注册
   */
  async register(registerData: RegisterRequest): Promise<AuthResponse> {
    try {
      const response = await api.post('/auth/register', registerData);
      const authResponse: AuthResponse = response.data;
      
      // 保存token和用户信息到本地存储
      this.setToken(authResponse.token);
      this.setUser(authResponse.user);
      
      return authResponse;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '注册失败');
    }
  }

  /**
   * 用户登出
   */
  logout(): void {
    localStorage.removeItem(this.TOKEN_KEY);
    localStorage.removeItem(this.USER_KEY);
  }

  /**
   * 检查用户名是否可用
   */
  async checkUsername(username: string): Promise<boolean> {
    try {
      const response = await api.get(`/auth/check-username?username=${username}`);
      return response.data.available;
    } catch (error) {
      return false;
    }
  }

  /**
   * 检查邮箱是否可用
   */
  async checkEmail(email: string): Promise<boolean> {
    try {
      const response = await api.get(`/auth/check-email?email=${email}`);
      return response.data.available;
    } catch (error) {
      return false;
    }
  }

  /**
   * 获取当前用户信息
   */
  getCurrentUser() {
    const userStr = localStorage.getItem(this.USER_KEY);
    return userStr ? JSON.parse(userStr) : null;
  }

  /**
   * 获取token
   */
  getToken(): string | null {
    return localStorage.getItem(this.TOKEN_KEY);
  }

  /**
   * 检查是否已登录
   */
  isAuthenticated(): boolean {
    const token = this.getToken();
    return !!token;
  }

  /**
   * 设置token
   */
  private setToken(token: string): void {
    localStorage.setItem(this.TOKEN_KEY, token);
  }

  /**
   * 设置用户信息
   */
  private setUser(user: any): void {
    localStorage.setItem(this.USER_KEY, JSON.stringify(user));
  }
}

export default new AuthService();