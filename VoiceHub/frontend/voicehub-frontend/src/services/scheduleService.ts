import api from './api';

// Schedule related interfaces
export interface Schedule {
  id?: number;
  title: string;
  description?: string;
  startTime: string; // ISO datetime string
  endTime?: string;
  location?: string;
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  status: 'SCHEDULED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED' | 'POSTPONED';
  isAllDay?: boolean;
  reminderMinutes?: number;
  voiceCommand?: string;
  createdByVoice?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export interface ScheduleStats {
  totalSchedules: number;
  scheduledCount: number;
  completedCount: number;
  cancelledCount: number;
  todaySchedules: number;
  upcomingSchedules: number;
  overdueSchedules: number;
}

export interface CreateScheduleFromVoiceRequest {
  voiceCommand: string;
}

export interface ScheduleResponse {
  success: boolean;
  message?: string;
  data?: Schedule | Schedule[] | ScheduleStats;
  error?: string;
}

export interface PaginatedScheduleResponse {
  success: boolean;
  data: Schedule[];
  totalElements: number;
  totalPages: number;
  currentPage: number;
}

// Schedule Service Class
class ScheduleService {
  private readonly BASE_URL = '/api/schedules';

  /**
   * Create schedule from voice command
   */
  async createScheduleFromVoice(voiceCommand: string): Promise<Schedule> {
    try {
      const response = await api.post<ScheduleResponse>(`${this.BASE_URL}/voice`, {
        voiceCommand
      });

      if (response.data.success && response.data.data) {
        return response.data.data as Schedule;
      } else {
        throw new Error(response.data.error || 'Failed to create schedule from voice');
      }
    } catch (error: any) {
      console.error('Failed to create schedule from voice:', error);
      throw new Error(error.response?.data?.error || error.message || 'Failed to create schedule from voice');
    }
  }

  /**
   * Create schedule manually
   */
  async createSchedule(schedule: Omit<Schedule, 'id' | 'createdAt' | 'updatedAt'>): Promise<Schedule> {
    try {
      const response = await api.post<ScheduleResponse>(this.BASE_URL, schedule);

      if (response.data.success && response.data.data) {
        return response.data.data as Schedule;
      } else {
        throw new Error(response.data.error || 'Failed to create schedule');
      }
    } catch (error: any) {
      console.error('Failed to create schedule:', error);
      throw new Error(error.response?.data?.error || error.message || 'Failed to create schedule');
    }
  }

  /**
   * Get user schedules with pagination
   */
  async getSchedules(page: number = 0, size: number = 20): Promise<PaginatedScheduleResponse> {
    try {
      const response = await api.get<PaginatedScheduleResponse>(`${this.BASE_URL}?page=${page}&size=${size}`);
      return response.data;
    } catch (error: any) {
      console.error('Failed to get schedules:', error);
      throw new Error(error.response?.data?.error || error.message || 'Failed to get schedules');
    }
  }

  /**
   * Get today's schedules
   */
  async getTodaySchedules(): Promise<Schedule[]> {
    try {
      const response = await api.get<ScheduleResponse>(`${this.BASE_URL}/today`);
      
      if (response.data.success && response.data.data) {
        return response.data.data as Schedule[];
      } else {
        throw new Error(response.data.error || 'Failed to get today\'s schedules');
      }
    } catch (error: any) {
      console.error('Failed to get today\'s schedules:', error);
      throw new Error(error.response?.data?.error || error.message || 'Failed to get today\'s schedules');
    }
  }

  /**
   * Get upcoming schedules
   */
  async getUpcomingSchedules(days: number = 7): Promise<Schedule[]> {
    try {
      const response = await api.get<ScheduleResponse>(`${this.BASE_URL}/upcoming?days=${days}`);
      
      if (response.data.success && response.data.data) {
        return response.data.data as Schedule[];
      } else {
        throw new Error(response.data.error || 'Failed to get upcoming schedules');
      }
    } catch (error: any) {
      console.error('Failed to get upcoming schedules:', error);
      throw new Error(error.response?.data?.error || error.message || 'Failed to get upcoming schedules');
    }
  }

  /**
   * Get schedules by time range
   */
  async getSchedulesByRange(startTime: Date, endTime: Date): Promise<Schedule[]> {
    try {
      const params = new URLSearchParams({
        startTime: startTime.toISOString(),
        endTime: endTime.toISOString()
      });

      const response = await api.get<ScheduleResponse>(`${this.BASE_URL}/range?${params}`);
      
      if (response.data.success && response.data.data) {
        return response.data.data as Schedule[];
      } else {
        throw new Error(response.data.error || 'Failed to get schedules by range');
      }
    } catch (error: any) {
      console.error('Failed to get schedules by range:', error);
      throw new Error(error.response?.data?.error || error.message || 'Failed to get schedules by range');
    }
  }

  /**
   * Get weekly schedules
   */
  async getWeeklySchedules(): Promise<Schedule[]> {
    try {
      const response = await api.get<ScheduleResponse>(`${this.BASE_URL}/weekly`);
      
      if (response.data.success && response.data.data) {
        return response.data.data as Schedule[];
      } else {
        throw new Error(response.data.error || 'Failed to get weekly schedules');
      }
    } catch (error: any) {
      console.error('Failed to get weekly schedules:', error);
      throw new Error(error.response?.data?.error || error.message || 'Failed to get weekly schedules');
    }
  }

  /**
   * Get monthly schedules
   */
  async getMonthlySchedules(): Promise<Schedule[]> {
    try {
      const response = await api.get<ScheduleResponse>(`${this.BASE_URL}/monthly`);
      
      if (response.data.success && response.data.data) {
        return response.data.data as Schedule[];
      } else {
        throw new Error(response.data.error || 'Failed to get monthly schedules');
      }
    } catch (error: any) {
      console.error('Failed to get monthly schedules:', error);
      throw new Error(error.response?.data?.error || error.message || 'Failed to get monthly schedules');
    }
  }

  /**
   * Search schedules
   */
  async searchSchedules(keyword: string): Promise<Schedule[]> {
    try {
      const response = await api.get<ScheduleResponse>(`${this.BASE_URL}/search?keyword=${encodeURIComponent(keyword)}`);
      
      if (response.data.success && response.data.data) {
        return response.data.data as Schedule[];
      } else {
        throw new Error(response.data.error || 'Failed to search schedules');
      }
    } catch (error: any) {
      console.error('Failed to search schedules:', error);
      throw new Error(error.response?.data?.error || error.message || 'Failed to search schedules');
    }
  }

  /**
   * Update schedule
   */
  async updateSchedule(id: number, schedule: Partial<Schedule>): Promise<Schedule> {
    try {
      const response = await api.put<ScheduleResponse>(`${this.BASE_URL}/${id}`, schedule);

      if (response.data.success && response.data.data) {
        return response.data.data as Schedule;
      } else {
        throw new Error(response.data.error || 'Failed to update schedule');
      }
    } catch (error: any) {
      console.error('Failed to update schedule:', error);
      throw new Error(error.response?.data?.error || error.message || 'Failed to update schedule');
    }
  }

  /**
   * Delete schedule
   */
  async deleteSchedule(id: number): Promise<void> {
    try {
      const response = await api.delete<ScheduleResponse>(`${this.BASE_URL}/${id}`);

      if (!response.data.success) {
        throw new Error(response.data.error || 'Failed to delete schedule');
      }
    } catch (error: any) {
      console.error('Failed to delete schedule:', error);
      throw new Error(error.response?.data?.error || error.message || 'Failed to delete schedule');
    }
  }

  /**
   * Complete schedule
   */
  async completeSchedule(id: number): Promise<Schedule> {
    try {
      const response = await api.put<ScheduleResponse>(`${this.BASE_URL}/${id}/complete`);

      if (response.data.success && response.data.data) {
        return response.data.data as Schedule;
      } else {
        throw new Error(response.data.error || 'Failed to complete schedule');
      }
    } catch (error: any) {
      console.error('Failed to complete schedule:', error);
      throw new Error(error.response?.data?.error || error.message || 'Failed to complete schedule');
    }
  }

  /**
   * Cancel schedule
   */
  async cancelSchedule(id: number): Promise<Schedule> {
    try {
      const response = await api.put<ScheduleResponse>(`${this.BASE_URL}/${id}/cancel`);

      if (response.data.success && response.data.data) {
        return response.data.data as Schedule;
      } else {
        throw new Error(response.data.error || 'Failed to cancel schedule');
      }
    } catch (error: any) {
      console.error('Failed to cancel schedule:', error);
      throw new Error(error.response?.data?.error || error.message || 'Failed to cancel schedule');
    }
  }

  /**
   * Postpone schedule
   */
  async postponeSchedule(id: number, minutes: number): Promise<Schedule> {
    try {
      const response = await api.put<ScheduleResponse>(`${this.BASE_URL}/${id}/postpone?minutes=${minutes}`);

      if (response.data.success && response.data.data) {
        return response.data.data as Schedule;
      } else {
        throw new Error(response.data.error || 'Failed to postpone schedule');
      }
    } catch (error: any) {
      console.error('Failed to postpone schedule:', error);
      throw new Error(error.response?.data?.error || error.message || 'Failed to postpone schedule');
    }
  }

  /**
   * Get schedule statistics
   */
  async getScheduleStats(): Promise<ScheduleStats> {
    try {
      const response = await api.get<ScheduleResponse>(`${this.BASE_URL}/stats`);
      
      if (response.data.success && response.data.data) {
        return response.data.data as ScheduleStats;
      } else {
        throw new Error(response.data.error || 'Failed to get schedule stats');
      }
    } catch (error: any) {
      console.error('Failed to get schedule stats:', error);
      throw new Error(error.response?.data?.error || error.message || 'Failed to get schedule stats');
    }
  }

  /**
   * Format date for display
   */
  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  /**
   * Format time for display
   */
  formatTime(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  /**
   * Get priority color
   */
  getPriorityColor(priority: Schedule['priority']): string {
    switch (priority) {
      case 'URGENT':
        return 'text-red-600 bg-red-100';
      case 'HIGH':
        return 'text-orange-600 bg-orange-100';
      case 'MEDIUM':
        return 'text-blue-600 bg-blue-100';
      case 'LOW':
        return 'text-gray-600 bg-gray-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  }

  /**
   * Get status color
   */
  getStatusColor(status: Schedule['status']): string {
    switch (status) {
      case 'SCHEDULED':
        return 'text-blue-600 bg-blue-100';
      case 'IN_PROGRESS':
        return 'text-yellow-600 bg-yellow-100';
      case 'COMPLETED':
        return 'text-green-600 bg-green-100';
      case 'CANCELLED':
        return 'text-red-600 bg-red-100';
      case 'POSTPONED':
        return 'text-purple-600 bg-purple-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  }
}

export default new ScheduleService();