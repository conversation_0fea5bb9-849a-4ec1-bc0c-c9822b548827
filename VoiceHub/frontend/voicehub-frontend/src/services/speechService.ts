import api from './api';

// 语音识别相关的接口类型定义
export interface SpeechRecognitionRequest {
  audio: Blob;
  format?: string;
  sampleRate?: number;
}

export interface SpeechRecognitionResponse {
  success: boolean;
  text: string;
  confidence?: number;
  language?: string;
  duration?: number;
  error?: string;
}

export interface SupportedFormatsResponse {
  supportedFormats: string[];
  recommendedSampleRates: { [key: string]: number };
  maxFileSize: string;
}

export interface ServiceStatusResponse {
  status: string;
  service: string;
  version: string;
  supportedLanguages: string[];
}

// 语音识别服务类
class SpeechService {
  private readonly MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
  private supportedFormats: string[] = [];
  private recommendedSampleRates: { [key: string]: number } = {};

  /**
   * 初始化服务，获取支持的格式信息
   */
  async initialize(): Promise<void> {
    try {
      const response = await api.get<SupportedFormatsResponse>('/speech/formats');
      this.supportedFormats = response.data.supportedFormats;
      this.recommendedSampleRates = response.data.recommendedSampleRates;
      console.log('Speech service initialized successfully');
    } catch (error) {
      console.error('Failed to initialize speech service:', error);
    }
  }

  /**
   * 语音转文本
   */
  async recognizeSpeech(request: SpeechRecognitionRequest): Promise<SpeechRecognitionResponse> {
    try {
      // 验证音频文件大小
      if (request.audio.size > this.MAX_FILE_SIZE) {
        throw new Error(`Audio file size exceeds ${this.MAX_FILE_SIZE / 1024 / 1024}MB limit`);
      }

      // 准备表单数据
      const formData = new FormData();
      formData.append('audio', request.audio);
      
      if (request.format) {
        formData.append('format', request.format);
      }
      
      if (request.sampleRate) {
        formData.append('rate', request.sampleRate.toString());
      }

      // 发送请求
      const response = await api.post<SpeechRecognitionResponse>('/speech/recognize', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        timeout: 30000, // 30秒超时
      });

      return response.data;
    } catch (error: any) {
      console.error('Speech recognition failed:', error);
      
      return {
        success: false,
        text: '',
        error: error.response?.data?.error || error.message || 'Speech recognition failed'
      };
    }
  }

  /**
   * 获取服务状态
   */
  async getServiceStatus(): Promise<ServiceStatusResponse | null> {
    try {
      const response = await api.get<ServiceStatusResponse>('/speech/status');
      return response.data;
    } catch (error) {
      console.error('Failed to get service status:', error);
      return null;
    }
  }

  /**
   * 检查音频格式是否支持
   */
  isSupportedFormat(format: string): boolean {
    return this.supportedFormats.includes(format.toLowerCase());
  }

  /**
   * 获取推荐的采样率
   */
  getRecommendedSampleRate(format: string): number {
    return this.recommendedSampleRates[format.toLowerCase()] || 16000;
  }

  /**
   * 获取支持的格式列表
   */
  getSupportedFormats(): string[] {
    return [...this.supportedFormats];
  }

  /**
   * 将WebM音频转换为WAV格式（用于更好的兼容性）
   */
  async convertWebMToWav(webmBlob: Blob): Promise<Blob> {
    return new Promise((resolve, reject) => {
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      const fileReader = new FileReader();

      fileReader.onload = async (e) => {
        try {
          const arrayBuffer = e.target?.result as ArrayBuffer;
          const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);
          
          // Convert to WAV
          const wavBlob = this.audioBufferToWav(audioBuffer);
          resolve(wavBlob);
        } catch (error) {
          reject(error);
        }
      };

      fileReader.onerror = reject;
      fileReader.readAsArrayBuffer(webmBlob);
    });
  }

  /**
   * 将AudioBuffer转换为WAV格式
   */
  private audioBufferToWav(audioBuffer: AudioBuffer): Blob {
    const numberOfChannels = audioBuffer.numberOfChannels;
    const sampleRate = audioBuffer.sampleRate;
    const format = 1; // PCM
    const bitDepth = 16;

    const bytesPerSample = bitDepth / 8;
    const blockAlign = numberOfChannels * bytesPerSample;
    const byteRate = sampleRate * blockAlign;
    const dataSize = audioBuffer.length * blockAlign;
    const bufferSize = 44 + dataSize;

    const buffer = new ArrayBuffer(bufferSize);
    const view = new DataView(buffer);

    // WAV header
    const writeString = (offset: number, string: string) => {
      for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i));
      }
    };

    writeString(0, 'RIFF');
    view.setUint32(4, bufferSize - 8, true);
    writeString(8, 'WAVE');
    writeString(12, 'fmt ');
    view.setUint32(16, 16, true);
    view.setUint16(20, format, true);
    view.setUint16(22, numberOfChannels, true);
    view.setUint32(24, sampleRate, true);
    view.setUint32(28, byteRate, true);
    view.setUint16(32, blockAlign, true);
    view.setUint16(34, bitDepth, true);
    writeString(36, 'data');
    view.setUint32(40, dataSize, true);

    // Convert audio data
    let offset = 44;
    for (let i = 0; i < audioBuffer.length; i++) {
      for (let channel = 0; channel < numberOfChannels; channel++) {
        const sample = Math.max(-1, Math.min(1, audioBuffer.getChannelData(channel)[i]));
        const intSample = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;
        view.setInt16(offset, intSample, true);
        offset += 2;
      }
    }

    return new Blob([buffer], { type: 'audio/wav' });
  }

  /**
   * 实时语音识别（WebSocket）
   */
  async startRealtimeRecognition(onResult: (text: string) => void, onError: (error: string) => void): Promise<void> {
    // TODO: Implement WebSocket-based real-time speech recognition
    console.log('Real-time speech recognition not implemented yet');
  }
}

export default new SpeechService();