import api from './api';

// TTS related interfaces
export interface TTSVoice {
  value: number;
  name: string;
  gender: 'male' | 'female';
}

export interface TTSFormat {
  value: number;
  extension: string;
  mimeType: string;
}

export interface TTSRequest {
  text: string;
  voice?: string;
  format?: string;
  speed?: number; // 0-15, 5 is normal
  pitch?: number; // 0-15, 5 is normal
  volume?: number; // 0-15, 5 is normal
  language?: string; // 'zh' for Chinese, 'en' for English
}

export interface TTSResponse {
  success: boolean;
  message?: string;
  audioData?: string; // Base64 encoded audio
  filePath?: string;
  fileSize?: number;
  duration?: number;
  format?: string;
  voice?: string;
  textLength?: number;
  error?: string;
}

export interface TTSServiceStatus {
  success: boolean;
  message: string;
  status: 'connected' | 'disconnected';
}

// Available voices enum
export const TTS_VOICES = {
  FEMALE_STANDARD: 'FEMALE_STANDARD',
  MALE_STANDARD: 'MALE_STANDARD',
  FEMALE_EMOTIONAL: 'FEMALE_EMOTIONAL',
  MALE_EMOTIONAL: 'MALE_EMOTIONAL',
  FEMALE_SWEET: 'FEMALE_SWEET',
  MALE_MAGNETIC: 'MALE_MAGNETIC',
  FEMALE_WARM: 'FEMALE_WARM',
  MALE_DEEP: 'MALE_DEEP'
} as const;

export type TTSVoiceType = typeof TTS_VOICES[keyof typeof TTS_VOICES];

// Available formats enum
export const TTS_FORMATS = {
  MP3: 'MP3',
  PCM: 'PCM',
  WAV: 'WAV',
  AMR: 'AMR'
} as const;

export type TTSFormatType = typeof TTS_FORMATS[keyof typeof TTS_FORMATS];

// Voice descriptions for UI
export const VOICE_DESCRIPTIONS = {
  [TTS_VOICES.FEMALE_STANDARD]: { name: '度小美', gender: 'female', description: 'Standard female voice' },
  [TTS_VOICES.MALE_STANDARD]: { name: '度小宇', gender: 'male', description: 'Standard male voice' },
  [TTS_VOICES.FEMALE_EMOTIONAL]: { name: '度逍遥', gender: 'female', description: 'Emotional female voice' },
  [TTS_VOICES.MALE_EMOTIONAL]: { name: '度丫丫', gender: 'male', description: 'Emotional male voice' },
  [TTS_VOICES.FEMALE_SWEET]: { name: '度小娇', gender: 'female', description: 'Sweet female voice' },
  [TTS_VOICES.MALE_MAGNETIC]: { name: '度米朵', gender: 'male', description: 'Magnetic male voice' },
  [TTS_VOICES.FEMALE_WARM]: { name: '度博文', gender: 'female', description: 'Warm female voice' },
  [TTS_VOICES.MALE_DEEP]: { name: '度小童', gender: 'male', description: 'Deep male voice' }
};

// TTS Service Class
class TTSService {
  private readonly BASE_URL = '/api/tts';
  private audioContext: AudioContext | null = null;
  private currentAudio: HTMLAudioElement | null = null;

  /**
   * Initialize audio context
   */
  private initAudioContext(): AudioContext {
    if (!this.audioContext) {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
    }
    return this.audioContext;
  }

  /**
   * Synthesize speech from text
   */
  async synthesizeSpeech(request: TTSRequest): Promise<Blob> {
    try {
      const response = await api.post(`${this.BASE_URL}/synthesize`, request, {
        responseType: 'blob',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.data instanceof Blob) {
        return response.data;
      } else {
        throw new Error('Invalid response format');
      }
    } catch (error: any) {
      console.error('Failed to synthesize speech:', error);
      throw new Error(error.response?.data?.error || error.message || 'Failed to synthesize speech');
    }
  }

  /**
   * Synthesize speech and return as Base64
   */
  async synthesizeSpeechBase64(request: TTSRequest): Promise<TTSResponse> {
    try {
      const response = await api.post<TTSResponse>(`${this.BASE_URL}/synthesize-base64`, request);
      return response.data;
    } catch (error: any) {
      console.error('Failed to synthesize speech as Base64:', error);
      throw new Error(error.response?.data?.error || error.message || 'Failed to synthesize speech');
    }
  }

  /**
   * Synthesize speech and save as file
   */
  async synthesizeAndSaveFile(request: TTSRequest): Promise<TTSResponse> {
    try {
      const response = await api.post<TTSResponse>(`${this.BASE_URL}/synthesize-file`, request);
      return response.data;
    } catch (error: any) {
      console.error('Failed to synthesize and save speech file:', error);
      throw new Error(error.response?.data?.error || error.message || 'Failed to synthesize and save speech');
    }
  }

  /**
   * Quick text-to-speech with default settings
   */
  async speak(text: string, voice: TTSVoiceType = TTS_VOICES.FEMALE_STANDARD): Promise<void> {
    try {
      const audioBlob = await this.synthesizeSpeech({ text, voice });
      await this.playAudio(audioBlob);
    } catch (error: any) {
      console.error('Failed to speak text:', error);
      throw error;
    }
  }

  /**
   * Quick text-to-speech with custom settings
   */
  async speakWithSettings(
    text: string,
    voice: TTSVoiceType = TTS_VOICES.FEMALE_STANDARD,
    speed: number = 5,
    pitch: number = 5,
    volume: number = 5
  ): Promise<void> {
    try {
      const audioBlob = await this.synthesizeSpeech({
        text,
        voice,
        speed,
        pitch,
        volume
      });
      await this.playAudio(audioBlob);
    } catch (error: any) {
      console.error('Failed to speak text with settings:', error);
      throw error;
    }
  }

  /**
   * Play audio blob
   */
  async playAudio(audioBlob: Blob): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        // Stop current audio if playing
        this.stopAudio();

        const audioUrl = URL.createObjectURL(audioBlob);
        this.currentAudio = new Audio(audioUrl);

        this.currentAudio.onended = () => {
          URL.revokeObjectURL(audioUrl);
          this.currentAudio = null;
          resolve();
        };

        this.currentAudio.onerror = (error) => {
          URL.revokeObjectURL(audioUrl);
          this.currentAudio = null;
          reject(new Error('Failed to play audio'));
        };

        this.currentAudio.play().catch(reject);
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Stop current audio playback
   */
  stopAudio(): void {
    if (this.currentAudio) {
      this.currentAudio.pause();
      this.currentAudio.currentTime = 0;
      this.currentAudio = null;
    }
  }

  /**
   * Check if audio is currently playing
   */
  isPlaying(): boolean {
    return this.currentAudio !== null && !this.currentAudio.paused;
  }

  /**
   * Convert Base64 audio to Blob
   */
  base64ToBlob(base64Data: string, mimeType: string = 'audio/mp3'): Blob {
    const byteCharacters = atob(base64Data);
    const byteNumbers = new Array(byteCharacters.length);
    
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    
    const byteArray = new Uint8Array(byteNumbers);
    return new Blob([byteArray], { type: mimeType });
  }

  /**
   * Download audio as file
   */
  downloadAudio(audioBlob: Blob, filename: string = 'speech.mp3'): void {
    const url = URL.createObjectURL(audioBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }

  /**
   * Get available voices
   */
  async getAvailableVoices(): Promise<TTSVoice[]> {
    try {
      const response = await api.get<{ success: boolean; voices: TTSVoice[] }>(`${this.BASE_URL}/voices`);
      return response.data.voices;
    } catch (error: any) {
      console.error('Failed to get available voices:', error);
      throw new Error(error.response?.data?.error || error.message || 'Failed to get available voices');
    }
  }

  /**
   * Get available audio formats
   */
  async getAvailableFormats(): Promise<TTSFormat[]> {
    try {
      const response = await api.get<{ success: boolean; formats: TTSFormat[] }>(`${this.BASE_URL}/formats`);
      return response.data.formats;
    } catch (error: any) {
      console.error('Failed to get available formats:', error);
      throw new Error(error.response?.data?.error || error.message || 'Failed to get available formats');
    }
  }

  /**
   * Test TTS service connectivity
   */
  async testService(): Promise<TTSServiceStatus> {
    try {
      const response = await api.get<TTSServiceStatus>(`${this.BASE_URL}/test`);
      return response.data;
    } catch (error: any) {
      console.error('Failed to test TTS service:', error);
      return {
        success: false,
        message: error.response?.data?.error || error.message || 'Service test failed',
        status: 'disconnected'
      };
    }
  }

  /**
   * Validate text for TTS
   */
  validateText(text: string): { valid: boolean; error?: string } {
    if (!text || text.trim().length === 0) {
      return { valid: false, error: 'Text cannot be empty' };
    }

    if (text.length > 1024) {
      return { valid: false, error: 'Text length cannot exceed 1024 characters' };
    }

    return { valid: true };
  }

  /**
   * Estimate speech duration
   */
  estimateDuration(text: string, speed: number = 5): number {
    // Rough estimation: Chinese characters ~0.5s each, English words ~0.3s each
    // Speed factor: 0-15 scale, 5 is normal (1.0x), 0 is slowest (0.5x), 15 is fastest (2.0x)
    const speedFactor = 0.5 + (speed / 15.0) * 1.5; // 0.5x to 2.0x

    const chineseChars = text.replace(/[^\u4e00-\u9fa5]/g, '').length;
    const englishWords = text.replace(/[\u4e00-\u9fa5]/g, '').split(/\s+/).length;

    const baseDuration = (chineseChars * 0.5) + (englishWords * 0.3);
    return baseDuration / speedFactor;
  }

  /**
   * Get voice description
   */
  getVoiceDescription(voice: TTSVoiceType): { name: string; gender: string; description: string } {
    return VOICE_DESCRIPTIONS[voice] || VOICE_DESCRIPTIONS[TTS_VOICES.FEMALE_STANDARD];
  }

  /**
   * Get recommended voice based on content
   */
  getRecommendedVoice(text: string, preferredGender?: 'male' | 'female'): TTSVoiceType {
    // Simple logic to recommend voice based on content and preference
    const isEmotional = /[!?。！？]/.test(text) || text.length > 100;
    
    if (preferredGender === 'male') {
      return isEmotional ? TTS_VOICES.MALE_EMOTIONAL : TTS_VOICES.MALE_STANDARD;
    } else {
      return isEmotional ? TTS_VOICES.FEMALE_EMOTIONAL : TTS_VOICES.FEMALE_STANDARD;
    }
  }

  /**
   * Batch synthesize multiple texts
   */
  async batchSynthesize(texts: string[], voice: TTSVoiceType = TTS_VOICES.FEMALE_STANDARD): Promise<Blob[]> {
    const results: Blob[] = [];
    
    for (const text of texts) {
      try {
        const audioBlob = await this.synthesizeSpeech({ text, voice });
        results.push(audioBlob);
      } catch (error) {
        console.error(`Failed to synthesize text: ${text}`, error);
        // Create empty blob for failed synthesis
        results.push(new Blob());
      }
    }
    
    return results;
  }

  /**
   * Create audio visualization data from audio blob
   */
  async createVisualizationData(audioBlob: Blob): Promise<number[]> {
    try {
      const audioContext = this.initAudioContext();
      const arrayBuffer = await audioBlob.arrayBuffer();
      const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);
      
      const channelData = audioBuffer.getChannelData(0);
      const samples = 128; // Number of visualization bars
      const blockSize = Math.floor(channelData.length / samples);
      const visualizationData: number[] = [];
      
      for (let i = 0; i < samples; i++) {
        let sum = 0;
        for (let j = 0; j < blockSize; j++) {
          sum += Math.abs(channelData[i * blockSize + j]);
        }
        visualizationData.push(sum / blockSize);
      }
      
      return visualizationData;
    } catch (error) {
      console.error('Failed to create visualization data:', error);
      return new Array(128).fill(0);
    }
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    this.stopAudio();
    if (this.audioContext) {
      this.audioContext.close();
      this.audioContext = null;
    }
  }
}

// Create and export singleton instance
const ttsService = new TTSService();
export default ttsService;

// Export utility functions
export const ttsUtils = {
  /**
   * Format duration in seconds to readable string
   */
  formatDuration: (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  },

  /**
   * Format file size in bytes to readable string
   */
  formatFileSize: (bytes: number): string => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  },

  /**
   * Detect text language
   */
  detectLanguage: (text: string): 'zh' | 'en' => {
    const chineseChars = text.replace(/[^\u4e00-\u9fa5]/g, '').length;
    const totalChars = text.replace(/\s/g, '').length;
    return chineseChars / totalChars > 0.3 ? 'zh' : 'en';
  },

  /**
   * Split long text into chunks for TTS
   */
  splitTextForTTS: (text: string, maxLength: number = 1000): string[] => {
    if (text.length <= maxLength) return [text];
    
    const chunks: string[] = [];
    const sentences = text.split(/[。！？.!?]/);
    let currentChunk = '';
    
    for (const sentence of sentences) {
      if (currentChunk.length + sentence.length <= maxLength) {
        currentChunk += sentence;
      } else {
        if (currentChunk) chunks.push(currentChunk.trim());
        currentChunk = sentence;
      }
    }
    
    if (currentChunk) chunks.push(currentChunk.trim());
    return chunks.filter(chunk => chunk.length > 0);
  }
};