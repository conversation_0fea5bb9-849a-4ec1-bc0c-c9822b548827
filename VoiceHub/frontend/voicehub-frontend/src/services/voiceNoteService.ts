import api from './api';

// Voice Note related interfaces
export interface VoiceNote {
  id?: number;
  title: string;
  description?: string;
  transcription?: string;
  audioFilePath?: string;
  audioFileName?: string;
  audioFileSize?: number;
  audioDurationSeconds?: number;
  audioFormat?: string;
  category: 'GENERAL' | 'MEETING' | 'IDEA' | 'REMINDER' | 'PERSONAL' | 'WORK' | 'STUDY' | 'TRAVEL' | 'HEALTH' | 'OTHER';
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  isFavorite?: boolean;
  isArchived?: boolean;
  transcriptionConfidence?: number;
  languageDetected?: string;
  tags?: string[];
  createdAt?: string;
  updatedAt?: string;
}

export interface VoiceNoteStats {
  totalNotes: number;
  favoriteNotes: number;
  archivedNotes: number;
  totalDurationSeconds: number;
  totalFileSizeBytes: number;
  todayNotes: number;
  thisWeekNotes: number;
  thisMonthNotes: number;
  formattedTotalDuration?: string;
  formattedTotalFileSize?: string;
}

export interface CreateVoiceNoteRequest {
  title?: string;
  description?: string;
  category?: VoiceNote['category'];
  priority?: VoiceNote['priority'];
  tags?: string[];
}

export interface CreateVoiceNoteFromAudioRequest {
  title?: string;
  audioData: string; // Base64 encoded
  audioFormat?: string;
  category?: VoiceNote['category'];
}

export interface VoiceNoteResponse {
  success: boolean;
  message?: string;
  data?: VoiceNote | VoiceNote[] | VoiceNoteStats | string[];
  error?: string;
}

export interface PaginatedVoiceNoteResponse {
  success: boolean;
  data: VoiceNote[];
  totalElements: number;
  totalPages: number;
  currentPage: number;
}

// Voice Note Service Class
class VoiceNoteService {
  private readonly BASE_URL = '/api/voice-notes';

  /**
   * Create voice note with audio file
   */
  async createVoiceNote(audioFile: File, request: CreateVoiceNoteRequest = {}): Promise<VoiceNote> {
    try {
      const formData = new FormData();
      formData.append('audio', audioFile);
      
      if (request.title) formData.append('title', request.title);
      if (request.description) formData.append('description', request.description);
      if (request.category) formData.append('category', request.category);
      if (request.priority) formData.append('priority', request.priority);
      if (request.tags) {
        request.tags.forEach(tag => formData.append('tags', tag));
      }

      const response = await api.post<VoiceNoteResponse>(this.BASE_URL, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      if (response.data.success && response.data.data) {
        return response.data.data as VoiceNote;
      } else {
        throw new Error(response.data.error || 'Failed to create voice note');
      }
    } catch (error: any) {
      console.error('Failed to create voice note:', error);
      throw new Error(error.response?.data?.error || error.message || 'Failed to create voice note');
    }
  }

  /**
   * Create voice note from audio data (for WebRTC integration)
   */
  async createVoiceNoteFromAudio(request: CreateVoiceNoteFromAudioRequest): Promise<VoiceNote> {
    try {
      const response = await api.post<VoiceNoteResponse>(`${this.BASE_URL}/from-audio`, request);

      if (response.data.success && response.data.data) {
        return response.data.data as VoiceNote;
      } else {
        throw new Error(response.data.error || 'Failed to create voice note from audio');
      }
    } catch (error: any) {
      console.error('Failed to create voice note from audio:', error);
      throw new Error(error.response?.data?.error || error.message || 'Failed to create voice note from audio');
    }
  }

  /**
   * Get voice notes with pagination
   */
  async getVoiceNotes(page: number = 0, size: number = 20): Promise<PaginatedVoiceNoteResponse> {
    try {
      const response = await api.get<PaginatedVoiceNoteResponse>(`${this.BASE_URL}?page=${page}&size=${size}`);
      return response.data;
    } catch (error: any) {
      console.error('Failed to get voice notes:', error);
      throw new Error(error.response?.data?.error || error.message || 'Failed to get voice notes');
    }
  }

  /**
   * Get voice note by ID
   */
  async getVoiceNote(id: number): Promise<VoiceNote> {
    try {
      const response = await api.get<VoiceNoteResponse>(`${this.BASE_URL}/${id}`);
      
      if (response.data.success && response.data.data) {
        return response.data.data as VoiceNote;
      } else {
        throw new Error(response.data.error || 'Failed to get voice note');
      }
    } catch (error: any) {
      console.error('Failed to get voice note:', error);
      throw new Error(error.response?.data?.error || error.message || 'Failed to get voice note');
    }
  }

  /**
   * Update voice note
   */
  async updateVoiceNote(id: number, updates: Partial<VoiceNote>): Promise<VoiceNote> {
    try {
      const response = await api.put<VoiceNoteResponse>(`${this.BASE_URL}/${id}`, updates);

      if (response.data.success && response.data.data) {
        return response.data.data as VoiceNote;
      } else {
        throw new Error(response.data.error || 'Failed to update voice note');
      }
    } catch (error: any) {
      console.error('Failed to update voice note:', error);
      throw new Error(error.response?.data?.error || error.message || 'Failed to update voice note');
    }
  }

  /**
   * Delete voice note
   */
  async deleteVoiceNote(id: number): Promise<void> {
    try {
      const response = await api.delete<VoiceNoteResponse>(`${this.BASE_URL}/${id}`);

      if (!response.data.success) {
        throw new Error(response.data.error || 'Failed to delete voice note');
      }
    } catch (error: any) {
      console.error('Failed to delete voice note:', error);
      throw new Error(error.response?.data?.error || error.message || 'Failed to delete voice note');
    }
  }

  /**
   * Search voice notes
   */
  async searchVoiceNotes(keyword: string): Promise<VoiceNote[]> {
    try {
      const response = await api.get<VoiceNoteResponse>(`${this.BASE_URL}/search?keyword=${encodeURIComponent(keyword)}`);
      
      if (response.data.success && response.data.data) {
        return response.data.data as VoiceNote[];
      } else {
        throw new Error(response.data.error || 'Failed to search voice notes');
      }
    } catch (error: any) {
      console.error('Failed to search voice notes:', error);
      throw new Error(error.response?.data?.error || error.message || 'Failed to search voice notes');
    }
  }

  /**
   * Get voice notes by category
   */
  async getVoiceNotesByCategory(category: VoiceNote['category']): Promise<VoiceNote[]> {
    try {
      const response = await api.get<VoiceNoteResponse>(`${this.BASE_URL}/category/${category}`);
      
      if (response.data.success && response.data.data) {
        return response.data.data as VoiceNote[];
      } else {
        throw new Error(response.data.error || 'Failed to get voice notes by category');
      }
    } catch (error: any) {
      console.error('Failed to get voice notes by category:', error);
      throw new Error(error.response?.data?.error || error.message || 'Failed to get voice notes by category');
    }
  }

  /**
   * Get favorite voice notes
   */
  async getFavoriteVoiceNotes(): Promise<VoiceNote[]> {
    try {
      const response = await api.get<VoiceNoteResponse>(`${this.BASE_URL}/favorites`);
      
      if (response.data.success && response.data.data) {
        return response.data.data as VoiceNote[];
      } else {
        throw new Error(response.data.error || 'Failed to get favorite voice notes');
      }
    } catch (error: any) {
      console.error('Failed to get favorite voice notes:', error);
      throw new Error(error.response?.data?.error || error.message || 'Failed to get favorite voice notes');
    }
  }

  /**
   * Get archived voice notes
   */
  async getArchivedVoiceNotes(): Promise<VoiceNote[]> {
    try {
      const response = await api.get<VoiceNoteResponse>(`${this.BASE_URL}/archived`);
      
      if (response.data.success && response.data.data) {
        return response.data.data as VoiceNote[];
      } else {
        throw new Error(response.data.error || 'Failed to get archived voice notes');
      }
    } catch (error: any) {
      console.error('Failed to get archived voice notes:', error);
      throw new Error(error.response?.data?.error || error.message || 'Failed to get archived voice notes');
    }
  }

  /**
   * Get voice notes by tag
   */
  async getVoiceNotesByTag(tag: string): Promise<VoiceNote[]> {
    try {
      const response = await api.get<VoiceNoteResponse>(`${this.BASE_URL}/tag/${encodeURIComponent(tag)}`);
      
      if (response.data.success && response.data.data) {
        return response.data.data as VoiceNote[];
      } else {
        throw new Error(response.data.error || 'Failed to get voice notes by tag');
      }
    } catch (error: any) {
      console.error('Failed to get voice notes by tag:', error);
      throw new Error(error.response?.data?.error || error.message || 'Failed to get voice notes by tag');
    }
  }

  /**
   * Get recent voice notes
   */
  async getRecentVoiceNotes(days: number = 7): Promise<VoiceNote[]> {
    try {
      const response = await api.get<VoiceNoteResponse>(`${this.BASE_URL}/recent?days=${days}`);
      
      if (response.data.success && response.data.data) {
        return response.data.data as VoiceNote[];
      } else {
        throw new Error(response.data.error || 'Failed to get recent voice notes');
      }
    } catch (error: any) {
      console.error('Failed to get recent voice notes:', error);
      throw new Error(error.response?.data?.error || error.message || 'Failed to get recent voice notes');
    }
  }

  /**
   * Toggle favorite status
   */
  async toggleFavorite(id: number): Promise<VoiceNote> {
    try {
      const response = await api.put<VoiceNoteResponse>(`${this.BASE_URL}/${id}/favorite`);

      if (response.data.success && response.data.data) {
        return response.data.data as VoiceNote;
      } else {
        throw new Error(response.data.error || 'Failed to toggle favorite');
      }
    } catch (error: any) {
      console.error('Failed to toggle favorite:', error);
      throw new Error(error.response?.data?.error || error.message || 'Failed to toggle favorite');
    }
  }

  /**
   * Toggle archive status
   */
  async toggleArchive(id: number): Promise<VoiceNote> {
    try {
      const response = await api.put<VoiceNoteResponse>(`${this.BASE_URL}/${id}/archive`);

      if (response.data.success && response.data.data) {
        return response.data.data as VoiceNote;
      } else {
        throw new Error(response.data.error || 'Failed to toggle archive');
      }
    } catch (error: any) {
      console.error('Failed to toggle archive:', error);
      throw new Error(error.response?.data?.error || error.message || 'Failed to toggle archive');
    }
  }

  /**
   * Get voice note statistics
   */
  async getVoiceNoteStats(): Promise<VoiceNoteStats> {
    try {
      const response = await api.get<VoiceNoteResponse>(`${this.BASE_URL}/stats`);
      
      if (response.data.success && response.data.data) {
        return response.data.data as VoiceNoteStats;
      } else {
        throw new Error(response.data.error || 'Failed to get voice note stats');
      }
    } catch (error: any) {
      console.error('Failed to get voice note stats:', error);
      throw new Error(error.response?.data?.error || error.message || 'Failed to get voice note stats');
    }
  }

  /**
   * Get user tags
   */
  async getUserTags(): Promise<string[]> {
    try {
      const response = await api.get<VoiceNoteResponse>(`${this.BASE_URL}/tags`);
      
      if (response.data.success && response.data.data) {
        return response.data.data as string[];
      } else {
        throw new Error(response.data.error || 'Failed to get user tags');
      }
    } catch (error: any) {
      console.error('Failed to get user tags:', error);
      throw new Error(error.response?.data?.error || error.message || 'Failed to get user tags');
    }
  }

  /**
   * Download audio file
   */
  async downloadAudioFile(id: number): Promise<Blob> {
    try {
      const response = await api.get(`${this.BASE_URL}/${id}/audio`, {
        responseType: 'blob',
      });

      return response.data;
    } catch (error: any) {
      console.error('Failed to download audio file:', error);
      throw new Error(error.response?.data?.error || error.message || 'Failed to download audio file');
    }
  }

  /**
   * Get audio file URL for playback
   */
  getAudioFileUrl(id: number): string {
    return `${api.defaults.baseURL}${this.BASE_URL}/${id}/audio`;
  }

  /**
   * Convert audio blob to base64 for API calls
   */
  async audioToBase64(audioBlob: Blob): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        // Remove data:audio/webm;base64, prefix
        const base64 = result.split(',')[1];
        resolve(base64);
      };
      reader.onerror = reject;
      reader.readAsDataURL(audioBlob);
    });
  }

  /**
   * Format duration for display
   */
  formatDuration(seconds: number): string {
    if (!seconds || seconds === 0) return '0:00';
    
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    } else {
      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
  }

  /**
   * Format file size for display
   */
  formatFileSize(bytes: number): string {
    if (!bytes || bytes === 0) return '0 B';
    
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
  }

  /**
   * Get category color for UI
   */
  getCategoryColor(category: VoiceNote['category']): string {
    const colors = {
      GENERAL: 'bg-gray-100 text-gray-800',
      MEETING: 'bg-blue-100 text-blue-800',
      IDEA: 'bg-yellow-100 text-yellow-800',
      REMINDER: 'bg-red-100 text-red-800',
      PERSONAL: 'bg-green-100 text-green-800',
      WORK: 'bg-purple-100 text-purple-800',
      STUDY: 'bg-indigo-100 text-indigo-800',
      TRAVEL: 'bg-pink-100 text-pink-800',
      HEALTH: 'bg-teal-100 text-teal-800',
      OTHER: 'bg-gray-100 text-gray-800',
    };
    
    return colors[category] || colors.GENERAL;
  }

  /**
   * Get priority color for UI
   */
  getPriorityColor(priority: VoiceNote['priority']): string {
    const colors = {
      LOW: 'bg-gray-100 text-gray-600',
      MEDIUM: 'bg-blue-100 text-blue-600',
      HIGH: 'bg-orange-100 text-orange-600',
      URGENT: 'bg-red-100 text-red-600',
    };
    
    return colors[priority] || colors.MEDIUM;
  }

  /**
   * Get category icon
   */
  getCategoryIcon(category: VoiceNote['category']): string {
    const icons = {
      GENERAL: '📝',
      MEETING: '🤝',
      IDEA: '💡',
      REMINDER: '⏰',
      PERSONAL: '👤',
      WORK: '💼',
      STUDY: '📚',
      TRAVEL: '✈️',
      HEALTH: '🏥',
      OTHER: '📋',
    };
    
    return icons[category] || icons.GENERAL;
  }

  /**
   * Validate audio file before upload
   */
  validateAudioFile(file: File): { valid: boolean; error?: string } {
    const maxSize = 10 * 1024 * 1024; // 10MB
    const allowedTypes = ['audio/wav', 'audio/mp3', 'audio/webm', 'audio/ogg', 'audio/m4a'];
    
    if (file.size > maxSize) {
      return { valid: false, error: 'File size exceeds 10MB limit' };
    }
    
    if (!allowedTypes.includes(file.type)) {
      return { valid: false, error: 'Unsupported audio format' };
    }
    
    return { valid: true };
  }

  /**
   * Generate default title based on current time
   */
  generateDefaultTitle(): string {
    const now = new Date();
    const timeStr = now.toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: false 
    });
    const dateStr = now.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric' 
    });
    
    return `Voice Note ${dateStr} ${timeStr}`;
  }

  /**
   * Extract keywords from transcription for auto-tagging
   */
  extractKeywords(transcription: string): string[] {
    if (!transcription) return [];
    
    const commonWords = ['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them'];
    
    const words = transcription
      .toLowerCase()
      .replace(/[^\w\s]/g, '')
      .split(/\s+/)
      .filter(word => word.length > 3 && !commonWords.includes(word));
    
    // Get unique words and return top 5 most frequent
    const wordCount = words.reduce((acc, word) => {
      acc[word] = (acc[word] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    return Object.entries(wordCount)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([word]) => word);
  }
}

export default new VoiceNoteService();