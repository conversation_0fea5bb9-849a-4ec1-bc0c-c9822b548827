// WebRTC Service for Voice Communication
export interface VoiceRecordingOptions {
  sampleRate?: number;
  channels?: number;
  bitRate?: number;
  format?: string;
}

export interface AudioAnalysisData {
  volume: number;
  frequency: number[];
  isVoiceDetected: boolean;
}

class WebRTCService {
  private mediaRecorder: MediaRecorder | null = null;
  private audioStream: MediaStream | null = null;
  private audioContext: AudioContext | null = null;
  private analyser: AnalyserNode | null = null;
  private dataArray: Uint8Array | null = null;
  private isRecording = false;
  private recordedChunks: Blob[] = [];
  private onDataAvailable?: (data: Blob) => void;
  private onAnalysisUpdate?: (data: AudioAnalysisData) => void;

  /**
   * Initialize WebRTC and request microphone access
   */
  async initialize(): Promise<boolean> {
    try {
      // Request microphone permission
      this.audioStream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 44100,
          channelCount: 1
        }
      });

      // Initialize Audio Context for analysis
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      this.analyser = this.audioContext.createAnalyser();
      
      const source = this.audioContext.createMediaStreamSource(this.audioStream);
      source.connect(this.analyser);
      
      this.analyser.fftSize = 256;
      const bufferLength = this.analyser.frequencyBinCount;
      this.dataArray = new Uint8Array(bufferLength);

      console.log('WebRTC initialized successfully');
      return true;
    } catch (error) {
      console.error('Failed to initialize WebRTC:', error);
      return false;
    }
  }

  /**
   * Start voice recording
   */
  async startRecording(options: VoiceRecordingOptions = {}): Promise<boolean> {
    if (!this.audioStream) {
      console.error('Audio stream not initialized');
      return false;
    }

    try {
      this.recordedChunks = [];
      
      // Configure MediaRecorder
      const mimeType = this.getSupportedMimeType();
      this.mediaRecorder = new MediaRecorder(this.audioStream, {
        mimeType,
        audioBitsPerSecond: options.bitRate || 128000
      });

      // Set up event handlers
      this.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          this.recordedChunks.push(event.data);
          if (this.onDataAvailable) {
            this.onDataAvailable(event.data);
          }
        }
      };

      this.mediaRecorder.onstop = () => {
        console.log('Recording stopped');
      };

      // Start recording
      this.mediaRecorder.start(100); // Collect data every 100ms
      this.isRecording = true;
      
      // Start audio analysis
      this.startAudioAnalysis();
      
      console.log('Voice recording started');
      return true;
    } catch (error) {
      console.error('Failed to start recording:', error);
      return false;
    }
  }

  /**
   * Stop voice recording
   */
  stopRecording(): Blob | null {
    if (!this.mediaRecorder || !this.isRecording) {
      return null;
    }

    this.mediaRecorder.stop();
    this.isRecording = false;
    
    // Stop audio analysis
    this.stopAudioAnalysis();

    // Create final blob
    if (this.recordedChunks.length > 0) {
      const mimeType = this.getSupportedMimeType();
      const recordedBlob = new Blob(this.recordedChunks, { type: mimeType });
      console.log('Recording completed, size:', recordedBlob.size);
      return recordedBlob;
    }

    return null;
  }

  /**
   * Start real-time audio analysis
   */
  private startAudioAnalysis(): void {
    if (!this.analyser || !this.dataArray) return;

    const analyze = () => {
      if (!this.isRecording) return;

      this.analyser!.getByteFrequencyData(this.dataArray!);
      
      // Calculate volume (RMS)
      let sum = 0;
      for (let i = 0; i < this.dataArray!.length; i++) {
        sum += this.dataArray![i] * this.dataArray![i];
      }
      const volume = Math.sqrt(sum / this.dataArray!.length) / 255;

      // Voice activity detection (simple threshold-based)
      const isVoiceDetected = volume > 0.01;

      // Frequency analysis
      const frequency = Array.from(this.dataArray!);

      const analysisData: AudioAnalysisData = {
        volume,
        frequency,
        isVoiceDetected
      };

      if (this.onAnalysisUpdate) {
        this.onAnalysisUpdate(analysisData);
      }

      requestAnimationFrame(analyze);
    };

    analyze();
  }

  /**
   * Stop audio analysis
   */
  private stopAudioAnalysis(): void {
    // Analysis will stop automatically when isRecording becomes false
  }

  /**
   * Get supported MIME type for recording
   */
  private getSupportedMimeType(): string {
    const types = [
      'audio/webm;codecs=opus',
      'audio/webm',
      'audio/mp4',
      'audio/ogg;codecs=opus',
      'audio/wav'
    ];

    for (const type of types) {
      if (MediaRecorder.isTypeSupported(type)) {
        return type;
      }
    }

    return 'audio/webm'; // fallback
  }

  /**
   * Convert audio blob to base64
   */
  async blobToBase64(blob: Blob): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        resolve(result.split(',')[1]); // Remove data:audio/webm;base64, prefix
      };
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  }

  /**
   * Convert audio blob to ArrayBuffer
   */
  async blobToArrayBuffer(blob: Blob): Promise<ArrayBuffer> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as ArrayBuffer);
      reader.onerror = reject;
      reader.readAsArrayBuffer(blob);
    });
  }

  /**
   * Play audio from blob
   */
  async playAudio(blob: Blob): Promise<void> {
    const audio = new Audio();
    const url = URL.createObjectURL(blob);
    
    return new Promise((resolve, reject) => {
      audio.onended = () => {
        URL.revokeObjectURL(url);
        resolve();
      };
      audio.onerror = reject;
      audio.src = url;
      audio.play();
    });
  }

  /**
   * Set callback for real-time data
   */
  setOnDataAvailable(callback: (data: Blob) => void): void {
    this.onDataAvailable = callback;
  }

  /**
   * Set callback for audio analysis updates
   */
  setOnAnalysisUpdate(callback: (data: AudioAnalysisData) => void): void {
    this.onAnalysisUpdate = callback;
  }

  /**
   * Check if currently recording
   */
  getIsRecording(): boolean {
    return this.isRecording;
  }

  /**
   * Get current audio stream
   */
  getAudioStream(): MediaStream | null {
    return this.audioStream;
  }

  /**
   * Clean up resources
   */
  cleanup(): void {
    if (this.mediaRecorder && this.isRecording) {
      this.stopRecording();
    }

    if (this.audioStream) {
      this.audioStream.getTracks().forEach(track => track.stop());
      this.audioStream = null;
    }

    if (this.audioContext) {
      this.audioContext.close();
      this.audioContext = null;
    }

    this.analyser = null;
    this.dataArray = null;
    this.recordedChunks = [];
  }

  /**
   * Check browser compatibility
   */
  static isSupported(): boolean {
    return !!(
      navigator.mediaDevices &&
      typeof navigator.mediaDevices.getUserMedia === 'function' &&
      window.MediaRecorder &&
      (window.AudioContext || (window as any).webkitAudioContext)
    );
  }
}

export default new WebRTCService();