import React from 'react';
import Dashboard from './components/Dashboard';
import { ThemeProvider } from './components/theme-provider';

// Mock user data
const mockUser = {
  name: '<PERSON>',
  email: '<EMAIL>',
  avatar: '/placeholder.svg?height=40&width=40'
};

function App() {
  return (
    <ThemeProvider defaultTheme="dark" storageKey="voicehub-ui-theme">
      <div className="min-h-screen">
        <Dashboard user={mockUser} />
      </div>
    </ThemeProvider>
  );
}

export default App;