import React, { useState, useRef, useEffect } from 'react';
import { 
  Message<PERSON>quare, 
  Send, 
  Mic, 
  <PERSON><PERSON>, 
  User, 
  <PERSON>, 
  <PERSON>,
  <PERSON>,
  <PERSON>own,
  <PERSON>h,
  Volume2,
  <PERSON><PERSON>,
  MoreV<PERSON>ical,
  Spark<PERSON>
} from 'lucide-react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';

interface Message {
  id: string;
  content: string;
  sender: 'user' | 'assistant';
  timestamp: Date;
  emotion?: 'happy' | 'sad' | 'neutral' | 'excited';
  isVoice?: boolean;
}

interface ConversationWidgetProps {
  expanded?: boolean;
}

const ConversationWidget: React.FC<ConversationWidgetProps> = ({ expanded = false }) => {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      content: 'Hello! I\'m your AI voice assistant. How can I help you today?',
      sender: 'assistant',
      timestamp: new Date(Date.now() - 300000),
      emotion: 'happy'
    },
    {
      id: '2',
      content: 'Hi there! I\'d like to schedule a meeting for tomorrow.',
      sender: 'user',
      timestamp: new Date(Date.now() - 240000),
      emotion: 'neutral'
    },
    {
      id: '3',
      content: 'I\'d be happy to help you schedule a meeting! What time works best for you tomorrow, and who will be attending?',
      sender: 'assistant',
      timestamp: new Date(Date.now() - 180000),
      emotion: 'happy'
    }
  ]);
  
  const [newMessage, setNewMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [conversationMode, setConversationMode] = useState<'general' | 'emotional' | 'schedule'>('general');
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async () => {
    if (!newMessage.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      content: newMessage,
      sender: 'user',
      timestamp: new Date(),
      emotion: 'neutral'
    };

    setMessages(prev => [...prev, userMessage]);
    setNewMessage('');
    setIsTyping(true);

    // Simulate AI response
    setTimeout(() => {
      const aiResponse: Message = {
        id: (Date.now() + 1).toString(),
        content: generateAIResponse(newMessage, conversationMode),
        sender: 'assistant',
        timestamp: new Date(),
        emotion: 'happy'
      };
      
      setMessages(prev => [...prev, aiResponse]);
      setIsTyping(false);
    }, 1500);
  };

  const generateAIResponse = (userInput: string, mode: string): string => {
    const responses = {
      general: [
        "That's interesting! Tell me more about that.",
        "I understand. How can I assist you further?",
        "Great question! Let me help you with that.",
        "I'm here to help. What would you like to know?"
      ],
      emotional: [
        "I hear you, and I want you to know that your feelings are valid.",
        "It sounds like you're going through something challenging. I'm here to listen.",
        "Thank you for sharing that with me. How are you feeling right now?",
        "I appreciate your openness. Would you like to talk about what's on your mind?"
      ],
      schedule: [
        "I can help you organize your schedule. What would you like to plan?",
        "Let me check your calendar and find the best time for that.",
        "I'll set up that appointment for you. What details should I include?",
        "Your schedule is looking good. Would you like me to add anything else?"
      ]
    };

    const modeResponses = responses[mode] || responses.general;
    return modeResponses[Math.floor(Math.random() * modeResponses.length)];
  };

  const handleVoiceRecord = () => {
    setIsRecording(!isRecording);
    // Simulate voice recording
    if (!isRecording) {
      setTimeout(() => {
        setIsRecording(false);
        const voiceMessage: Message = {
          id: Date.now().toString(),
          content: "This is a voice message that has been transcribed to text.",
          sender: 'user',
          timestamp: new Date(),
          emotion: 'neutral',
          isVoice: true
        };
        setMessages(prev => [...prev, voiceMessage]);
      }, 3000);
    }
  };

  const getEmotionIcon = (emotion?: string) => {
    switch (emotion) {
      case 'happy':
      case 'excited':
        return <Smile className="w-4 h-4 text-green-400" />;
      case 'sad':
        return <Frown className="w-4 h-4 text-red-400" />;
      default:
        return <Meh className="w-4 h-4 text-blue-400" />;
    }
  };

  const formatTime = (date: Date): string => {
    return date.toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  return (
    <Card className={`bg-white/10 backdrop-blur-xl border-white/20 hover:bg-white/15 transition-all duration-300 ${
      expanded ? 'col-span-full h-[600px]' : 'h-[400px]'
    }`}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-white flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
              <MessageSquare className="w-4 h-4 text-white" />
            </div>
            <span>AI Conversation</span>
          </CardTitle>
          <div className="flex items-center space-x-2">
            <Badge 
              variant="secondary" 
              className={`cursor-pointer transition-colors ${
                conversationMode === 'general' 
                  ? 'bg-blue-500/20 text-blue-200 border-blue-400/30' 
                  : 'bg-white/10 text-blue-300 border-white/20'
              }`}
              onClick={() => setConversationMode('general')}
            >
              <Brain className="w-3 h-3 mr-1" />
              General
            </Badge>
            <Badge 
              variant="secondary" 
              className={`cursor-pointer transition-colors ${
                conversationMode === 'emotional' 
                  ? 'bg-pink-500/20 text-pink-200 border-pink-400/30' 
                  : 'bg-white/10 text-blue-300 border-white/20'
              }`}
              onClick={() => setConversationMode('emotional')}
            >
              <Heart className="w-3 h-3 mr-1" />
              Support
            </Badge>
            <Badge 
              variant="secondary" 
              className={`cursor-pointer transition-colors ${
                conversationMode === 'schedule' 
                  ? 'bg-green-500/20 text-green-200 border-green-400/30' 
                  : 'bg-white/10 text-blue-300 border-white/20'
              }`}
              onClick={() => setConversationMode('schedule')}
            >
              <Sparkles className="w-3 h-3 mr-1" />
              Schedule
            </Badge>
          </div>
        </div>
      </CardHeader>

      <CardContent className="flex flex-col h-full p-0">
        {/* Messages Area */}
        <ScrollArea className="flex-1 px-6">
          <div className="space-y-4 pb-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex items-start space-x-3 ${
                  message.sender === 'user' ? 'flex-row-reverse space-x-reverse' : ''
                }`}
              >
                {/* Avatar */}
                <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                  message.sender === 'user' 
                    ? 'bg-gradient-to-r from-blue-500 to-cyan-500' 
                    : 'bg-gradient-to-r from-purple-500 to-pink-500'
                }`}>
                  {message.sender === 'user' ? (
                    <User className="w-4 h-4 text-white" />
                  ) : (
                    <Bot className="w-4 h-4 text-white" />
                  )}
                </div>

                {/* Message Content */}
                <div className={`flex-1 max-w-[80%] ${
                  message.sender === 'user' ? 'text-right' : 'text-left'
                }`}>
                  <div className={`inline-block p-3 rounded-2xl ${
                    message.sender === 'user'
                      ? 'bg-gradient-to-r from-blue-500/20 to-cyan-500/20 border border-blue-400/30'
                      : 'bg-white/10 border border-white/20'
                  }`}>
                    <p className="text-white text-sm leading-relaxed">
                      {message.content}
                    </p>
                    {message.isVoice && (
                      <div className="flex items-center mt-2 pt-2 border-t border-white/10">
                        <Volume2 className="w-3 h-3 text-blue-400 mr-1" />
                        <span className="text-xs text-blue-300">Voice message</span>
                      </div>
                    )}
                  </div>
                  
                  {/* Message Meta */}
                  <div className={`flex items-center mt-1 space-x-2 text-xs text-blue-300 ${
                    message.sender === 'user' ? 'justify-end' : 'justify-start'
                  }`}>
                    <span>{formatTime(message.timestamp)}</span>
                    {getEmotionIcon(message.emotion)}
                    <Button variant="ghost" size="sm" className="h-auto p-0 text-blue-300 hover:text-white">
                      <Copy className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
              </div>
            ))}

            {/* Typing Indicator */}
            {isTyping && (
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center">
                  <Bot className="w-4 h-4 text-white" />
                </div>
                <div className="bg-white/10 border border-white/20 rounded-2xl p-3">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                  </div>
                </div>
              </div>
            )}
            
            <div ref={messagesEndRef} />
          </div>
        </ScrollArea>

        {/* Input Area */}
        <div className="p-6 border-t border-white/10">
          <div className="flex items-center space-x-3">
            <div className="flex-1 relative">
              <Input
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                placeholder="Type your message or use voice..."
                className="bg-white/10 border-white/20 text-white placeholder-blue-300 focus:border-blue-400 pr-12"
                disabled={isRecording}
              />
              {isRecording && (
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                </div>
              )}
            </div>
            
            <Button
              onClick={handleVoiceRecord}
              className={`w-10 h-10 rounded-full ${
                isRecording
                  ? 'bg-red-500 hover:bg-red-600 animate-pulse'
                  : 'bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600'
              }`}
            >
              <Mic className="w-4 h-4" />
            </Button>
            
            <Button
              onClick={handleSendMessage}
              disabled={!newMessage.trim() || isRecording}
              className="w-10 h-10 bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 rounded-full disabled:opacity-50"
            >
              <Send className="w-4 h-4" />
            </Button>
          </div>

          {/* Quick Actions */}
          {expanded && (
            <div className="flex items-center justify-between mt-4 pt-4 border-t border-white/10">
              <div className="flex items-center space-x-2 text-xs text-blue-300">
                <span>Mode:</span>
                <Badge variant="outline" className="border-white/20 text-blue-200">
                  {conversationMode.charAt(0).toUpperCase() + conversationMode.slice(1)}
                </Badge>
              </div>
              <div className="flex items-center space-x-2">
                <Button variant="ghost" size="sm" className="text-blue-300 hover:text-white">
                  <Volume2 className="w-4 h-4 mr-1" />
                  Read Aloud
                </Button>
                <Button variant="ghost" size="sm" className="text-blue-300 hover:text-white">
                  <MoreVertical className="w-4 h-4" />
                </Button>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default ConversationWidget;