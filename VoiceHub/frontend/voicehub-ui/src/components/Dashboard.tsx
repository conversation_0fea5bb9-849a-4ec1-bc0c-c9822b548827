import React, { useState, useEffect } from 'react';
import { 
  Mic, 
  MessageSquare, 
  Calendar, 
  FileText, 
  Settings, 
  User, 
  Bell,
  Search,
  Menu,
  X,
  Play,
  Pause,
  Volume2,
  Heart,
  TrendingUp
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import VoiceRecorderWidget from './VoiceRecorderWidget';
import ConversationWidget from './ConversationWidget';
import ScheduleWidget from './ScheduleWidget';
import VoiceNotesWidget from './VoiceNotesWidget';

interface DashboardProps {
  user?: {
    name: string;
    avatar?: string;
    email: string;
  };
}

const Dashboard: React.FC<DashboardProps> = ({ user }) => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [activeSection, setActiveSection] = useState('dashboard');
  const [isRecording, setIsRecording] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());

  // Update time every minute
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);
    return () => clearInterval(timer);
  }, []);

  const navigationItems = [
    { id: 'dashboard', label: 'Dashboard', icon: TrendingUp },
    { id: 'voice-chat', label: 'Voice Chat', icon: MessageSquare },
    { id: 'schedule', label: 'Schedule', icon: Calendar },
    { id: 'voice-notes', label: 'Voice Notes', icon: FileText },
    { id: 'settings', label: 'Settings', icon: Settings },
  ];

  const quickStats = [
    { label: 'Today\'s Conversations', value: '12', trend: '+3', color: 'text-blue-600' },
    { label: 'Voice Notes', value: '48', trend: '+8', color: 'text-green-600' },
    { label: 'Scheduled Events', value: '6', trend: '+2', color: 'text-purple-600' },
    { label: 'Active Sessions', value: '2', trend: '0', color: 'text-orange-600' },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900">
      {/* Background Effects */}
      <div className="fixed inset-0 bg-[url('/placeholder.svg?height=1080&width=1920')] bg-cover bg-center opacity-5"></div>
      <div className="fixed inset-0 bg-gradient-to-br from-blue-500/10 via-cyan-500/5 to-purple-500/10"></div>
      
      {/* Sidebar */}
      <div className={`fixed inset-y-0 left-0 z-50 w-64 transform transition-transform duration-300 ease-in-out ${
        isSidebarOpen ? 'translate-x-0' : '-translate-x-full'
      } lg:translate-x-0`}>
        <div className="h-full bg-white/10 backdrop-blur-xl border-r border-white/20">
          {/* Logo */}
          <div className="flex items-center justify-between p-6 border-b border-white/10">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center">
                <Mic className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-white">VoiceHub</h1>
                <p className="text-xs text-blue-200">AI Assistant</p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              className="lg:hidden text-white hover:bg-white/10"
              onClick={() => setIsSidebarOpen(false)}
            >
              <X className="w-5 h-5" />
            </Button>
          </div>

          {/* User Profile */}
          <div className="p-6 border-b border-white/10">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                <User className="w-6 h-6 text-white" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-white truncate">
                  {user?.name || 'Guest User'}
                </p>
                <p className="text-xs text-blue-200 truncate">
                  {user?.email || '<EMAIL>'}
                </p>
              </div>
            </div>
          </div>

          {/* Navigation */}
          <nav className="p-4 space-y-2">
            {navigationItems.map((item) => {
              const Icon = item.icon;
              return (
                <button
                  key={item.id}
                  onClick={() => setActiveSection(item.id)}
                  className={`w-full flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-200 ${
                    activeSection === item.id
                      ? 'bg-gradient-to-r from-blue-500/20 to-cyan-500/20 text-white border border-blue-400/30'
                      : 'text-blue-100 hover:bg-white/10 hover:text-white'
                  }`}
                >
                  <Icon className="w-5 h-5" />
                  <span className="font-medium">{item.label}</span>
                </button>
              );
            })}
          </nav>

          {/* Quick Actions */}
          <div className="p-4 mt-auto">
            <div className="bg-gradient-to-r from-blue-500/20 to-cyan-500/20 rounded-xl p-4 border border-blue-400/30">
              <h3 className="text-sm font-medium text-white mb-2">Quick Record</h3>
              <Button
                onClick={() => setIsRecording(!isRecording)}
                className={`w-full ${
                  isRecording
                    ? 'bg-red-500 hover:bg-red-600'
                    : 'bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600'
                }`}
              >
                <Mic className="w-4 h-4 mr-2" />
                {isRecording ? 'Stop Recording' : 'Start Recording'}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="lg:ml-64">
        {/* Top Bar */}
        <header className="bg-white/10 backdrop-blur-xl border-b border-white/20 sticky top-0 z-40">
          <div className="flex items-center justify-between px-6 py-4">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                className="lg:hidden text-white hover:bg-white/10"
                onClick={() => setIsSidebarOpen(true)}
              >
                <Menu className="w-5 h-5" />
              </Button>
              
              <div className="hidden md:block">
                <h2 className="text-2xl font-bold text-white capitalize">
                  {activeSection.replace('-', ' ')}
                </h2>
                <p className="text-blue-200 text-sm">
                  {currentTime.toLocaleDateString('en-US', { 
                    weekday: 'long', 
                    year: 'numeric', 
                    month: 'long', 
                    day: 'numeric' 
                  })}
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              {/* Search */}
              <div className="hidden md:block relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-blue-300 w-4 h-4" />
                <Input
                  placeholder="Search conversations, notes..."
                  className="pl-10 bg-white/10 border-white/20 text-white placeholder-blue-300 focus:border-blue-400"
                />
              </div>

              {/* Notifications */}
              <Button variant="ghost" size="sm" className="text-white hover:bg-white/10 relative">
                <Bell className="w-5 h-5" />
                <Badge className="absolute -top-1 -right-1 w-5 h-5 p-0 bg-red-500 text-xs">
                  3
                </Badge>
              </Button>

              {/* Voice Status */}
              <div className="flex items-center space-x-2 bg-white/10 rounded-full px-3 py-2">
                <div className={`w-2 h-2 rounded-full ${isRecording ? 'bg-red-500 animate-pulse' : 'bg-green-500'}`}></div>
                <span className="text-sm text-white">
                  {isRecording ? 'Recording' : 'Ready'}
                </span>
              </div>
            </div>
          </div>
        </header>

        {/* Dashboard Content */}
        <main className="p-6">
          {activeSection === 'dashboard' && (
            <div className="space-y-6">
              {/* Quick Stats */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {quickStats.map((stat, index) => (
                  <Card key={index} className="bg-white/10 backdrop-blur-xl border-white/20 hover:bg-white/15 transition-all duration-300">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-blue-200 text-sm font-medium">{stat.label}</p>
                          <p className="text-3xl font-bold text-white mt-2">{stat.value}</p>
                        </div>
                        <div className={`text-right ${stat.color}`}>
                          <p className="text-sm font-medium">{stat.trend}</p>
                          <p className="text-xs text-blue-300">vs yesterday</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              {/* Main Widgets */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <VoiceRecorderWidget />
                <ConversationWidget />
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <ScheduleWidget />
                <VoiceNotesWidget />
              </div>
            </div>
          )}

          {/* Other sections would be rendered here based on activeSection */}
          {activeSection === 'voice-chat' && (
            <div className="max-w-4xl mx-auto">
              <ConversationWidget expanded />
            </div>
          )}

          {activeSection === 'schedule' && (
            <div className="max-w-6xl mx-auto">
              <ScheduleWidget expanded />
            </div>
          )}

          {activeSection === 'voice-notes' && (
            <div className="max-w-6xl mx-auto">
              <VoiceNotesWidget expanded />
            </div>
          )}
        </main>
      </div>

      {/* Mobile Sidebar Overlay */}
      {isSidebarOpen && (
        <div
          className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40 lg:hidden"
          onClick={() => setIsSidebarOpen(false)}
        />
      )}
    </div>
  );
};

export default Dashboard;