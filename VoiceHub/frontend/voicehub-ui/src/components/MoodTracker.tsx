import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  <PERSON>, 
  <PERSON>, 
  <PERSON><PERSON>, 
  <PERSON><PERSON>, 
  <PERSON>, 
  Surprised,
  <PERSON>,
  TrendingUp,
  <PERSON>,
  <PERSON>rk<PERSON>
} from 'lucide-react';

interface MoodEntry {
  id: string;
  date: Date;
  mood: 'happy' | 'sad' | 'angry' | 'surprised' | 'neutral' | 'excited';
  intensity: number; // 1-10
  note?: string;
  triggers?: string[];
}

interface MoodTrackerProps {
  className?: string;
}

const MoodTracker: React.FC<MoodTrackerProps> = ({ className = '' }) => {
  const [currentMood, setCurrentMood] = useState<MoodEntry['mood'] | null>(null);
  const [intensity, setIntensity] = useState(5);
  const [recentEntries, setRecentEntries] = useState<MoodEntry[]>([
    {
      id: '1',
      date: new Date(Date.now() - 3600000),
      mood: 'happy',
      intensity: 8,
      note: 'Great conversation with <PERSON> assistant',
      triggers: ['achievement', 'social']
    },
    {
      id: '2',
      date: new Date(Date.now() - 7200000),
      mood: 'excited',
      intensity: 9,
      note: 'Successfully completed voice recording session',
      triggers: ['accomplishment', 'progress']
    },
    {
      id: '3',
      date: new Date(Date.now() - 10800000),
      mood: 'neutral',
      intensity: 6,
      note: 'Regular check-in',
      triggers: ['routine']
    }
  ]);

  const moodOptions = [
    { 
      mood: 'happy' as const, 
      icon: Smile, 
      color: 'text-green-400', 
      bgColor: 'bg-green-500/20',
      borderColor: 'border-green-400/30',
      label: 'Happy' 
    },
    { 
      mood: 'excited' as const, 
      icon: Sparkles, 
      color: 'text-yellow-400', 
      bgColor: 'bg-yellow-500/20',
      borderColor: 'border-yellow-400/30',
      label: 'Excited' 
    },
    { 
      mood: 'neutral' as const, 
      icon: Meh, 
      color: 'text-gray-400', 
      bgColor: 'bg-gray-500/20',
      borderColor: 'border-gray-400/30',
      label: 'Neutral' 
    },
    { 
      mood: 'sad' as const, 
      icon: Frown, 
      color: 'text-blue-400', 
      bgColor: 'bg-blue-500/20',
      borderColor: 'border-blue-400/30',
      label: 'Sad' 
    },
    { 
      mood: 'angry' as const, 
      icon: Angry, 
      color: 'text-red-400', 
      bgColor: 'bg-red-500/20',
      borderColor: 'border-red-400/30',
      label: 'Angry' 
    },
    { 
      mood: 'surprised' as const, 
      icon: Surprised, 
      color: 'text-purple-400', 
      bgColor: 'bg-purple-500/20',
      borderColor: 'border-purple-400/30',
      label: 'Surprised' 
    }
  ];

  const handleMoodSubmit = () => {
    if (!currentMood) return;

    const newEntry: MoodEntry = {
      id: Date.now().toString(),
      date: new Date(),
      mood: currentMood,
      intensity,
      note: `Mood recorded via voice session`,
      triggers: ['voice_session']
    };

    setRecentEntries(prev => [newEntry, ...prev.slice(0, 4)]);
    setCurrentMood(null);
    setIntensity(5);
  };

  const getMoodIcon = (mood: MoodEntry['mood']) => {
    const option = moodOptions.find(opt => opt.mood === mood);
    return option ? option.icon : Meh;
  };

  const getMoodColor = (mood: MoodEntry['mood']) => {
    const option = moodOptions.find(opt => opt.mood === mood);
    return option ? option.color : 'text-gray-400';
  };

  const formatTimeAgo = (date: Date): string => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 60) {
      return `${diffInMinutes}m ago`;
    } else if (diffInMinutes < 1440) {
      return `${Math.floor(diffInMinutes / 60)}h ago`;
    } else {
      return `${Math.floor(diffInMinutes / 1440)}d ago`;
    }
  };

  return (
    <Card className={`bg-white/10 backdrop-blur-xl border-white/20 ${className}`}>
      <CardHeader>
        <CardTitle className="text-white flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-r from-pink-500 to-purple-500 rounded-lg flex items-center justify-center">
              <Heart className="w-4 h-4 text-white" />
            </div>
            <span>Mood Tracker</span>
          </div>
          <Badge variant="secondary" className="bg-purple-500/20 text-purple-400 border-purple-400/30">
            Real-time
          </Badge>
        </CardTitle>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Current Mood Selection */}
        <div className="space-y-4">
          <h3 className="text-white font-medium">How are you feeling right now?</h3>
          
          <div className="grid grid-cols-3 gap-3">
            {moodOptions.map((option) => {
              const Icon = option.icon;
              const isSelected = currentMood === option.mood;
              
              return (
                <button
                  key={option.mood}
                  onClick={() => setCurrentMood(option.mood)}
                  className={`p-4 rounded-lg border-2 transition-all duration-200 ${
                    isSelected 
                      ? `${option.bgColor} ${option.borderColor} scale-105` 
                      : 'bg-white/5 border-white/10 hover:bg-white/10'
                  }`}
                >
                  <Icon className={`w-6 h-6 mx-auto mb-2 ${
                    isSelected ? option.color : 'text-gray-400'
                  }`} />
                  <p className={`text-sm ${
                    isSelected ? 'text-white' : 'text-gray-300'
                  }`}>
                    {option.label}
                  </p>
                </button>
              );
            })}
          </div>

          {/* Intensity Slider */}
          {currentMood && (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <label className="text-white text-sm">Intensity Level</label>
                <span className="text-blue-300 text-sm">{intensity}/10</span>
              </div>
              
              <div className="flex items-center space-x-3">
                <span className="text-gray-400 text-xs">Low</span>
                <div className="flex-1 relative">
                  <input
                    type="range"
                    min="1"
                    max="10"
                    value={intensity}
                    onChange={(e) => setIntensity(Number(e.target.value))}
                    className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
                  />
                  <div 
                    className="absolute top-0 left-0 h-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg pointer-events-none"
                    style={{ width: `${(intensity / 10) * 100}%` }}
                  />
                </div>
                <span className="text-gray-400 text-xs">High</span>
              </div>

              <Button 
                onClick={handleMoodSubmit}
                className="w-full bg-gradient-to-r from-pink-500 to-purple-500 hover:from-pink-600 hover:to-purple-600"
              >
                Record Mood
              </Button>
            </div>
          )}
        </div>

        {/* Recent Mood Entries */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h3 className="text-white font-medium">Recent Moods</h3>
            <Button variant="ghost" size="sm" className="text-blue-300 hover:text-white">
              <Calendar className="w-4 h-4 mr-1" />
              View All
            </Button>
          </div>

          <div className="space-y-2">
            {recentEntries.map((entry) => {
              const Icon = getMoodIcon(entry.mood);
              const colorClass = getMoodColor(entry.mood);
              
              return (
                <div 
                  key={entry.id}
                  className="flex items-center space-x-3 p-3 bg-white/5 rounded-lg hover:bg-white/10 transition-colors"
                >
                  <div className="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center">
                    <Icon className={`w-5 h-5 ${colorClass}`} />
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <span className="text-white font-medium capitalize">
                        {entry.mood}
                      </span>
                      <Badge variant="secondary" className="text-xs">
                        {entry.intensity}/10
                      </Badge>
                    </div>
                    
                    {entry.note && (
                      <p className="text-blue-200 text-sm mt-1">{entry.note}</p>
                    )}
                    
                    <div className="flex items-center space-x-2 mt-1">
                      <span className="text-gray-400 text-xs">
                        {formatTimeAgo(entry.date)}
                      </span>
                      {entry.triggers && entry.triggers.length > 0 && (
                        <div className="flex items-center space-x-1">
                          {entry.triggers.slice(0, 2).map((trigger, index) => (
                            <Badge 
                              key={index}
                              variant="secondary" 
                              className="text-xs bg-blue-500/20 text-blue-300"
                            >
                              {trigger}
                            </Badge>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Mood Insights */}
        <div className="space-y-3">
          <h3 className="text-white font-medium">Today's Insights</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div className="p-3 bg-gradient-to-r from-green-500/20 to-emerald-500/20 border border-green-400/30 rounded-lg">
              <div className="flex items-center space-x-2">
                <TrendingUp className="w-4 h-4 text-green-400" />
                <span className="text-green-400 text-sm font-medium">Positive Trend</span>
              </div>
              <p className="text-green-300 text-xs mt-1">
                Your mood has been consistently positive today
              </p>
            </div>

            <div className="p-3 bg-gradient-to-r from-purple-500/20 to-pink-500/20 border border-purple-400/30 rounded-lg">
              <div className="flex items-center space-x-2">
                <Brain className="w-4 h-4 text-purple-400" />
                <span className="text-purple-400 text-sm font-medium">AI Suggestion</span>
              </div>
              <p className="text-purple-300 text-xs mt-1">
                Voice sessions seem to boost your mood
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default MoodTracker;