import React, { useState } from 'react';
import { 
  Calendar, 
  Clock, 
  Plus, 
  MapPin, 
  Users, 
  Bell,
  ChevronLeft,
  ChevronRight,
  MoreVertical,
  Mic,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { <PERSON>, <PERSON>Content, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';

interface ScheduleEvent {
  id: string;
  title: string;
  time: string;
  duration: string;
  location?: string;
  attendees?: number;
  priority: 'low' | 'medium' | 'high';
  status: 'upcoming' | 'ongoing' | 'completed';
  isVoiceCreated?: boolean;
}

interface ScheduleWidgetProps {
  expanded?: boolean;
}

const ScheduleWidget: React.FC<ScheduleWidgetProps> = ({ expanded = false }) => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [isVoiceMode, setIsVoiceMode] = useState(false);

  const events: ScheduleEvent[] = [
    {
      id: '1',
      title: 'Team Standup Meeting',
      time: '09:00',
      duration: '30 min',
      location: 'Conference Room A',
      attendees: 8,
      priority: 'high',
      status: 'upcoming',
      isVoiceCreated: true
    },
    {
      id: '2',
      title: 'Client Presentation',
      time: '14:00',
      duration: '1 hour',
      location: 'Virtual Meeting',
      attendees: 5,
      priority: 'high',
      status: 'upcoming'
    },
    {
      id: '3',
      title: 'Code Review Session',
      time: '16:30',
      duration: '45 min',
      attendees: 3,
      priority: 'medium',
      status: 'upcoming'
    },
    {
      id: '4',
      title: 'Project Planning',
      time: '11:00',
      duration: '2 hours',
      location: 'Meeting Room B',
      attendees: 6,
      priority: 'medium',
      status: 'completed'
    }
  ];

  const todayEvents = events.filter(event => 
    selectedDate.toDateString() === new Date().toDateString()
  );

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-500/20 text-red-200 border-red-400/30';
      case 'medium':
        return 'bg-yellow-500/20 text-yellow-200 border-yellow-400/30';
      case 'low':
        return 'bg-green-500/20 text-green-200 border-green-400/30';
      default:
        return 'bg-blue-500/20 text-blue-200 border-blue-400/30';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-400" />;
      case 'ongoing':
        return <AlertCircle className="w-4 h-4 text-orange-400" />;
      default:
        return <Clock className="w-4 h-4 text-blue-400" />;
    }
  };

  const formatDate = (date: Date): string => {
    return date.toLocaleDateString('en-US', { 
      weekday: 'long', 
      month: 'short', 
      day: 'numeric' 
    });
  };

  const navigateDate = (direction: 'prev' | 'next') => {
    const newDate = new Date(selectedDate);
    newDate.setDate(newDate.getDate() + (direction === 'next' ? 1 : -1));
    setSelectedDate(newDate);
  };

  return (
    <Card className={`bg-white/10 backdrop-blur-xl border-white/20 hover:bg-white/15 transition-all duration-300 ${
      expanded ? 'col-span-full' : ''
    }`}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-white flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg flex items-center justify-center">
              <Calendar className="w-4 h-4 text-white" />
            </div>
            <span>Schedule</span>
          </CardTitle>
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsVoiceMode(!isVoiceMode)}
              className={`text-white hover:bg-white/10 ${
                isVoiceMode ? 'bg-blue-500/20 border border-blue-400/30' : ''
              }`}
            >
              <Mic className="w-4 h-4" />
            </Button>
            <Button variant="ghost" size="sm" className="text-white hover:bg-white/10">
              <Plus className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Date Navigation */}
        <div className="flex items-center justify-between bg-white/5 rounded-lg p-3">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigateDate('prev')}
            className="text-blue-200 hover:bg-white/10"
          >
            <ChevronLeft className="w-4 h-4" />
          </Button>
          
          <div className="text-center">
            <h3 className="text-white font-medium">{formatDate(selectedDate)}</h3>
            <p className="text-blue-200 text-sm">{todayEvents.length} events</p>
          </div>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigateDate('next')}
            className="text-blue-200 hover:bg-white/10"
          >
            <ChevronRight className="w-4 h-4" />
          </Button>
        </div>

        {/* Voice Command Hint */}
        {isVoiceMode && (
          <div className="bg-blue-500/20 border border-blue-400/30 rounded-lg p-3">
            <div className="flex items-center space-x-2 mb-2">
              <Mic className="w-4 h-4 text-blue-400" />
              <span className="text-blue-200 text-sm font-medium">Voice Mode Active</span>
            </div>
            <p className="text-blue-300 text-xs">
              Try: "Schedule a meeting tomorrow at 2 PM" or "What's my schedule for today?"
            </p>
          </div>
        )}

        {/* Events List */}
        <ScrollArea className={expanded ? "h-[400px]" : "h-[250px]"}>
          <div className="space-y-3">
            {todayEvents.length > 0 ? (
              todayEvents.map((event) => (
                <div
                  key={event.id}
                  className="bg-white/5 rounded-xl p-4 border border-white/10 hover:bg-white/10 transition-all duration-200"
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(event.status)}
                      <h4 className="text-white font-medium text-sm">{event.title}</h4>
                      {event.isVoiceCreated && (
                        <Badge variant="outline" className="border-purple-400/30 text-purple-200 text-xs">
                          <Mic className="w-2 h-2 mr-1" />
                          Voice
                        </Badge>
                      )}
                    </div>
                    <Button variant="ghost" size="sm" className="text-blue-300 hover:text-white">
                      <MoreVertical className="w-4 h-4" />
                    </Button>
                  </div>

                  <div className="grid grid-cols-2 gap-3 mb-3">
                    <div className="flex items-center space-x-2 text-blue-200 text-xs">
                      <Clock className="w-3 h-3" />
                      <span>{event.time} ({event.duration})</span>
                    </div>
                    {event.location && (
                      <div className="flex items-center space-x-2 text-blue-200 text-xs">
                        <MapPin className="w-3 h-3" />
                        <span>{event.location}</span>
                      </div>
                    )}
                    {event.attendees && (
                      <div className="flex items-center space-x-2 text-blue-200 text-xs">
                        <Users className="w-3 h-3" />
                        <span>{event.attendees} attendees</span>
                      </div>
                    )}
                    <div className="flex items-center space-x-2">
                      <Badge variant="secondary" className={`text-xs ${getPriorityColor(event.priority)}`}>
                        {event.priority.toUpperCase()}
                      </Badge>
                    </div>
                  </div>

                  {event.status === 'upcoming' && (
                    <div className="flex items-center justify-between pt-3 border-t border-white/10">
                      <Button variant="ghost" size="sm" className="text-blue-300 hover:text-white text-xs">
                        <Bell className="w-3 h-3 mr-1" />
                        Remind me
                      </Button>
                      <Button variant="ghost" size="sm" className="text-green-300 hover:text-white text-xs">
                        Join Meeting
                      </Button>
                    </div>
                  )}
                </div>
              ))
            ) : (
              <div className="text-center py-8">
                <Calendar className="w-12 h-12 text-blue-400 mx-auto mb-3 opacity-50" />
                <p className="text-blue-200 text-sm">No events scheduled for this day</p>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="text-blue-300 hover:text-white mt-2"
                  onClick={() => setIsVoiceMode(true)}
                >
                  <Plus className="w-4 h-4 mr-1" />
                  Add Event
                </Button>
              </div>
            )}
          </div>
        </ScrollArea>

        {/* Quick Stats */}
        {expanded && (
          <div className="grid grid-cols-3 gap-4 pt-4 border-t border-white/10">
            <div className="text-center">
              <p className="text-2xl font-bold text-white">{events.filter(e => e.status === 'upcoming').length}</p>
              <p className="text-blue-200 text-xs">Upcoming</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-white">{events.filter(e => e.status === 'completed').length}</p>
              <p className="text-blue-200 text-xs">Completed</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-white">{events.filter(e => e.isVoiceCreated).length}</p>
              <p className="text-blue-200 text-xs">Voice Created</p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ScheduleWidget;