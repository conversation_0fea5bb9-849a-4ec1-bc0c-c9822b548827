import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { 
  <PERSON><PERSON>hart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
  Area,
  AreaChart
} from 'recharts';
import { 
  TrendingUp, 
  TrendingDown, 
  Activity, 
  Clock, 
  Mic, 
  Volume2,
  Brain,
  Heart,
  Zap,
  Target,
  Calendar,
  Filter,
  Download,
  RefreshCw
} from 'lucide-react';

interface VoiceMetrics {
  totalRecordings: number;
  totalDuration: number;
  averageSessionLength: number;
  voiceQualityScore: number;
  speechRate: number;
  pauseFrequency: number;
  volumeConsistency: number;
  clarityScore: number;
}

interface MoodData {
  date: string;
  happiness: number;
  sadness: number;
  anger: number;
  fear: number;
  surprise: number;
  neutral: number;
  overall: number;
}

interface UsageData {
  date: string;
  recordings: number;
  duration: number;
  sessions: number;
}

interface VoiceAnalyticsProps {
  className?: string;
}

const VoiceAnalytics: React.FC<VoiceAnalyticsProps> = ({ className = '' }) => {
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d' | '1y'>('30d');
  const [isLoading, setIsLoading] = useState(false);
  
  // Mock data - in real app, this would come from API
  const [metrics, setMetrics] = useState<VoiceMetrics>({
    totalRecordings: 247,
    totalDuration: 18420, // seconds
    averageSessionLength: 74.6,
    voiceQualityScore: 8.7,
    speechRate: 145, // words per minute
    pauseFrequency: 12.3,
    volumeConsistency: 85.2,
    clarityScore: 9.1
  });

  const [moodData, setMoodData] = useState<MoodData[]>([
    { date: '2024-01-15', happiness: 75, sadness: 15, anger: 5, fear: 3, surprise: 12, neutral: 45, overall: 7.2 },
    { date: '2024-01-16', happiness: 82, sadness: 10, anger: 3, fear: 2, surprise: 18, neutral: 38, overall: 7.8 },
    { date: '2024-01-17', happiness: 68, sadness: 22, anger: 8, fear: 5, surprise: 8, neutral: 52, overall: 6.5 },
    { date: '2024-01-18', happiness: 85, sadness: 8, anger: 2, fear: 1, surprise: 15, neutral: 35, overall: 8.1 },
    { date: '2024-01-19', happiness: 78, sadness: 12, anger: 4, fear: 3, surprise: 20, neutral: 42, overall: 7.5 },
    { date: '2024-01-20', happiness: 90, sadness: 5, anger: 1, fear: 1, surprise: 25, neutral: 28, overall: 8.7 },
    { date: '2024-01-21', happiness: 72, sadness: 18, anger: 6, fear: 4, surprise: 10, neutral: 48, overall: 6.8 }
  ]);

  const [usageData, setUsageData] = useState<UsageData[]>([
    { date: '2024-01-15', recordings: 12, duration: 890, sessions: 5 },
    { date: '2024-01-16', recordings: 15, duration: 1120, sessions: 7 },
    { date: '2024-01-17', recordings: 8, duration: 650, sessions: 4 },
    { date: '2024-01-18', recordings: 18, duration: 1340, sessions: 8 },
    { date: '2024-01-19', recordings: 14, duration: 980, sessions: 6 },
    { date: '2024-01-20', recordings: 22, duration: 1580, sessions: 9 },
    { date: '2024-01-21', recordings: 16, duration: 1150, sessions: 7 }
  ]);

  const voiceQualityData = [
    { metric: 'Clarity', score: 91, maxScore: 100 },
    { metric: 'Volume', score: 85, maxScore: 100 },
    { metric: 'Pace', score: 78, maxScore: 100 },
    { metric: 'Tone', score: 88, maxScore: 100 },
    { metric: 'Consistency', score: 82, maxScore: 100 }
  ];

  const emotionDistribution = [
    { name: 'Happy', value: 35, color: '#10B981' },
    { name: 'Neutral', value: 28, color: '#6B7280' },
    { name: 'Excited', value: 18, color: '#F59E0B' },
    { name: 'Calm', value: 12, color: '#3B82F6' },
    { name: 'Sad', value: 5, color: '#EF4444' },
    { name: 'Angry', value: 2, color: '#DC2626' }
  ];

  const refreshData = async () => {
    setIsLoading(true);
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);
  };

  const formatDuration = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}h ${minutes}m`;
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric' 
    });
  };

  const getScoreColor = (score: number): string => {
    if (score >= 8) return 'text-green-400';
    if (score >= 6) return 'text-yellow-400';
    return 'text-red-400';
  };

  const getScoreBadgeColor = (score: number): string => {
    if (score >= 8) return 'bg-green-500/20 text-green-400 border-green-400/30';
    if (score >= 6) return 'bg-yellow-500/20 text-yellow-400 border-yellow-400/30';
    return 'bg-red-500/20 text-red-400 border-red-400/30';
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white mb-2">Voice Analytics</h2>
          <p className="text-blue-200">Comprehensive analysis of your voice patterns and emotional insights</p>
        </div>
        
        <div className="flex items-center space-x-3">
          <div className="flex items-center space-x-1 bg-white/10 rounded-lg p-1">
            {(['7d', '30d', '90d', '1y'] as const).map((range) => (
              <Button
                key={range}
                variant="ghost"
                size="sm"
                onClick={() => setTimeRange(range)}
                className={`${
                  timeRange === range 
                    ? 'bg-blue-500 text-white' 
                    : 'text-blue-200 hover:text-white'
                }`}
              >
                {range}
              </Button>
            ))}
          </div>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={refreshData}
            disabled={isLoading}
            className="text-blue-300 hover:text-white"
          >
            <RefreshCw className={`w-4 h-4 mr-1 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            className="text-blue-300 hover:text-white"
          >
            <Download className="w-4 h-4 mr-1" />
            Export
          </Button>
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="bg-white/10 backdrop-blur-xl border-white/20">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-200 text-sm">Total Recordings</p>
                <p className="text-white text-2xl font-bold">{metrics.totalRecordings}</p>
                <div className="flex items-center mt-1">
                  <TrendingUp className="w-3 h-3 text-green-400 mr-1" />
                  <span className="text-green-400 text-xs">+12% vs last period</span>
                </div>
              </div>
              <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-lg flex items-center justify-center">
                <Mic className="w-5 h-5 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white/10 backdrop-blur-xl border-white/20">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-200 text-sm">Total Duration</p>
                <p className="text-white text-2xl font-bold">{formatDuration(metrics.totalDuration)}</p>
                <div className="flex items-center mt-1">
                  <TrendingUp className="w-3 h-3 text-green-400 mr-1" />
                  <span className="text-green-400 text-xs">+8% vs last period</span>
                </div>
              </div>
              <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                <Clock className="w-5 h-5 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white/10 backdrop-blur-xl border-white/20">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-200 text-sm">Voice Quality</p>
                <p className={`text-2xl font-bold ${getScoreColor(metrics.voiceQualityScore)}`}>
                  {metrics.voiceQualityScore}/10
                </p>
                <div className="flex items-center mt-1">
                  <TrendingUp className="w-3 h-3 text-green-400 mr-1" />
                  <span className="text-green-400 text-xs">+0.3 vs last period</span>
                </div>
              </div>
              <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg flex items-center justify-center">
                <Volume2 className="w-5 h-5 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white/10 backdrop-blur-xl border-white/20">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-200 text-sm">Speech Rate</p>
                <p className="text-white text-2xl font-bold">{metrics.speechRate} <span className="text-sm text-blue-200">wpm</span></p>
                <div className="flex items-center mt-1">
                  <TrendingDown className="w-3 h-3 text-yellow-400 mr-1" />
                  <span className="text-yellow-400 text-xs">-2 vs last period</span>
                </div>
              </div>
              <div className="w-10 h-10 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg flex items-center justify-center">
                <Activity className="w-5 h-5 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Analytics Tabs */}
      <Tabs defaultValue="usage" className="space-y-4">
        <TabsList className="bg-white/10 border-white/20">
          <TabsTrigger value="usage" className="data-[state=active]:bg-blue-500">
            Usage Patterns
          </TabsTrigger>
          <TabsTrigger value="mood" className="data-[state=active]:bg-blue-500">
            Mood Tracking
          </TabsTrigger>
          <TabsTrigger value="quality" className="data-[state=active]:bg-blue-500">
            Voice Quality
          </TabsTrigger>
          <TabsTrigger value="insights" className="data-[state=active]:bg-blue-500">
            AI Insights
          </TabsTrigger>
        </TabsList>

        {/* Usage Patterns Tab */}
        <TabsContent value="usage" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="bg-white/10 backdrop-blur-xl border-white/20">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <BarChart className="w-5 h-5 mr-2 text-blue-400" />
                  Daily Usage
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={usageData}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                    <XAxis 
                      dataKey="date" 
                      stroke="#9CA3AF"
                      tickFormatter={formatDate}
                    />
                    <YAxis stroke="#9CA3AF" />
                    <Tooltip 
                      contentStyle={{ 
                        backgroundColor: 'rgba(17, 24, 39, 0.9)', 
                        border: '1px solid rgba(75, 85, 99, 0.3)',
                        borderRadius: '8px'
                      }}
                    />
                    <Bar dataKey="recordings" fill="#3B82F6" radius={[4, 4, 0, 0]} />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card className="bg-white/10 backdrop-blur-xl border-white/20">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <Activity className="w-5 h-5 mr-2 text-cyan-400" />
                  Session Duration
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={usageData}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                    <XAxis 
                      dataKey="date" 
                      stroke="#9CA3AF"
                      tickFormatter={formatDate}
                    />
                    <YAxis stroke="#9CA3AF" />
                    <Tooltip 
                      contentStyle={{ 
                        backgroundColor: 'rgba(17, 24, 39, 0.9)', 
                        border: '1px solid rgba(75, 85, 99, 0.3)',
                        borderRadius: '8px'
                      }}
                    />
                    <Area 
                      type="monotone" 
                      dataKey="duration" 
                      stroke="#06B6D4" 
                      fill="url(#colorDuration)" 
                    />
                    <defs>
                      <linearGradient id="colorDuration" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="#06B6D4" stopOpacity={0.8}/>
                        <stop offset="95%" stopColor="#06B6D4" stopOpacity={0.1}/>
                      </linearGradient>
                    </defs>
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Mood Tracking Tab */}
        <TabsContent value="mood" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="bg-white/10 backdrop-blur-xl border-white/20">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <Heart className="w-5 h-5 mr-2 text-pink-400" />
                  Mood Trends
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={moodData}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                    <XAxis 
                      dataKey="date" 
                      stroke="#9CA3AF"
                      tickFormatter={formatDate}
                    />
                    <YAxis stroke="#9CA3AF" />
                    <Tooltip 
                      contentStyle={{ 
                        backgroundColor: 'rgba(17, 24, 39, 0.9)', 
                        border: '1px solid rgba(75, 85, 99, 0.3)',
                        borderRadius: '8px'
                      }}
                    />
                    <Line 
                      type="monotone" 
                      dataKey="happiness" 
                      stroke="#10B981" 
                      strokeWidth={2}
                      dot={{ fill: '#10B981', strokeWidth: 2, r: 4 }}
                    />
                    <Line 
                      type="monotone" 
                      dataKey="sadness" 
                      stroke="#EF4444" 
                      strokeWidth={2}
                      dot={{ fill: '#EF4444', strokeWidth: 2, r: 4 }}
                    />
                    <Line 
                      type="monotone" 
                      dataKey="overall" 
                      stroke="#3B82F6" 
                      strokeWidth={3}
                      dot={{ fill: '#3B82F6', strokeWidth: 2, r: 5 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card className="bg-white/10 backdrop-blur-xl border-white/20">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <Brain className="w-5 h-5 mr-2 text-purple-400" />
                  Emotion Distribution
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={emotionDistribution}
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={120}
                      paddingAngle={5}
                      dataKey="value"
                    >
                      {emotionDistribution.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip 
                      contentStyle={{ 
                        backgroundColor: 'rgba(17, 24, 39, 0.9)', 
                        border: '1px solid rgba(75, 85, 99, 0.3)',
                        borderRadius: '8px'
                      }}
                    />
                  </PieChart>
                </ResponsiveContainer>
                
                <div className="grid grid-cols-2 gap-2 mt-4">
                  {emotionDistribution.map((emotion, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <div 
                        className="w-3 h-3 rounded-full" 
                        style={{ backgroundColor: emotion.color }}
                      />
                      <span className="text-white text-sm">{emotion.name}</span>
                      <span className="text-blue-200 text-sm">{emotion.value}%</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Mood Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card className="bg-gradient-to-r from-green-500/20 to-emerald-500/20 border-green-400/30">
              <CardContent className="p-4 text-center">
                <div className="w-12 h-12 bg-green-500/30 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Heart className="w-6 h-6 text-green-400" />
                </div>
                <h3 className="text-white font-semibold mb-1">Most Positive Day</h3>
                <p className="text-green-400 text-lg font-bold">January 20th</p>
                <p className="text-green-300 text-sm">Score: 8.7/10</p>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-r from-blue-500/20 to-cyan-500/20 border-blue-400/30">
              <CardContent className="p-4 text-center">
                <div className="w-12 h-12 bg-blue-500/30 rounded-full flex items-center justify-center mx-auto mb-3">
                  <TrendingUp className="w-6 h-6 text-blue-400" />
                </div>
                <h3 className="text-white font-semibold mb-1">Average Mood</h3>
                <p className="text-blue-400 text-lg font-bold">7.4/10</p>
                <p className="text-blue-300 text-sm">+0.8 vs last week</p>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-r from-purple-500/20 to-pink-500/20 border-purple-400/30">
              <CardContent className="p-4 text-center">
                <div className="w-12 h-12 bg-purple-500/30 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Target className="w-6 h-6 text-purple-400" />
                </div>
                <h3 className="text-white font-semibold mb-1">Mood Stability</h3>
                <p className="text-purple-400 text-lg font-bold">82%</p>
                <p className="text-purple-300 text-sm">Consistent patterns</p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Voice Quality Tab */}
        <TabsContent value="quality" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="bg-white/10 backdrop-blur-xl border-white/20">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <Zap className="w-5 h-5 mr-2 text-yellow-400" />
                  Voice Quality Radar
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <RadarChart data={voiceQualityData}>
                    <PolarGrid stroke="#374151" />
                    <PolarAngleAxis dataKey="metric" tick={{ fill: '#9CA3AF', fontSize: 12 }} />
                    <PolarRadiusAxis 
                      angle={90} 
                      domain={[0, 100]} 
                      tick={{ fill: '#9CA3AF', fontSize: 10 }}
                    />
                    <Radar
                      name="Score"
                      dataKey="score"
                      stroke="#3B82F6"
                      fill="#3B82F6"
                      fillOpacity={0.3}
                      strokeWidth={2}
                    />
                  </RadarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card className="bg-white/10 backdrop-blur-xl border-white/20">
              <CardHeader>
                <CardTitle className="text-white">Quality Metrics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {voiceQualityData.map((item, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-white text-sm">{item.metric}</span>
                      <Badge className={getScoreBadgeColor(item.score / 10)}>
                        {item.score}/100
                      </Badge>
                    </div>
                    <div className="w-full bg-gray-700 rounded-full h-2">
                      <div 
                        className="bg-gradient-to-r from-blue-500 to-cyan-500 h-2 rounded-full transition-all duration-500"
                        style={{ width: `${item.score}%` }}
                      />
                    </div>
                  </div>
                ))}
                
                <div className="pt-4 border-t border-white/10">
                  <div className="flex items-center justify-between">
                    <span className="text-white font-medium">Overall Score</span>
                    <Badge className="bg-blue-500/20 text-blue-400 border-blue-400/30 text-lg px-3 py-1">
                      {metrics.voiceQualityScore}/10
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* AI Insights Tab */}
        <TabsContent value="insights" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="bg-white/10 backdrop-blur-xl border-white/20">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <Brain className="w-5 h-5 mr-2 text-purple-400" />
                  AI-Generated Insights
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="p-4 bg-blue-500/10 border border-blue-400/30 rounded-lg">
                  <div className="flex items-start space-x-3">
                    <div className="w-8 h-8 bg-blue-500/30 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                      <TrendingUp className="w-4 h-4 text-blue-400" />
                    </div>
                    <div>
                      <h4 className="text-white font-medium mb-1">Positive Trend</h4>
                      <p className="text-blue-200 text-sm">
                        Your voice quality has improved by 15% over the past month. 
                        Consistent practice is showing great results!
                      </p>
                    </div>
                  </div>
                </div>

                <div className="p-4 bg-yellow-500/10 border border-yellow-400/30 rounded-lg">
                  <div className="flex items-start space-x-3">
                    <div className="w-8 h-8 bg-yellow-500/30 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                      <Target className="w-4 h-4 text-yellow-400" />
                    </div>
                    <div>
                      <h4 className="text-white font-medium mb-1">Recommendation</h4>
                      <p className="text-yellow-200 text-sm">
                        Consider slowing down your speech rate slightly. 
                        Your current pace of 145 WPM is above optimal range.
                      </p>
                    </div>
                  </div>
                </div>

                <div className="p-4 bg-green-500/10 border border-green-400/30 rounded-lg">
                  <div className="flex items-start space-x-3">
                    <div className="w-8 h-8 bg-green-500/30 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                      <Heart className="w-4 h-4 text-green-400" />
                    </div>
                    <div>
                      <h4 className="text-white font-medium mb-1">Mood Pattern</h4>
                      <p className="text-green-200 text-sm">
                        Your mood tends to be most positive on weekends. 
                        Consider scheduling important conversations during these times.
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white/10 backdrop-blur-xl border-white/20">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <Calendar className="w-5 h-5 mr-2 text-blue-400" />
                  Performance Goals
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                    <div>
                      <h4 className="text-white font-medium">Daily Recording Goal</h4>
                      <p className="text-blue-200 text-sm">Target: 5 recordings per day</p>
                    </div>
                    <div className="text-right">
                      <p className="text-green-400 font-bold">4/5</p>
                      <p className="text-green-300 text-xs">80% complete</p>
                    </div>
                  </div>

                  <div className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                    <div>
                      <h4 className="text-white font-medium">Voice Quality Target</h4>
                      <p className="text-blue-200 text-sm">Target: 9.0/10 average</p>
                    </div>
                    <div className="text-right">
                      <p className="text-yellow-400 font-bold">8.7/10</p>
                      <p className="text-yellow-300 text-xs">97% complete</p>
                    </div>
                  </div>

                  <div className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                    <div>
                      <h4 className="text-white font-medium">Weekly Duration</h4>
                      <p className="text-blue-200 text-sm">Target: 2 hours per week</p>
                    </div>
                    <div className="text-right">
                      <p className="text-blue-400 font-bold">1.8h</p>
                      <p className="text-blue-300 text-xs">90% complete</p>
                    </div>
                  </div>
                </div>

                <div className="pt-4 border-t border-white/10">
                  <Button className="w-full bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600">
                    Set New Goals
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default VoiceAnalytics;
