#!/bin/bash

# VoiceHub Deployment Script
# This script handles the complete deployment of VoiceHub platform

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="voicehub"
DOCKER_COMPOSE_FILE="docker/docker-compose.yml"
PROD_COMPOSE_FILE="docker/docker-compose.prod.yml"
ENV_FILE="docker/.env"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_requirements() {
    log_info "Checking system requirements..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    # Check if Docker daemon is running
    if ! docker info &> /dev/null; then
        log_error "Docker daemon is not running. Please start Docker first."
        exit 1
    fi
    
    log_success "System requirements check passed"
}

setup_environment() {
    log_info "Setting up environment..."
    
    # Create .env file if it doesn't exist
    if [ ! -f "$ENV_FILE" ]; then
        log_warning ".env file not found. Creating from template..."
        cp docker/.env.example "$ENV_FILE"
        log_warning "Please edit $ENV_FILE with your configuration before proceeding."
        read -p "Press Enter to continue after editing .env file..."
    fi
    
    # Create necessary directories
    mkdir -p logs
    mkdir -p uploads
    mkdir -p backups
    
    log_success "Environment setup completed"
}

build_images() {
    log_info "Building Docker images..."
    
    # Build backend image
    log_info "Building backend image..."
    docker build -t voicehub-backend:latest backend/voicehub-backend/
    
    # Build frontend image
    log_info "Building frontend image..."
    docker build -t voicehub-frontend:latest frontend/voicehub-ui/
    
    log_success "Docker images built successfully"
}

deploy_development() {
    log_info "Deploying development environment..."
    
    # Stop existing containers
    docker-compose -f "$DOCKER_COMPOSE_FILE" down
    
    # Start services
    docker-compose -f "$DOCKER_COMPOSE_FILE" up -d
    
    # Wait for services to be ready
    log_info "Waiting for services to be ready..."
    sleep 30
    
    # Check service health
    check_services_health
    
    log_success "Development environment deployed successfully"
    log_info "Frontend: http://localhost:3000"
    log_info "Backend API: http://localhost:8080/api"
    log_info "Database: localhost:5432"
}

deploy_production() {
    log_info "Deploying production environment..."
    
    # Stop existing containers
    docker-compose -f "$PROD_COMPOSE_FILE" down
    
    # Start services
    docker-compose -f "$PROD_COMPOSE_FILE" up -d
    
    # Wait for services to be ready
    log_info "Waiting for services to be ready..."
    sleep 60
    
    # Check service health
    check_services_health
    
    log_success "Production environment deployed successfully"
    log_info "Application: http://localhost"
    log_info "Monitoring: http://localhost:3001 (Grafana)"
    log_info "Metrics: http://localhost:9090 (Prometheus)"
}

check_services_health() {
    log_info "Checking service health..."
    
    # Check backend health
    for i in {1..30}; do
        if curl -f http://localhost:8080/api/health &> /dev/null; then
            log_success "Backend service is healthy"
            break
        fi
        if [ $i -eq 30 ]; then
            log_error "Backend service health check failed"
            return 1
        fi
        sleep 2
    done
    
    # Check frontend health
    for i in {1..30}; do
        if curl -f http://localhost:3000 &> /dev/null || curl -f http://localhost &> /dev/null; then
            log_success "Frontend service is healthy"
            break
        fi
        if [ $i -eq 30 ]; then
            log_error "Frontend service health check failed"
            return 1
        fi
        sleep 2
    done
    
    log_success "All services are healthy"
}

backup_data() {
    log_info "Creating backup..."
    
    BACKUP_DIR="backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    # Backup database
    docker exec voicehub-postgres pg_dump -U voicehub voicehub > "$BACKUP_DIR/database.sql"
    
    # Backup uploads
    docker cp voicehub-backend:/app/uploads "$BACKUP_DIR/"
    
    # Create archive
    tar -czf "$BACKUP_DIR.tar.gz" -C backups "$(basename "$BACKUP_DIR")"
    rm -rf "$BACKUP_DIR"
    
    log_success "Backup created: $BACKUP_DIR.tar.gz"
}

show_logs() {
    local service=$1
    if [ -z "$service" ]; then
        docker-compose -f "$DOCKER_COMPOSE_FILE" logs -f
    else
        docker-compose -f "$DOCKER_COMPOSE_FILE" logs -f "$service"
    fi
}

stop_services() {
    log_info "Stopping services..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" down
    docker-compose -f "$PROD_COMPOSE_FILE" down
    log_success "Services stopped"
}

show_status() {
    log_info "Service Status:"
    docker-compose -f "$DOCKER_COMPOSE_FILE" ps
}

show_help() {
    echo "VoiceHub Deployment Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  dev         Deploy development environment"
    echo "  prod        Deploy production environment"
    echo "  build       Build Docker images"
    echo "  backup      Create data backup"
    echo "  logs [svc]  Show logs (optionally for specific service)"
    echo "  status      Show service status"
    echo "  stop        Stop all services"
    echo "  help        Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 dev              # Deploy development environment"
    echo "  $0 prod             # Deploy production environment"
    echo "  $0 logs backend     # Show backend logs"
    echo "  $0 backup           # Create backup"
}

# Main script logic
case "${1:-help}" in
    "dev")
        check_requirements
        setup_environment
        build_images
        deploy_development
        ;;
    "prod")
        check_requirements
        setup_environment
        build_images
        deploy_production
        ;;
    "build")
        check_requirements
        build_images
        ;;
    "backup")
        backup_data
        ;;
    "logs")
        show_logs "$2"
        ;;
    "status")
        show_status
        ;;
    "stop")
        stop_services
        ;;
    "help"|*)
        show_help
        ;;
esac