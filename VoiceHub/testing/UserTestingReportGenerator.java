package com.voicehub.testing;

import com.voicehub.backend.service.VoiceOptimizationService.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.FileWriter;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Generates comprehensive user testing reports for voice recognition optimization
 */
@Component
@Slf4j
public class UserTestingReportGenerator {

    private static final DateTimeFormatter TIMESTAMP_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final DateTimeFormatter FILE_DATE_FORMAT = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss");

    /**
     * Generate comprehensive testing report
     */
    public void generateTestingReport(VoiceBenchmarkResult benchmarkResult, 
                                    List<UserTestingSession> userSessions,
                                    String outputPath) {
        try {
            String timestamp = LocalDateTime.now().format(FILE_DATE_FORMAT);
            String fileName = String.format("%s/VoiceHub_Testing_Report_%s.md", outputPath, timestamp);
            
            try (FileWriter writer = new FileWriter(fileName)) {
                writeReportHeader(writer);
                writeExecutiveSummary(writer, benchmarkResult, userSessions);
                writeTestingMethodology(writer);
                writeBenchmarkResults(writer, benchmarkResult);
                writeUserTestingResults(writer, userSessions);
                writeOptimizationRecommendations(writer, benchmarkResult, userSessions);
                writeConclusions(writer);
                writeAppendices(writer, benchmarkResult);
            }
            
            log.info("Testing report generated successfully: {}", fileName);
            
        } catch (IOException e) {
            log.error("Error generating testing report", e);
        }
    }

    /**
     * Write report header
     */
    private void writeReportHeader(FileWriter writer) throws IOException {
        writer.write("# VoiceHub Voice Recognition Testing Report\n\n");
        writer.write("**Generated:** " + LocalDateTime.now().format(TIMESTAMP_FORMAT) + "\n\n");
        writer.write("**Version:** 1.0\n\n");
        writer.write("**Testing Period:** " + getTestingPeriod() + "\n\n");
        writer.write("---\n\n");
    }

    /**
     * Write executive summary
     */
    private void writeExecutiveSummary(FileWriter writer, VoiceBenchmarkResult benchmarkResult, 
                                     List<UserTestingSession> userSessions) throws IOException {
        writer.write("## Executive Summary\n\n");
        
        writer.write("### Key Findings\n\n");
        writer.write(String.format("- **Overall Voice Recognition Accuracy:** %.1f%%\n", 
            benchmarkResult.getOverallAccuracy() * 100));
        writer.write(String.format("- **Average Response Latency:** %d ms\n", 
            benchmarkResult.getOverallLatency()));
        writer.write(String.format("- **Total Test Cases:** %d\n", 
            benchmarkResult.getTotalTestCases()));
        writer.write(String.format("- **Passed Tests:** %d (%.1f%%)\n", 
            benchmarkResult.getPassedTests(),
            (double) benchmarkResult.getPassedTests() / benchmarkResult.getTotalTestCases() * 100));
        writer.write(String.format("- **User Satisfaction:** %.1f/5.0\n", 
            calculateAverageUserSatisfaction(userSessions)));
        writer.write("\n");

        writer.write("### Performance Status\n\n");
        String performanceStatus = getPerformanceStatus(benchmarkResult);
        writer.write("**Status:** " + performanceStatus + "\n\n");
        
        if (benchmarkResult.getOverallAccuracy() >= 0.95) {
            writer.write("✅ **Excellent Performance** - Voice recognition accuracy exceeds target threshold\n\n");
        } else if (benchmarkResult.getOverallAccuracy() >= 0.90) {
            writer.write("✅ **Good Performance** - Voice recognition accuracy meets acceptable standards\n\n");
        } else if (benchmarkResult.getOverallAccuracy() >= 0.80) {
            writer.write("⚠️ **Acceptable Performance** - Voice recognition accuracy needs improvement\n\n");
        } else {
            writer.write("❌ **Poor Performance** - Voice recognition accuracy requires significant optimization\n\n");
        }
    }

    /**
     * Write testing methodology
     */
    private void writeTestingMethodology(FileWriter writer) throws IOException {
        writer.write("## Testing Methodology\n\n");
        
        writer.write("### Test Categories\n\n");
        writer.write("1. **Standard Speech Tests**\n");
        writer.write("   - Clear speech in quiet environments\n");
        writer.write("   - Common vocabulary and phrases\n");
        writer.write("   - Target accuracy: >90%\n\n");
        
        writer.write("2. **Challenging Conditions Tests**\n");
        writer.write("   - Background noise scenarios\n");
        writer.write("   - Fast speech patterns\n");
        writer.write("   - Technical vocabulary\n");
        writer.write("   - Target accuracy: >75%\n\n");
        
        writer.write("3. **Edge Case Tests**\n");
        writer.write("   - Very short utterances\n");
        writer.write("   - Long sentences\n");
        writer.write("   - Filler words and hesitations\n");
        writer.write("   - Target accuracy: >50%\n\n");
        
        writer.write("### Performance Metrics\n\n");
        writer.write("- **Word Accuracy:** Percentage of correctly recognized words\n");
        writer.write("- **Character Accuracy:** Character-level recognition accuracy\n");
        writer.write("- **Semantic Similarity:** AI-powered semantic understanding\n");
        writer.write("- **Response Latency:** Time from speech end to transcription\n");
        writer.write("- **Confidence Score:** System confidence in recognition\n\n");
    }

    /**
     * Write benchmark results
     */
    private void writeBenchmarkResults(FileWriter writer, VoiceBenchmarkResult benchmarkResult) throws IOException {
        writer.write("## Benchmark Test Results\n\n");
        
        writer.write("### Overall Performance\n\n");
        writer.write("| Metric | Value | Status |\n");
        writer.write("|--------|-------|--------|\n");
        writer.write(String.format("| Overall Accuracy | %.1f%% | %s |\n", 
            benchmarkResult.getOverallAccuracy() * 100,
            benchmarkResult.getOverallAccuracy() >= 0.90 ? "✅ Pass" : "❌ Fail"));
        writer.write(String.format("| Average Latency | %d ms | %s |\n", 
            benchmarkResult.getOverallLatency(),
            benchmarkResult.getOverallLatency() <= 300 ? "✅ Pass" : "❌ Fail"));
        writer.write(String.format("| Total Test Cases | %d | - |\n", 
            benchmarkResult.getTotalTestCases()));
        writer.write(String.format("| Passed Tests | %d | %.1f%% |\n", 
            benchmarkResult.getPassedTests(),
            (double) benchmarkResult.getPassedTests() / benchmarkResult.getTotalTestCases() * 100));
        writer.write(String.format("| Failed Tests | %d | %.1f%% |\n", 
            benchmarkResult.getFailedTests(),
            (double) benchmarkResult.getFailedTests() / benchmarkResult.getTotalTestCases() * 100));
        writer.write("\n");

        writer.write("### Category Performance\n\n");
        writer.write("| Category | Accuracy | Latency | Status |\n");
        writer.write("|----------|----------|---------|--------|\n");
        
        for (Map.Entry<String, Double> entry : benchmarkResult.getCategoryAccuracy().entrySet()) {
            String category = entry.getKey();
            Double accuracy = entry.getValue();
            Long latency = benchmarkResult.getCategoryLatency().get(category);
            String status = accuracy >= getCategoryThreshold(category) ? "✅ Pass" : "❌ Fail";
            
            writer.write(String.format("| %s | %.1f%% | %d ms | %s |\n", 
                capitalize(category), accuracy * 100, latency, status));
        }
        writer.write("\n");
    }

    /**
     * Write user testing results
     */
    private void writeUserTestingResults(FileWriter writer, List<UserTestingSession> userSessions) throws IOException {
        writer.write("## User Testing Results\n\n");
        
        writer.write("### User Demographics\n\n");
        Map<String, Long> ageGroups = userSessions.stream()
            .collect(Collectors.groupingBy(this::getAgeGroup, Collectors.counting()));
        
        writer.write("| Age Group | Count | Percentage |\n");
        writer.write("|-----------|-------|------------|\n");
        for (Map.Entry<String, Long> entry : ageGroups.entrySet()) {
            double percentage = (double) entry.getValue() / userSessions.size() * 100;
            writer.write(String.format("| %s | %d | %.1f%% |\n", 
                entry.getKey(), entry.getValue(), percentage));
        }
        writer.write("\n");

        writer.write("### User Satisfaction Metrics\n\n");
        double avgSatisfaction = calculateAverageUserSatisfaction(userSessions);
        double avgTaskCompletion = calculateAverageTaskCompletion(userSessions);
        double avgFeatureAdoption = calculateAverageFeatureAdoption(userSessions);
        
        writer.write("| Metric | Score | Target | Status |\n");
        writer.write("|--------|-------|--------|--------|\n");
        writer.write(String.format("| User Satisfaction | %.1f/5.0 | >4.0 | %s |\n", 
            avgSatisfaction, avgSatisfaction >= 4.0 ? "✅ Pass" : "❌ Fail"));
        writer.write(String.format("| Task Completion Rate | %.1f%% | >90%% | %s |\n", 
            avgTaskCompletion, avgTaskCompletion >= 90.0 ? "✅ Pass" : "❌ Fail"));
        writer.write(String.format("| Feature Adoption | %.1f%% | >80%% | %s |\n", 
            avgFeatureAdoption, avgFeatureAdoption >= 80.0 ? "✅ Pass" : "❌ Fail"));
        writer.write("\n");

        writer.write("### Common User Feedback\n\n");
        writeUserFeedbackSummary(writer, userSessions);
    }

    /**
     * Write optimization recommendations
     */
    private void writeOptimizationRecommendations(FileWriter writer, VoiceBenchmarkResult benchmarkResult, 
                                                List<UserTestingSession> userSessions) throws IOException {
        writer.write("## Optimization Recommendations\n\n");
        
        writer.write("### Technical Optimizations\n\n");
        
        if (benchmarkResult.getOverallAccuracy() < 0.90) {
            writer.write("#### Voice Recognition Accuracy\n");
            writer.write("- **Priority:** High\n");
            writer.write("- **Current:** " + String.format("%.1f%%", benchmarkResult.getOverallAccuracy() * 100) + "\n");
            writer.write("- **Target:** >90%\n");
            writer.write("- **Actions:**\n");
            writer.write("  - Implement advanced noise reduction algorithms\n");
            writer.write("  - Fine-tune acoustic models for specific use cases\n");
            writer.write("  - Add context-aware recognition enhancement\n");
            writer.write("  - Implement user-specific voice adaptation\n\n");
        }
        
        if (benchmarkResult.getOverallLatency() > 300) {
            writer.write("#### Response Latency\n");
            writer.write("- **Priority:** Medium\n");
            writer.write("- **Current:** " + benchmarkResult.getOverallLatency() + " ms\n");
            writer.write("- **Target:** <200 ms\n");
            writer.write("- **Actions:**\n");
            writer.write("  - Optimize audio processing pipeline\n");
            writer.write("  - Implement edge computing for local processing\n");
            writer.write("  - Add streaming recognition capabilities\n");
            writer.write("  - Optimize network communication protocols\n\n");
        }

        writer.write("### User Experience Improvements\n\n");
        
        double avgSatisfaction = calculateAverageUserSatisfaction(userSessions);
        if (avgSatisfaction < 4.0) {
            writer.write("#### User Satisfaction\n");
            writer.write("- **Priority:** High\n");
            writer.write("- **Current:** " + String.format("%.1f/5.0", avgSatisfaction) + "\n");
            writer.write("- **Target:** >4.0/5.0\n");
            writer.write("- **Actions:**\n");
            writer.write("  - Improve error handling and recovery mechanisms\n");
            writer.write("  - Add better visual feedback for voice interactions\n");
            writer.write("  - Implement progressive disclosure for complex features\n");
            writer.write("  - Enhance onboarding and user education\n\n");
        }

        writer.write("### Feature Enhancements\n\n");
        writer.write("1. **Multi-language Support**\n");
        writer.write("   - Add support for additional languages and dialects\n");
        writer.write("   - Implement automatic language detection\n\n");
        
        writer.write("2. **Accessibility Improvements**\n");
        writer.write("   - Enhanced support for users with speech impairments\n");
        writer.write("   - Better accommodation for different accents\n\n");
        
        writer.write("3. **Advanced Analytics**\n");
        writer.write("   - Real-time performance monitoring\n");
        writer.write("   - Predictive accuracy optimization\n\n");
    }

    /**
     * Write conclusions
     */
    private void writeConclusions(FileWriter writer) throws IOException {
        writer.write("## Conclusions\n\n");
        
        writer.write("### Summary\n\n");
        writer.write("The VoiceHub voice recognition testing has provided comprehensive insights into the system's ");
        writer.write("performance across various scenarios and user conditions. The testing methodology covered ");
        writer.write("standard use cases, challenging conditions, and edge cases to ensure robust evaluation.\n\n");
        
        writer.write("### Key Achievements\n\n");
        writer.write("- Comprehensive testing framework implementation\n");
        writer.write("- Automated performance benchmarking system\n");
        writer.write("- User-centered testing approach\n");
        writer.write("- Data-driven optimization recommendations\n\n");
        
        writer.write("### Next Steps\n\n");
        writer.write("1. **Immediate Actions (1-2 weeks)**\n");
        writer.write("   - Address critical performance issues\n");
        writer.write("   - Implement high-priority optimizations\n\n");
        
        writer.write("2. **Short-term Goals (1-3 months)**\n");
        writer.write("   - Deploy optimization improvements\n");
        writer.write("   - Conduct follow-up testing\n");
        writer.write("   - Gather additional user feedback\n\n");
        
        writer.write("3. **Long-term Vision (3-12 months)**\n");
        writer.write("   - Advanced AI-powered optimizations\n");
        writer.write("   - Multi-language support expansion\n");
        writer.write("   - Enterprise-grade scalability\n\n");
    }

    /**
     * Write appendices
     */
    private void writeAppendices(FileWriter writer, VoiceBenchmarkResult benchmarkResult) throws IOException {
        writer.write("## Appendices\n\n");
        
        writer.write("### Appendix A: Detailed Test Results\n\n");
        writer.write("```json\n");
        writer.write("{\n");
        writer.write("  \"totalTestCases\": " + benchmarkResult.getTotalTestCases() + ",\n");
        writer.write("  \"overallAccuracy\": " + benchmarkResult.getOverallAccuracy() + ",\n");
        writer.write("  \"overallLatency\": " + benchmarkResult.getOverallLatency() + ",\n");
        writer.write("  \"passedTests\": " + benchmarkResult.getPassedTests() + ",\n");
        writer.write("  \"failedTests\": " + benchmarkResult.getFailedTests() + ",\n");
        writer.write("  \"categoryResults\": {\n");
        
        List<String> categories = new ArrayList<>(benchmarkResult.getCategoryAccuracy().keySet());
        for (int i = 0; i < categories.size(); i++) {
            String category = categories.get(i);
            Double accuracy = benchmarkResult.getCategoryAccuracy().get(category);
            Long latency = benchmarkResult.getCategoryLatency().get(category);
            
            writer.write("    \"" + category + "\": {\n");
            writer.write("      \"accuracy\": " + accuracy + ",\n");
            writer.write("      \"latency\": " + latency + "\n");
            writer.write("    }" + (i < categories.size() - 1 ? "," : "") + "\n");
        }
        
        writer.write("  }\n");
        writer.write("}\n");
        writer.write("```\n\n");
        
        writer.write("### Appendix B: Testing Environment\n\n");
        writer.write("- **Testing Framework:** JUnit 5 with Spring Boot\n");
        writer.write("- **Audio Processing:** Web Audio API with custom optimization\n");
        writer.write("- **AI Integration:** OpenAI GPT-3.5 for semantic analysis\n");
        writer.write("- **Database:** PostgreSQL with Redis caching\n");
        writer.write("- **Deployment:** Docker containerization\n\n");
        
        writer.write("### Appendix C: Performance Thresholds\n\n");
        writer.write("| Category | Accuracy Threshold | Latency Threshold |\n");
        writer.write("|----------|-------------------|-------------------|\n");
        writer.write("| Standard | >90% | <200ms |\n");
        writer.write("| Technical | >80% | <300ms |\n");
        writer.write("| Noisy | >70% | <400ms |\n");
        writer.write("| Fast Speech | >75% | <250ms |\n");
        writer.write("| Edge Cases | >50% | <500ms |\n\n");
    }

    // Helper methods
    
    private String getTestingPeriod() {
        return LocalDateTime.now().minusWeeks(4).format(TIMESTAMP_FORMAT) + " to " + 
               LocalDateTime.now().format(TIMESTAMP_FORMAT);
    }
    
    private String getPerformanceStatus(VoiceBenchmarkResult result) {
        if (result.getOverallAccuracy() >= 0.95 && result.getOverallLatency() <= 200) {
            return "🟢 Excellent";
        } else if (result.getOverallAccuracy() >= 0.90 && result.getOverallLatency() <= 300) {
            return "🟡 Good";
        } else if (result.getOverallAccuracy() >= 0.80) {
            return "🟠 Acceptable";
        } else {
            return "🔴 Needs Improvement";
        }
    }
    
    private double calculateAverageUserSatisfaction(List<UserTestingSession> sessions) {
        return sessions.stream()
            .mapToDouble(UserTestingSession::getSatisfactionRating)
            .average()
            .orElse(0.0);
    }
    
    private double calculateAverageTaskCompletion(List<UserTestingSession> sessions) {
        return sessions.stream()
            .mapToDouble(UserTestingSession::getTaskCompletionRate)
            .average()
            .orElse(0.0);
    }
    
    private double calculateAverageFeatureAdoption(List<UserTestingSession> sessions) {
        return sessions.stream()
            .mapToDouble(UserTestingSession::getFeatureAdoptionRate)
            .average()
            .orElse(0.0);
    }
    
    private String getAgeGroup(UserTestingSession session) {
        int age = session.getUserAge();
        if (age < 25) return "18-24";
        else if (age < 35) return "25-34";
        else if (age < 45) return "35-44";
        else if (age < 55) return "45-54";
        else return "55+";
    }
    
    private void writeUserFeedbackSummary(FileWriter writer, List<UserTestingSession> sessions) throws IOException {
        Map<String, Long> feedbackCategories = sessions.stream()
            .flatMap(s -> s.getFeedbackComments().stream())
            .collect(Collectors.groupingBy(this::categorizeFeedback, Collectors.counting()));
        
        writer.write("| Feedback Category | Count | Percentage |\n");
        writer.write("|-------------------|-------|------------|\n");
        
        for (Map.Entry<String, Long> entry : feedbackCategories.entrySet()) {
            double percentage = (double) entry.getValue() / sessions.size() * 100;
            writer.write(String.format("| %s | %d | %.1f%% |\n", 
                entry.getKey(), entry.getValue(), percentage));
        }
        writer.write("\n");
    }
    
    private String categorizeFeedback(String feedback) {
        String lower = feedback.toLowerCase();
        if (lower.contains("accuracy") || lower.contains("recognition")) {
            return "Recognition Accuracy";
        } else if (lower.contains("speed") || lower.contains("latency") || lower.contains("slow")) {
            return "Response Speed";
        } else if (lower.contains("interface") || lower.contains("ui") || lower.contains("design")) {
            return "User Interface";
        } else if (lower.contains("feature") || lower.contains("functionality")) {
            return "Features";
        } else {
            return "General";
        }
    }
    
    private double getCategoryThreshold(String category) {
        switch (category.toLowerCase()) {
            case "standard": return 0.90;
            case "technical": return 0.80;
            case "noisy": return 0.70;
            case "fast-speech": return 0.75;
            default: return 0.80;
        }
    }
    
    private String capitalize(String str) {
        if (str == null || str.isEmpty()) return str;
        return str.substring(0, 1).toUpperCase() + str.substring(1).toLowerCase().replace("-", " ");
    }

    // Data classes for user testing sessions
    
    @lombok.Data
    @lombok.Builder
    public static class UserTestingSession {
        private String userId;
        private int userAge;
        private String userDemographic;
        private double satisfactionRating;
        private double taskCompletionRate;
        private double featureAdoptionRate;
        private List<String> feedbackComments;
        private LocalDateTime sessionDate;
    }
}