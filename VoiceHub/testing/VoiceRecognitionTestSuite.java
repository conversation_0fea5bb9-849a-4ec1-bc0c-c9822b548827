package com.voicehub.testing;

import com.voicehub.backend.service.VoiceOptimizationService;
import com.voicehub.backend.service.VoiceOptimizationService.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;
import org.junit.jupiter.api.*;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * Comprehensive test suite for voice recognition accuracy and optimization
 */
@SpringBootTest
@SpringJUnitConfig
@Slf4j
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class VoiceRecognitionTestSuite {

    @Autowired
    private VoiceOptimizationService voiceOptimizationService;

    private static final String TEST_AUDIO_DIR = "src/test/resources/audio/";
    private static final double ACCURACY_THRESHOLD = 0.90;
    private static final long LATENCY_THRESHOLD = 300L; // milliseconds

    private List<VoiceTestCase> standardTestCases;
    private List<VoiceTestCase> challengingTestCases;
    private List<VoiceTestCase> edgeCaseTestCases;

    @BeforeEach
    void setUp() throws IOException {
        setupTestCases();
    }

    /**
     * Test basic voice recognition accuracy with clear speech
     */
    @Test
    @Order(1)
    @DisplayName("Basic Voice Recognition Accuracy Test")
    void testBasicVoiceRecognitionAccuracy() throws Exception {
        log.info("Starting basic voice recognition accuracy test");

        for (VoiceTestCase testCase : standardTestCases) {
            CompletableFuture<VoiceAccuracyTestResult> future = voiceOptimizationService
                .testVoiceAccuracy(testCase.getAudioFile(), testCase.getExpectedText(), 
                    testCase.getOptimizationSettings());

            VoiceAccuracyTestResult result = future.get(10, TimeUnit.SECONDS);

            // Assert accuracy meets threshold
            Assertions.assertTrue(result.getWordAccuracy() >= ACCURACY_THRESHOLD,
                String.format("Word accuracy %.2f below threshold %.2f for text: %s", 
                    result.getWordAccuracy(), ACCURACY_THRESHOLD, testCase.getExpectedText()));

            // Assert latency is acceptable
            Assertions.assertTrue(result.getLatency() <= LATENCY_THRESHOLD,
                String.format("Latency %dms exceeds threshold %dms", 
                    result.getLatency(), LATENCY_THRESHOLD));

            log.info("Test case passed - Expected: '{}', Recognized: '{}', Accuracy: {:.2f}, Latency: {}ms",
                testCase.getExpectedText(), result.getRecognizedText(), 
                result.getWordAccuracy(), result.getLatency());
        }
    }

    /**
     * Test voice recognition with challenging conditions
     */
    @Test
    @Order(2)
    @DisplayName("Challenging Conditions Voice Recognition Test")
    void testChallengingConditionsVoiceRecognition() throws Exception {
        log.info("Starting challenging conditions voice recognition test");

        double totalAccuracy = 0.0;
        int passedTests = 0;

        for (VoiceTestCase testCase : challengingTestCases) {
            CompletableFuture<VoiceAccuracyTestResult> future = voiceOptimizationService
                .testVoiceAccuracy(testCase.getAudioFile(), testCase.getExpectedText(), 
                    testCase.getOptimizationSettings());

            VoiceAccuracyTestResult result = future.get(15, TimeUnit.SECONDS);
            totalAccuracy += result.getWordAccuracy();

            // Lower threshold for challenging conditions
            if (result.getWordAccuracy() >= 0.75) {
                passedTests++;
            }

            log.info("Challenging test - Category: {}, Expected: '{}', Recognized: '{}', Accuracy: {:.2f}",
                testCase.getCategory(), testCase.getExpectedText(), 
                result.getRecognizedText(), result.getWordAccuracy());
        }

        double averageAccuracy = totalAccuracy / challengingTestCases.size();
        double passRate = (double) passedTests / challengingTestCases.size();

        // Assert that at least 70% of challenging tests pass
        Assertions.assertTrue(passRate >= 0.70,
            String.format("Pass rate %.2f below threshold 0.70", passRate));

        log.info("Challenging conditions test completed - Average accuracy: {:.2f}, Pass rate: {:.2f}",
            averageAccuracy, passRate);
    }

    /**
     * Test voice recognition performance under load
     */
    @Test
    @Order(3)
    @DisplayName("Performance Under Load Test")
    void testPerformanceUnderLoad() throws Exception {
        log.info("Starting performance under load test");

        int concurrentRequests = 10;
        List<CompletableFuture<VoiceAccuracyTestResult>> futures = new ArrayList<>();

        // Submit concurrent requests
        for (int i = 0; i < concurrentRequests; i++) {
            VoiceTestCase testCase = standardTestCases.get(i % standardTestCases.size());
            CompletableFuture<VoiceAccuracyTestResult> future = voiceOptimizationService
                .testVoiceAccuracy(testCase.getAudioFile(), testCase.getExpectedText(), 
                    testCase.getOptimizationSettings());
            futures.add(future);
        }

        // Wait for all requests to complete
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(
            futures.toArray(new CompletableFuture[0]));
        allFutures.get(30, TimeUnit.SECONDS);

        // Analyze results
        double totalAccuracy = 0.0;
        long totalLatency = 0L;
        int successfulRequests = 0;

        for (CompletableFuture<VoiceAccuracyTestResult> future : futures) {
            try {
                VoiceAccuracyTestResult result = future.get();
                totalAccuracy += result.getWordAccuracy();
                totalLatency += result.getLatency();
                successfulRequests++;
            } catch (Exception e) {
                log.error("Request failed under load", e);
            }
        }

        double averageAccuracy = totalAccuracy / successfulRequests;
        long averageLatency = totalLatency / successfulRequests;

        // Assert performance doesn't degrade significantly under load
        Assertions.assertTrue(averageAccuracy >= ACCURACY_THRESHOLD - 0.05,
            String.format("Average accuracy under load %.2f below acceptable threshold", averageAccuracy));

        Assertions.assertTrue(averageLatency <= LATENCY_THRESHOLD * 2,
            String.format("Average latency under load %dms exceeds acceptable threshold", averageLatency));

        log.info("Performance under load test completed - Successful requests: {}/{}, " +
            "Average accuracy: {:.2f}, Average latency: {}ms",
            successfulRequests, concurrentRequests, averageAccuracy, averageLatency);
    }

    /**
     * Test voice recognition with different optimization settings
     */
    @ParameterizedTest
    @ValueSource(doubles = {0.6, 0.7, 0.8, 0.9})
    @Order(4)
    @DisplayName("Optimization Settings Impact Test")
    void testOptimizationSettingsImpact(double sensitivity) throws Exception {
        log.info("Testing optimization settings with sensitivity: {}", sensitivity);

        VoiceOptimizationSettings settings = VoiceOptimizationSettings.builder()
            .recognitionSensitivity(sensitivity)
            .speechTimeout(5000L)
            .noiseReduction(sensitivity < 0.8)
            .echoCancellation(sensitivity < 0.8)
            .languageModel("general")
            .vocabularyExpansion(false)
            .streamingRecognition(false)
            .localProcessing(false)
            .build();

        VoiceTestCase testCase = standardTestCases.get(0);
        CompletableFuture<VoiceAccuracyTestResult> future = voiceOptimizationService
            .testVoiceAccuracy(testCase.getAudioFile(), testCase.getExpectedText(), settings);

        VoiceAccuracyTestResult result = future.get(10, TimeUnit.SECONDS);

        // Log results for analysis
        log.info("Sensitivity {}: Accuracy = {:.2f}, Latency = {}ms, Confidence = {:.2f}",
            sensitivity, result.getWordAccuracy(), result.getLatency(), result.getConfidenceScore());

        // Basic validation - results should be reasonable
        Assertions.assertTrue(result.getWordAccuracy() >= 0.5,
            "Accuracy too low even with adjusted settings");
        Assertions.assertTrue(result.getLatency() > 0,
            "Invalid latency measurement");
    }

    /**
     * Test comprehensive voice recognition benchmark
     */
    @Test
    @Order(5)
    @DisplayName("Comprehensive Benchmark Test")
    void testComprehensiveBenchmark() throws Exception {
        log.info("Starting comprehensive voice recognition benchmark");

        List<VoiceTestCase> allTestCases = new ArrayList<>();
        allTestCases.addAll(standardTestCases);
        allTestCases.addAll(challengingTestCases);
        allTestCases.addAll(edgeCaseTestCases);

        CompletableFuture<VoiceBenchmarkResult> future = voiceOptimizationService
            .performBenchmark(allTestCases);

        VoiceBenchmarkResult benchmarkResult = future.get(60, TimeUnit.SECONDS);

        // Assert overall performance
        Assertions.assertTrue(benchmarkResult.getOverallAccuracy() >= 0.85,
            String.format("Overall benchmark accuracy %.2f below threshold 0.85", 
                benchmarkResult.getOverallAccuracy()));

        Assertions.assertTrue(benchmarkResult.getOverallLatency() <= LATENCY_THRESHOLD * 1.5,
            String.format("Overall benchmark latency %dms exceeds threshold", 
                benchmarkResult.getOverallLatency()));

        // Assert category performance
        for (Map.Entry<String, Double> entry : benchmarkResult.getCategoryAccuracy().entrySet()) {
            String category = entry.getKey();
            Double accuracy = entry.getValue();
            
            double categoryThreshold = getCategoryThreshold(category);
            Assertions.assertTrue(accuracy >= categoryThreshold,
                String.format("Category '%s' accuracy %.2f below threshold %.2f", 
                    category, accuracy, categoryThreshold));
        }

        log.info("Comprehensive benchmark completed - Overall accuracy: {:.2f}, " +
            "Overall latency: {}ms, Passed: {}/{}, Failed: {}",
            benchmarkResult.getOverallAccuracy(), benchmarkResult.getOverallLatency(),
            benchmarkResult.getPassedTests(), benchmarkResult.getTotalTestCases(),
            benchmarkResult.getFailedTests());
    }

    /**
     * Test edge cases and error handling
     */
    @Test
    @Order(6)
    @DisplayName("Edge Cases and Error Handling Test")
    void testEdgeCasesAndErrorHandling() throws Exception {
        log.info("Starting edge cases and error handling test");

        for (VoiceTestCase testCase : edgeCaseTestCases) {
            try {
                CompletableFuture<VoiceAccuracyTestResult> future = voiceOptimizationService
                    .testVoiceAccuracy(testCase.getAudioFile(), testCase.getExpectedText(), 
                        testCase.getOptimizationSettings());

                VoiceAccuracyTestResult result = future.get(20, TimeUnit.SECONDS);

                // Edge cases should still produce valid results, even if accuracy is low
                Assertions.assertNotNull(result.getRecognizedText(),
                    "Recognized text should not be null for edge case");
                Assertions.assertTrue(result.getLatency() >= 0,
                    "Latency should be non-negative for edge case");

                log.info("Edge case handled - Category: {}, Expected: '{}', Recognized: '{}', Accuracy: {:.2f}",
                    testCase.getCategory(), testCase.getExpectedText(), 
                    result.getRecognizedText(), result.getWordAccuracy());

            } catch (Exception e) {
                log.warn("Edge case failed as expected: {}", e.getMessage());
                // Some edge cases are expected to fail
            }
        }
    }

    /**
     * Setup test cases for different scenarios
     */
    private void setupTestCases() throws IOException {
        standardTestCases = createStandardTestCases();
        challengingTestCases = createChallengingTestCases();
        edgeCaseTestCases = createEdgeCaseTestCases();
    }

    /**
     * Create standard test cases with clear speech
     */
    private List<VoiceTestCase> createStandardTestCases() throws IOException {
        List<VoiceTestCase> testCases = new ArrayList<>();
        
        VoiceOptimizationSettings standardSettings = VoiceOptimizationSettings.builder()
            .recognitionSensitivity(0.8)
            .speechTimeout(5000L)
            .noiseReduction(false)
            .echoCancellation(false)
            .languageModel("general")
            .vocabularyExpansion(false)
            .streamingRecognition(false)
            .localProcessing(false)
            .build();

        // Create mock audio files for testing
        String[] testTexts = {
            "Hello, this is a test of the voice recognition system",
            "Please create a meeting for tomorrow at 2 PM",
            "Record a voice note about the project status",
            "What is the weather like today",
            "Set a reminder to call John at 5 o'clock"
        };

        for (String text : testTexts) {
            MockMultipartFile audioFile = createMockAudioFile(text, "clear");
            testCases.add(VoiceTestCase.builder()
                .audioFile(audioFile)
                .expectedText(text)
                .category("standard")
                .optimizationSettings(standardSettings)
                .build());
        }

        return testCases;
    }

    /**
     * Create challenging test cases with difficult conditions
     */
    private List<VoiceTestCase> createChallengingTestCases() throws IOException {
        List<VoiceTestCase> testCases = new ArrayList<>();
        
        VoiceOptimizationSettings challengingSettings = VoiceOptimizationSettings.builder()
            .recognitionSensitivity(0.7)
            .speechTimeout(8000L)
            .noiseReduction(true)
            .echoCancellation(true)
            .languageModel("enhanced-accuracy")
            .vocabularyExpansion(true)
            .streamingRecognition(false)
            .localProcessing(false)
            .build();

        String[] challengingTexts = {
            "The quick brown fox jumps over the lazy dog",
            "Supercalifragilisticexpialidocious is a very long word",
            "I need to schedule a meeting with the interdisciplinary team",
            "Please transcribe this message with background noise",
            "Fast speech recognition test with multiple technical terms"
        };

        String[] categories = {"tongue-twister", "long-words", "technical", "noisy", "fast-speech"};

        for (int i = 0; i < challengingTexts.length; i++) {
            MockMultipartFile audioFile = createMockAudioFile(challengingTexts[i], categories[i]);
            testCases.add(VoiceTestCase.builder()
                .audioFile(audioFile)
                .expectedText(challengingTexts[i])
                .category(categories[i])
                .optimizationSettings(challengingSettings)
                .build());
        }

        return testCases;
    }

    /**
     * Create edge case test cases
     */
    private List<VoiceTestCase> createEdgeCaseTestCases() throws IOException {
        List<VoiceTestCase> testCases = new ArrayList<>();
        
        VoiceOptimizationSettings edgeSettings = VoiceOptimizationSettings.builder()
            .recognitionSensitivity(0.9)
            .speechTimeout(10000L)
            .noiseReduction(true)
            .echoCancellation(true)
            .languageModel("general")
            .vocabularyExpansion(true)
            .streamingRecognition(true)
            .localProcessing(false)
            .build();

        String[] edgeTexts = {
            "", // Empty/silent audio
            "a", // Single character
            "Um, uh, well, you know, like, actually...", // Filler words
            "123 456 789 zero one two three", // Numbers and digits
            "Test with very very very long sentence that goes on and on without stopping for a very long time to test the limits of the system"
        };

        String[] edgeCategories = {"silent", "minimal", "filler-words", "numbers", "very-long"};

        for (int i = 0; i < edgeTexts.length; i++) {
            MockMultipartFile audioFile = createMockAudioFile(edgeTexts[i], edgeCategories[i]);
            testCases.add(VoiceTestCase.builder()
                .audioFile(audioFile)
                .expectedText(edgeTexts[i])
                .category(edgeCategories[i])
                .optimizationSettings(edgeSettings)
                .build());
        }

        return testCases;
    }

    /**
     * Create mock audio file for testing
     */
    private MockMultipartFile createMockAudioFile(String text, String category) {
        // Create a mock WAV file with minimal header
        byte[] mockAudioData = createMockWavData(text, category);
        return new MockMultipartFile(
            "audioFile",
            String.format("test_%s_%d.wav", category, text.hashCode()),
            "audio/wav",
            mockAudioData
        );
    }

    /**
     * Create mock WAV audio data
     */
    private byte[] createMockWavData(String text, String category) {
        // Create a minimal WAV file header + some mock audio data
        // This is a simplified mock - in real testing, you'd use actual audio files
        
        int dataSize = Math.max(1000, text.length() * 100); // Rough estimation
        int fileSize = 44 + dataSize; // WAV header is 44 bytes
        
        byte[] wavData = new byte[fileSize];
        
        // WAV header
        System.arraycopy("RIFF".getBytes(), 0, wavData, 0, 4);
        writeInt(wavData, 4, fileSize - 8);
        System.arraycopy("WAVE".getBytes(), 0, wavData, 8, 4);
        System.arraycopy("fmt ".getBytes(), 0, wavData, 12, 4);
        writeInt(wavData, 16, 16); // PCM format chunk size
        writeShort(wavData, 20, (short) 1); // PCM format
        writeShort(wavData, 22, (short) 1); // Mono
        writeInt(wavData, 24, 16000); // Sample rate
        writeInt(wavData, 28, 32000); // Byte rate
        writeShort(wavData, 32, (short) 2); // Block align
        writeShort(wavData, 34, (short) 16); // Bits per sample
        System.arraycopy("data".getBytes(), 0, wavData, 36, 4);
        writeInt(wavData, 40, dataSize);
        
        // Mock audio data (sine wave based on text content)
        for (int i = 44; i < fileSize; i += 2) {
            double frequency = 440.0 + (text.hashCode() % 200); // Vary frequency based on text
            double amplitude = 16000.0;
            double sample = amplitude * Math.sin(2.0 * Math.PI * frequency * (i - 44) / 32000.0);
            writeShort(wavData, i, (short) sample);
        }
        
        return wavData;
    }
    
    /**
     * Write integer to byte array (little endian)
     */
    private void writeInt(byte[] data, int offset, int value) {
        data[offset] = (byte) (value & 0xFF);
        data[offset + 1] = (byte) ((value >> 8) & 0xFF);
        data[offset + 2] = (byte) ((value >> 16) & 0xFF);
        data[offset + 3] = (byte) ((value >> 24) & 0xFF);
    }
    
    /**
     * Write short to byte array (little endian)
     */
    private void writeShort(byte[] data, int offset, short value) {
        data[offset] = (byte) (value & 0xFF);
        data[offset + 1] = (byte) ((value >> 8) & 0xFF);
    }

    /**
     * Get accuracy threshold for different categories
     */
    private double getCategoryThreshold(String category) {
        switch (category.toLowerCase()) {
            case "standard":
                return 0.90;
            case "technical":
                return 0.80;
            case "noisy":
                return 0.70;
            case "fast-speech":
                return 0.75;
            case "tongue-twister":
                return 0.60;
            case "long-words":
                return 0.75;
            case "numbers":
                return 0.85;
            case "filler-words":
                return 0.50;
            case "very-long":
                return 0.70;
            case "minimal":
                return 0.30;
            case "silent":
                return 0.10;
            default:
                return 0.80;
        }
    }

    /**
     * Performance test helper methods
     */
    @AfterEach
    void tearDown() {
        // Cleanup after each test
        log.info("Test completed, cleaning up resources");
    }

    @AfterAll
    static void tearDownAll() {
        log.info("All voice recognition tests completed");
    }
}
